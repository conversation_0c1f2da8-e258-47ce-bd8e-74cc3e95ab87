<resources>
    <!--
    TODO: Before you run your application, you need a Google Maps API key.

    To get one, follow this link, follow the directions and press "Create" at the end:

    https://console.developers.google.com/flows/enableapi?apiid=maps_android_backend&keyType=CLIENT_SIDE_ANDROID&r=7E:6F:04:B3:4B:F3:42:A0:C2:EE:9A:CF:E7:94:0B:1C:60:AE:1B:B8%3Bcom.project.bri.brimo.ui.activities

    You can also add your credentials to an existing key, using these values:

    Package name:
    7E:6F:04:B3:4B:F3:42:A0:C2:EE:9A:CF:E7:94:0B:1C:60:AE:1B:B8

    SHA-1 certificate fingerprint:
    4D:1F:E9:AC:A9:0A:95:C2:DE:7A:D5:2C:F1:03:4B:92:9A:DF:0E:AE

    Alternatively, follow the directions here:
    https://developers.google.com/maps/documentation/android/start#get-key

    Once you have your key (it starts with "AIza"), replace the "google_maps_key"
    string in this file.
    -->
    <string name="google_maps_key" templateMergeStrategy="preserve" translatable="false">AIzaSyDQmKSjujgpUh461g0_0P9Fsj-_Sqi-9xU</string>
</resources>
