package id.co.bri.brimo.presenters.britamarencanarevamp

import id.co.bri.brimo.contract.IPresenter.britamarencanarevamp.IBritamaJunioTabunganInquiryRevPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.britamarencanarevamp.IBritamaJunioTabunganInquiryRevView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimo.data.repository.category.CategoryPfmSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.config.AppConfig
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.BranchRequest
import id.co.bri.brimo.models.apimodel.request.juniorevamp.ConfirmationRequest
import id.co.bri.brimo.models.apimodel.response.BranchResponse
import id.co.bri.brimo.models.apimodel.response.GeneralConfirmationResponse
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.Schedulers
import java.util.concurrent.TimeUnit

class InquiryJunioRevPresenter<V>(schedulerProvider: SchedulerProvider,
                                  compositeDisposable: CompositeDisposable,
                                  mBRImoPrefRepository: BRImoPrefSource, apiSource: ApiSource,
                                  categoryPfmSource: CategoryPfmSource,
                                  transaksiPfmSource: TransaksiPfmSource,
                                  anggaranPfmSource: AnggaranPfmSource) : MvpPresenter<V>(
    schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource
),
    IBritamaJunioTabunganInquiryRevPresenter<V> where V : IMvpView, V : IBritamaJunioTabunganInquiryRevView {

    open var mUrl : String? = null
    open var generalConfirmationResponse = GeneralConfirmationResponse()
    open var mUrlKode : String? = null
    open var branchResponse = BranchResponse()


    override fun setUrlConfirmation(url: String) {
        mUrl = url
    }

    override fun getConfirmation(response: ConfirmationRequest) {
        if (isViewAttached) {
            //sho loading
            view.showProgress()

            val seqNum = brImoPrefRepository.seqNumber
            val listConnectableObservable = apiSource.getData(mUrl, response, seqNum).subscribeOn(
                schedulerProvider.io()
            )
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .observeOn(schedulerProvider.mainThread())
                .replay()
            compositeDisposable.add(listConnectableObservable
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(type: String) {
                        getView().hideProgress()
                        getView().onException(type)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView().hideProgress()
                        generalConfirmationResponse = response.getData(
                            GeneralConfirmationResponse::class.java
                        )

                        getView()!!.onSuccessConfirmation(generalConfirmationResponse)

                        if(!GeneralHelper.isProd()) {
                            GeneralHelper.responseChuck(response)
                        }
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView().hideProgress()
                        if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value, ignoreCase = true)) {
                            getView().onSessionEnd(restResponse.desc)
                        } else {
                            getView().onException(restResponse.desc)
                        }
                    }

                    override fun onComplete() {
                        super.onComplete()
                    }
                })
            )
            listConnectableObservable.connect()
        }
    }

    override fun setUrlPayment(urlPayment: String) {
        view.getUrlPayment(urlPayment)
    }

    override fun start() {
        getDefaultSaldo()
        super.start()
    }

    /**
     * Method yang digunakan untuk Set Default Saldo
     */
    fun getDefaultSaldo() {
        var saldo = 0.0
        val saldoText = brImoPrefRepository.saldoRekeningUtama
        if (saldoText != "") {
            saldo = java.lang.Double.valueOf(saldoText)
        }
        val defaultAcc = brImoPrefRepository.accountDefault
        val saldoString = brImoPrefRepository.saldoRekeningUtamaString
        val saldoHold = brImoPrefRepository.saldoHold
        view.setDefaultSaldo(saldo, saldoString, defaultAcc,saldoHold)
    }

    override fun getBranchCode(request: BranchRequest) {
        if (isViewAttached) {
            view.showProgress()

            val seqNum = brImoPrefRepository.seqNumber

            val listConnectableObservable = apiSource.getData(mUrlKode, request, seqNum).subscribeOn(
                schedulerProvider.io()
            )
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .observeOn(schedulerProvider.mainThread())
                .replay()
            compositeDisposable.add(listConnectableObservable
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(type: String) {
                        getView().hideProgress()
                        getView().onException(type)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView().hideProgress()
                        branchResponse = response.getData(
                            BranchResponse::class.java
                        )

                        getView().onSuccessBranch(branchResponse)

                        if(!GeneralHelper.isProd()) {
                            GeneralHelper.responseChuck(response)
                        }
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView().hideProgress()
                        if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value, ignoreCase = true)) {
                            getView().onSessionEnd(restResponse.desc)
                        } else {
                            getView().onException(restResponse.desc)
                        }
                    }

                    override fun onComplete() {
                        super.onComplete()
                    }
                })
            )
            listConnectableObservable.connect()
        }
    }

    override fun setUrlPilihKode(url: String) {
        mUrlKode=url
    }

}