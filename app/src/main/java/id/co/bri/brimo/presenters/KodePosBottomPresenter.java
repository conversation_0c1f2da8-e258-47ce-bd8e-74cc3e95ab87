package id.co.bri.brimo.presenters;

import java.util.concurrent.TimeUnit;

import id.co.bri.brimo.contract.IPresenter.IKodePosBottomPresenter;
import id.co.bri.brimo.contract.IView.IKodePosBottomView;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.config.AppConfig;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.apimodel.request.KodePosRequest;
import id.co.bri.brimo.models.apimodel.response.KodePosBottomResponse;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.schedulers.Schedulers;

public class KodePosBottomPresenter<V extends IMvpView & IKodePosBottomView>
        extends MvpPresenter<V> implements IKodePosBottomPresenter<V> {

    protected String url;

    public KodePosBottomPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void setUrl(String url) {
        this.url = url;
    }

    @Override
    public void onSendKodePos(KodePosRequest kodePosRequest) {
        if (url != null && isViewAttached()) {

            String seqNum = getBRImoPrefRepository().getSeqNumber();

            getCompositeDisposable().add(
                    getApiSource().getData(url, kodePosRequest, seqNum)
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .subscribeOn(Schedulers.io())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribeWith(new ApiObserver(getView(), seqNum) {

                                @Override
                                protected void onFailureHttp(String errorMessage) {
                                    getView().onException(errorMessage);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SUCCESS.getValue())) {
                                        KodePosBottomResponse kodePosResponse = response.getData(KodePosBottomResponse.class);
                                        getView().onSuccessData(kodePosResponse);
                                    } else getView().notFoundData();
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                        getView().onSessionEnd(restResponse.getDesc());
                                    else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_12.getValue()))
                                        getView().notFoundData();
                                    else
                                        getView().onException(restResponse.getDesc());
                                }
                            })
            );
        }
    }
}