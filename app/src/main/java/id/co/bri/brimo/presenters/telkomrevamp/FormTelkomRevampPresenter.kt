package id.co.bri.brimo.presenters.telkomrevamp

import id.co.bri.brimo.contract.IPresenter.telkomrevamp.IFormTelkomRevampPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.base.IBaseFormRevampView
import id.co.bri.brimo.contract.IView.telkomrevamp.IFormTelkomRevampView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.config.AppConfig
import id.co.bri.brimo.domain.config.enumconfig.JourneyType
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.InquiryTelkomRequest
import id.co.bri.brimo.models.apimodel.request.UpdateSavedRequest
import id.co.bri.brimo.models.apimodel.response.InquiryBrivaRevampResponse
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.SavedResponse
import id.co.bri.brimo.presenters.base.BaseFormRevampPresenter
import io.reactivex.disposables.CompositeDisposable
import java.util.concurrent.TimeUnit

class FormTelkomRevampPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBrimoPrefRepository: BRImoPrefSource,
    apiSource: ApiSource,
    transaksiPfmSource: TransaksiPfmSource,
) : BaseFormRevampPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    mBrimoPrefRepository,
    apiSource,
    transaksiPfmSource
),
    IFormTelkomRevampPresenter<V> where V : IMvpView, V : IBaseFormRevampView, V : IFormTelkomRevampView {
    override fun setUpdateItem(
        url: String,
        savedResponse: SavedResponse,
        position: Int,
        type: Int,
        journeyType: JourneyType?,
    ) {
        if (url.isEmpty() || !isViewAttached || onLoad) return
        onLoad = true
        val str1 =
            savedResponse.value.split("\\|".toRegex()).dropLastWhile { it.isEmpty() }.toTypedArray()
        val saveId = str1[0]
        val updateSavedRequest = UpdateSavedRequest(saveId, "")
        val seqNum = brImoPrefRepository.seqNumber
        view.showProgress()

        compositeDisposable.add(
            apiSource.getData(url, updateSavedRequest, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        onLoad = false
                        view.hideProgress()
                        view.onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        onLoad = false
                        view.hideProgress()
                        getView().onSuccessUpdate(savedResponse, position, type)
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        onApiError(restResponse)
                    }
                })
        )
    }

    override fun getDataInquiry(telkomNum: String) {
        if (mUrlInquiry.isEmpty() || !isViewAttached) return
        view.showProgress()
        val seqNum = brImoPrefRepository.seqNumber
        val inquiryRequest = InquiryTelkomRequest(telkomNum)
        compositeDisposable.add(
            apiSource.getData(mUrlInquiry, inquiryRequest, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        view.hideProgress()
                        view.onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        view.hideProgress()
                        val responseBriva = response.getData(InquiryBrivaRevampResponse::class.java)
                        getView().onSuccessGetInquiry(responseBriva, mUrlPayment)
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        view.hideProgress()
                        when (restResponse.code) {
                            RestResponse.ResponseCodeEnum.RC_88_BILL_ALREADY_PAID.value -> getView().onBillingAlreadyPaid(restResponse.desc)
                            else -> getView().onException(restResponse.desc)
                        }
                    }
                })
        )
    }
}