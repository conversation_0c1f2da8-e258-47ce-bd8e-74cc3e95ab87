package id.co.bri.brimo.presenters.ssc;


import java.util.concurrent.TimeUnit;
import id.co.bri.brimo.contract.IPresenter.ssc.IListComplainPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.ssc.IListComplainView;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.config.AppConfig;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.apimodel.request.DetailStatusRequest;
import id.co.bri.brimo.models.apimodel.request.StatusKartuRequest;
import id.co.bri.brimo.models.apimodel.response.ssc.ComplaintDetailStatusRes;
import id.co.bri.brimo.models.apimodel.response.ssc.ListComplainResponse;
import id.co.bri.brimo.models.apimodel.response.ListRekeningResponse;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.presenters.MvpPresenter;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.schedulers.Schedulers;

public class ListComplainPresenter<V extends IMvpView & IListComplainView> extends MvpPresenter<V> implements IListComplainPresenter<V> {

    protected String urlRekening;
    protected String urlStatusDefault;
    protected String urlStatus;
    protected String urlDetailStatus;

    public ListComplainPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void urlRekening(String urlRekening) {
        this.urlRekening = urlRekening;
    }

    @Override
    public void urlStatusComplainDefault(String urlStatusDefault) {
        this.urlStatusDefault = urlStatusDefault;
    }

    @Override
    public void urlStatusComplain(String urlStatus) {
        this.urlStatus = urlStatus;
    }

    @Override
    public void urlDetailStatus(String urlDetailStatus) {
        this.urlDetailStatus = urlDetailStatus;
    }

    @Override
    public void getDataRekening() {
        if (isViewAttached()) {

            String seq = getBRImoPrefRepository().getSeqNumber();

            getCompositeDisposable().add(
                    getApiSource().getDataTanpaRequest(urlRekening, seq)
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .subscribeOn(Schedulers.single())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribeWith(new ApiObserver(getView(),seq) {

                                @Override
                                protected void onFailureHttp(String errorMessage) {
                                    getView().onException(errorMessage);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    //TO-DO onSuccess
                                    ListRekeningResponse listRekeningResponse = response.getData(ListRekeningResponse.class);
                                    getView().onSuccessGetAccount(listRekeningResponse.getAccount());
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                        getView().onSessionEnd(restResponse.getDesc());
                                    else
                                        getView().onExceptionErrorGetAccount(restResponse.getDesc());
                                }
                            })
            );
        }
    }

    @Override
    public void sendDataStatusComplainDefault(StatusKartuRequest request) {
        if (isViewAttached()) {

            String seq = getBRImoPrefRepository().getSeqNumber();

            getCompositeDisposable().add(
                    getApiSource().getData(urlStatusDefault, request, seq)
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .subscribeOn(Schedulers.single())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribeWith(new ApiObserver(getView(),seq) {

                                @Override
                                protected void onFailureHttp(String errorMessage) {
                                    getView().hideProgress();
                                    getView().onException(errorMessage);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    //TO-DO onSuccess
                                    getView().hideProgress();
                                    if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SUCCESS.getValue())) {
                                        ListComplainResponse complainResponse = response.getData(ListComplainResponse.class);
                                        getView().onSuccessGetStatusComplaint(complainResponse);
                                    } else if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_01.getValue())) {
                                        getView().onDataNotFound();
                                    }
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    onApiError(restResponse);
                                }
                            })
            );
        }
    }

    @Override
    public void sendDataStatusComplain(StatusKartuRequest request) {
        if (isViewAttached()) {

            String seq = getBRImoPrefRepository().getSeqNumber();

            getView().showProgress();
            getCompositeDisposable().add(
                    getApiSource().getData(urlStatus, request, seq)
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .subscribeOn(Schedulers.single())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribeWith(new ApiObserver(getView(),seq) {

                                @Override
                                protected void onFailureHttp(String errorMessage) {
                                    getView().hideProgress();
                                    getView().onException(errorMessage);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    //TO-DO onSuccess
                                    getView().hideProgress();
                                    if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SUCCESS.getValue())) {
                                        ListComplainResponse complainResponse = response.getData(ListComplainResponse.class);
                                        getView().onSuccessGetStatusComplaint(complainResponse);
                                    } else if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_01.getValue())) {
                                        getView().onDataNotFound();
                                    }

                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    onApiError(restResponse);
                                }
                            })
            );
        }
    }

    @Override
    public void sendDataDetailStatusComplain(DetailStatusRequest detailStatusRequest, String title) {
        if (isViewAttached()) {

            String seq = getBRImoPrefRepository().getSeqNumber();

            getView().showProgress();
            getCompositeDisposable().add(
                    getApiSource().getData(urlDetailStatus, detailStatusRequest, seq)
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .subscribeOn(Schedulers.single())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribeWith(new ApiObserver(getView(),seq) {

                                @Override
                                protected void onFailureHttp(String errorMessage) {
                                    getView().hideProgress();
                                    getView().onException(errorMessage);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    //TO-DO onSuccess
                                    getView().hideProgress();
                                    ComplaintDetailStatusRes detailStatusRes = response.getData(ComplaintDetailStatusRes.class);
                                    getView().onSuccessDetailStatusComplaint(detailStatusRes, title);
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    onApiError(restResponse);
                                }
                            })
            );
        }
    }
}