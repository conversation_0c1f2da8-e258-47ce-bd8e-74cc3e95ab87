package id.co.bri.brimo.presenters.base;

import android.util.Log;

import id.co.bri.brimo.contract.IPresenter.base.IBaseFormPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.base.IBaseFormNosavedView;
import id.co.bri.brimo.contract.IView.base.IBaseFormView;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.config.AppConfig;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.apimodel.request.UpdateSavedRequest;
import id.co.bri.brimo.models.apimodel.response.GeneralFormResponse;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.models.apimodel.response.SavedResponse;

import java.util.concurrent.TimeUnit;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.schedulers.Schedulers;

public class BaseFormPresenter<V extends IMvpView & IBaseFormNosavedView & IBaseFormView> extends BaseFormNosavedPresenter<V> implements IBaseFormPresenter<V> {

    private static final String TAG = "BaseFormPresenter";

    private String saveId = "";
    private String purchaseType = "";

    protected GeneralFormResponse generalFormResponse;

    public BaseFormPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, categoryPfmSource, transaksiPfmSource, anggaranPfmSource);
    }


    /**
     * @param url
     * @param savedResponse
     * @param position
     * @param type
     */
    @Override
    public void setUpdateItem(String url, SavedResponse savedResponse, int position, int type) {
        if (url == null || !isViewAttached()) {
            return;
        }
        String s = savedResponse.getValue();
        String[] str1 = s.split("\\|");

        if(str1.length > 1){
            saveId = str1[0];
            purchaseType = str1[1];

        }else{
            saveId = str1[0];
        }

        UpdateSavedRequest updateSavedRequest = new UpdateSavedRequest(saveId,purchaseType);

        String seqNum = getBRImoPrefRepository().getSeqNumber();

        getCompositeDisposable().add(getApiSource().getData(url, updateSavedRequest, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(new ApiObserver(getView(),seqNum) {

                    @Override
                    protected void onFailureHttp(String errorMessage) {
                        getView().onException(errorMessage);
                    }

                    @Override
                    protected void onApiCallSuccess(RestResponse response) {
                        //TO-DO onSuccess
                        getView().hideProgress();
                        getView().onSuccessUpdate(savedResponse, position, type);

                    }

                    @Override
                    protected void onApiCallError(RestResponse restResponse) {
                        onApiError(restResponse);
                    }
                }));

    }

    /*
     * method untuk memproses restResponse jika berhasil mendapatkan balikan dari getDataForm() dan getDataFormFastMenu()
     */
    @Override
    public void onApiSuccess(RestResponse response) {
        super.onApiSuccess(response);
        if(!isViewAttached())
            return;

        generalFormResponse = response.getData(GeneralFormResponse.class);

        getView().onSuccessGetSavedForm(generalFormResponse.getSaved());
    }


}
