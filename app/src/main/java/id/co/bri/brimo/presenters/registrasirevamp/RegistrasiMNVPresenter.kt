package id.co.bri.brimo.presenters.registrasirevamp

import android.content.Context
import android.net.ConnectivityManager
import android.net.Network
import android.net.NetworkCapabilities
import android.net.NetworkRequest
import android.net.Uri
import android.os.Build
import android.util.Log
import com.chuckerteam.chucker.api.ChuckerInterceptor
import id.co.bri.brimo.R
import id.co.bri.brimo.contract.IPresenter.registrasirevamp.IRegistrasiMNVPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.registrasirevamp.IRegistrasiMNVView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimo.data.repository.category.CategoryPfmSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.NetworkDns
import id.co.bri.brimo.domain.config.AppConfig
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.config.Constant.ResponseMNV
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.mnv.CheckMNVReq
import id.co.bri.brimo.models.apimodel.request.mnv.HistoryCallback
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.registrasi.RegisEmailResponse
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.disposables.Disposable
import okhttp3.OkHttpClient
import okhttp3.Request
import java.io.IOException
import java.net.SocketTimeoutException
import java.util.Objects
import java.util.concurrent.TimeUnit

class RegistrasiMNVPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBRImoPrefRepository: BRImoPrefSource,
    apiSource: ApiSource,
    transaksiPfmSource: TransaksiPfmSource
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    mBRImoPrefRepository,
    apiSource,
    transaksiPfmSource
), IRegistrasiMNVPresenter<V> where V : IMvpView, V : IRegistrasiMNVView {

    private var urlCheckMNV = ""
    private var tempUrlMNV = ""

    private var mnvHitMaxRetry = 0
    private var mnvHitCounter = 1

    lateinit var connectivityManager: ConnectivityManager
    lateinit var networkRequest: NetworkRequest

    private val historyCallbacks = ArrayList<HistoryCallback>()

    private val networkCallback: ConnectivityManager.NetworkCallback by lazy {
        object : ConnectivityManager.NetworkCallback() {
            override fun onAvailable(network: Network) {
                networkDnsMnv(network, tempUrlMNV)
            }

            override fun onUnavailable() {
                view.showDialogCheckDataCellular()
            }
        }
    }

    override fun setMaxRetry(mnvMaxRetry: Int) {
        mnvHitMaxRetry = mnvMaxRetry
    }

    override fun setUrlCheckMNV(urlCheckMNV: String) {
        this.urlCheckMNV = urlCheckMNV
    }

    override fun setConnectionMnv(
        ctx: Context,
        url: String
    ) {
        tempUrlMNV = url
        registerNetworkCallback(ctx)

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            connectivityManager.requestNetwork(networkRequest, networkCallback, 5000)
        } else {
            connectivityManager.requestNetwork(networkRequest, networkCallback)
        }
    }

    private fun networkDnsMnv(
        network: Network,
        url: String
    ) {

        val dns = NetworkDns.getInstance()
        dns.setNetwork(network)

        val chuckerInterceptor: ChuckerInterceptor = ChuckerInterceptor.Builder(view as Context)
            .build()

        val httpBuilder: OkHttpClient.Builder = OkHttpClient.Builder()
            .socketFactory(network.socketFactory)
            .dns(dns)
            .followRedirects(false)
            .followSslRedirects(false)
            .connectTimeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
            .readTimeout(AppConfig.TIMEOUT_READ.toLong(), TimeUnit.SECONDS)

        if (!GeneralHelper.isProd()) {
            httpBuilder.addInterceptor(chuckerInterceptor)
        }

        val okHttpClient: OkHttpClient = httpBuilder.build()
        val mRequestBuilder: Request.Builder = Request.Builder().url(url)

        try {
            val response = okHttpClient.newCall(mRequestBuilder.build()).execute()
            when (response.code) {
                ResponseMNV.RC302.code,
                ResponseMNV.RC303.code -> {
                    val responseHeader = response.header(Constant.LOCATION_MNV)
                    if (responseHeader != null) {
                        historyCallbacks.add(
                            HistoryCallback(
                                mnvHitCounter,
                                tempUrlMNV,
                                responseHeader,
                                response.code.toString()
                            )
                        )
                        if ((responseHeader.contains(Constant.STATE_MNV) &&
                                    responseHeader.contains(Constant.CODE_MNV) &&
                                    getStateFromUrl(responseHeader)!!.length < Constant.MAX_LENGHT_URL &&
                                    responseHeader.contains(Constant.TELKOMSEL_MNV)) ||
                            responseHeader.contains(Constant.ERROR_MNV)
                        )
                            view.onSuccessAuthMnv(
                                responseHeader,
                                response.code.toString(),
                                mnvHitCounter,
                                historyCallbacks
                            )
                        else checkMnvHitCounter(
                            responseHeader,
                            response.code.toString(),
                            historyCallbacks
                        )
                    } else {
                        view.showDialogCellularNotMatch(GeneralHelper.getString(R.string.nomor_tidak_sesuai))
                    }
                }

                ResponseMNV.RC400.code -> {
                    historyCallbacks.add(
                        HistoryCallback(
                            mnvHitCounter,
                            tempUrlMNV,
                            tempUrlMNV,
                            response.code.toString()
                        )
                    )
                    view.onSuccessAuthMnv(
                        tempUrlMNV,
                        response.code.toString(),
                        mnvHitCounter,
                        historyCallbacks
                    )
                }

                else -> view.onException(Constant.SERVER_UNDER_MAINTENANCE)
            }
        } catch (e: SocketTimeoutException) {
            view.showDialogCheckDataCellular()
        } catch (e: IOException) {
            if (!GeneralHelper.isProd()) {
                Log.e("networkDnsMnv: ", e.message!!)
            }

            if (Objects.requireNonNull<String?>(e.message)
                    .contains(GeneralHelper.getString(R.string.eperm))
            )
                view.showDialogCheckVpn()
            else view.onException(Constant.SERVER_UNDER_MAINTENANCE)
        }
    }

    private fun getStateFromUrl(url: String): String? {
        val uri = Uri.parse(url)
        return uri.getQueryParameter(Constant.STATE_MNV)
    }

    private fun checkMnvHitCounter(
        responseHeader: String,
        codeStatus: String,
        historyCallbacks: ArrayList<HistoryCallback>
    ) {
        mnvHitCounter += 1
        if (mnvHitCounter <= mnvHitMaxRetry) {
            setConnectionMnv(view as Context, responseHeader)
        } else {
            view.onSuccessAuthMnv(
                responseHeader,
                codeStatus,
                mnvHitCounter,
                historyCallbacks
            )
        }
    }

    override fun onCheckMNV(request: CheckMNVReq) {
        if (urlCheckMNV.isEmpty() || !isViewAttached) return

        val seqNum = brImoPrefRepository.seqNumber

        val disposable: Disposable = apiSource.getData(urlCheckMNV, request, seqNum)
            .subscribeOn(schedulerProvider.single())
            .observeOn(schedulerProvider.mainThread())
            .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
            .subscribeWith(object : ApiObserver(view, seqNum) {
                override fun onFailureHttp(errorMessage: String) {
                    getView().onException(errorMessage)
                }

                override fun onApiCallSuccess(response: RestResponse) {
                    val regisEmailResponse =
                        response.getData(RegisEmailResponse::class.java)
                    getView().onSuccessMNV(regisEmailResponse)
                }

                override fun onApiCallError(restResponse: RestResponse) {
                    if (restResponse.code == RestResponse.ResponseCodeEnum.RC_58.value)
                        getView().showDialogCellularNotMatch(restResponse.desc)
                    else getView().onException(restResponse.desc)
                }
            })

        compositeDisposable.add(disposable)
    }

    override fun registerNetworkCallback(context: Context) {
        connectivityManager =
            context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        networkRequest = NetworkRequest.Builder()
            .addTransportType(NetworkCapabilities.TRANSPORT_CELLULAR)
            .addCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
            .build()
    }

    override fun unregisterNetworkCallback() {
        connectivityManager.unregisterNetworkCallback(networkCallback)
    }
}