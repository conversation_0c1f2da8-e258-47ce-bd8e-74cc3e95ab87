package id.co.bri.brimo.presenters.britamarencana;

import java.util.concurrent.TimeUnit;

import id.co.bri.brimo.contract.IPresenter.britamarencana.IHitungRencanaPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.britamarencana.IHitungRencanaView;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.config.AppConfig;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.apimodel.request.InquiryEdiRequest;
import id.co.bri.brimo.models.apimodel.request.InquiryOpenRequest;
import id.co.bri.brimo.models.apimodel.response.FormBukaRencanaResponse;
import id.co.bri.brimo.models.apimodel.response.InquiryEditRencanaResponse;
import id.co.bri.brimo.models.apimodel.response.InquiryOpenRencanaResponse;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.presenters.MvpPresenter;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.schedulers.Schedulers;

public class HitungRencanaPresenter<V extends IMvpView & IHitungRencanaView> extends MvpPresenter<V>
        implements IHitungRencanaPresenter<V> {

    String url, urlInquiry;
    public HitungRencanaPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void setUrl(String url) {
        this.url = url;
    }

    @Override
    public void setUrlInquiry(String url) {
        this.urlInquiry = url;
    }

    @Override
    public void getFormBukaRencana() {
        getView().showProgress();
        if (url == null || !isViewAttached()) {
            return;
        }
        String seqNum = getBRImoPrefRepository().getSeqNumber();

        getCompositeDisposable().add(
                getApiSource().getDataTanpaRequest(url, seqNum)//function(param)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(getSchedulerProvider().single())
                        .observeOn(getSchedulerProvider().mainThread())
                        .subscribeWith(new ApiObserver(getView(),seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().hideProgress();
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                getView().hideProgress();
                                FormBukaRencanaResponse formBukaRencanaResponse = response.getData(FormBukaRencanaResponse.class);
                                getView().onSuccessGetFormRencana(formBukaRencanaResponse);

                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                if (restResponse.getCode().equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                    getView().onSessionEnd(restResponse.getDesc());
                                else
                                    getView().onException(restResponse.getDesc());
                            }
                        })
        );
    }

    @Override
    public void getInquiryOpenRencana(InquiryOpenRequest inquiryOpenRequest) {
        getView().showProgress();
        if (urlInquiry == null || !isViewAttached()) {
            return;
        }

        String seqNum = getBRImoPrefRepository().getSeqNumber();

        getCompositeDisposable().add(
                getApiSource().getData(urlInquiry, inquiryOpenRequest, seqNum)//function(param)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(Schedulers.single())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(new ApiObserver(getView(),seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().hideProgress();
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                getView().hideProgress();
                                InquiryOpenRencanaResponse inquiryOpenRencanaResponse = response.getData(InquiryOpenRencanaResponse.class);
                                getView().onSuccessInquiry(inquiryOpenRencanaResponse);
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                if (restResponse.getCode().equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                    getView().onSessionEnd(restResponse.getDesc());
                                else
                                    getView().onException(restResponse.getDesc());
                            }
                        })
        );
    }

    @Override
    public void getFormEditRencana() {
        getView().showProgress();
        if (url == null || !isViewAttached()) {
            return;
        }
        String seqNum = getBRImoPrefRepository().getSeqNumber();

        getCompositeDisposable().add(
                getApiSource().getDataTanpaRequest(url, seqNum)//function(param)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(Schedulers.single())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(new ApiObserver(getView(),seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().hideProgress();
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                getView().hideProgress();
                                FormBukaRencanaResponse formBukaRencanaResponse = response.getData(FormBukaRencanaResponse.class);
                                getView().onSuccessGetFormEditRencana(formBukaRencanaResponse);

                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                if (restResponse.getCode().equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                    getView().onSessionEnd(restResponse.getDesc());
                                else if (restResponse.getCode().equals(RestResponse.ResponseCodeEnum.RC_12.getValue()))
                                    getView().onException12();
                                else
                                    getView().onException(restResponse.getDesc());
                            }
                        })
        );
    }

    @Override
    public void getInquiryEditRencana(InquiryEdiRequest inquiryEdiRequest) {
        getView().showProgress();
        if (urlInquiry == null || !isViewAttached()) {
            return;
        }

        String seqNum = getBRImoPrefRepository().getSeqNumber();

        getCompositeDisposable().add(
                getApiSource().getData(urlInquiry, inquiryEdiRequest, seqNum)//function(param)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(Schedulers.single())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(new ApiObserver(getView(),seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().hideProgress();
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                getView().hideProgress();
                                InquiryEditRencanaResponse inquiryEditRencanaResponse = response.getData(InquiryEditRencanaResponse.class);
                                getView().onSuccessInquiryEdit(inquiryEditRencanaResponse);
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                if (restResponse.getCode().equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                    getView().onSessionEnd(restResponse.getDesc());
                                else
                                    getView().onException(restResponse.getDesc());
                            }
                        })
        );
    }
}