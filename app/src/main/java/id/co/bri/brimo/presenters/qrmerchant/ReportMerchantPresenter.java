package id.co.bri.brimo.presenters.qrmerchant;

import id.co.bri.brimo.contract.IPresenter.qrmerchant.IReportMerchantPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.qrmerchant.IReportMerchantView;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.presenters.MvpPresenter;

import io.reactivex.disposables.CompositeDisposable;

public class ReportMerchantPresenter  <V extends IMvpView & IReportMerchantView> extends MvpPresenter<V> implements IReportMerchantPresenter<V> {

    private static final String TAG = "ReportMerchantPresenter";

    protected String url;

    public ReportMerchantPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void getReportMerchant() {

    }

    @Override
    public void setUrl(String url) {

    }
}
