package id.co.bri.brimo.presenters.kartuKredit;

import android.util.Log;

import id.co.bri.brimo.contract.IPresenter.kartuKredit.ITambahKreditPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.kartuKredit.ITambahKreditView;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.config.AppConfig;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.apimodel.request.InquiryKreditRequest;
import id.co.bri.brimo.models.apimodel.response.GeneralInquiryResponse;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.presenters.MvpPresenter;

import java.util.concurrent.TimeUnit;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.schedulers.Schedulers;

public class TambahKreditPresenter<V extends IMvpView & ITambahKreditView> extends MvpPresenter<V> implements ITambahKreditPresenter<V> {

    private static final String TAG = "TambahKreditPresenter";

    protected String inquiryUrl;

    public TambahKreditPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    /**
     * @param request
     */
    @Override
    public void getDataInquiry(InquiryKreditRequest request) {
        if (inquiryUrl == null || !isViewAttached()) {
            Log.d(TAG, "getDataInquiry: view atau inquiry urlInformasi null");
            return;
        }

        String seqNum = getBRImoPrefRepository().getSeqNumber();
        getView().showProgress();
        getCompositeDisposable()
                .add(getApiSource().getData(inquiryUrl, request, seqNum)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(Schedulers.io())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(new ApiObserver(getView(),seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                //TO-DO onSuccess
                                getView().hideProgress();
                                GeneralInquiryResponse responKredit = response.getData(GeneralInquiryResponse.class);
                                getView().onSuccessGetInquiry(responKredit);
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                onApiError(restResponse);
                            }
                        }));
    }

    /**
     * @param url
     */
    @Override
    public void setInquiryUrl(String url) {
        inquiryUrl = url;
    }
}
