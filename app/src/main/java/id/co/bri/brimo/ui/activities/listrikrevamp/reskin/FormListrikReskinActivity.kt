package id.co.bri.brimo.ui.activities.listrikrevamp.reskin

import android.Manifest
import android.app.Activity
import android.content.Intent
import android.content.pm.PackageManager
import android.content.res.ColorStateList
import android.graphics.Rect
import android.os.Bundle
import android.text.Editable
import android.text.InputType
import android.text.TextWatcher
import android.view.MotionEvent
import android.view.View
import android.view.WindowManager
import android.widget.EditText
import androidx.camera.core.ExperimentalGetImage
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.ethanhua.skeleton.Skeleton
import com.ethanhua.skeleton.SkeletonScreen
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.HistoryAdapterNs
import id.co.bri.brimo.adapters.SavedAdapterNs
import id.co.bri.brimo.contract.IPresenter.listrikrevamp.reskin.IFormListrikReskinPresenter
import id.co.bri.brimo.contract.IPresenter.listrikrevamp.reskin.SavedListNs
import id.co.bri.brimo.contract.IView.listrikrevamp.reskin.IFormListrikReskinView
import id.co.bri.brimo.contract.IView.listrikrevamp.reskin.ListrikErrorCode
import id.co.bri.brimo.contract.IView.listrikrevamp.reskin.ListrikException
import id.co.bri.brimo.contract.IView.listrikrevamp.reskin.ListrikResult
import id.co.bri.brimo.data.api.observer.ResExceptionErr
import id.co.bri.brimo.databinding.ActivityFormListrikReskinBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.GeneralHelperNewSkin
import id.co.bri.brimo.models.AccountModel
import id.co.bri.brimo.models.apimodel.request.InquiryPlnRequest
import id.co.bri.brimo.models.apimodel.response.DataPlnResponse
import id.co.bri.brimo.models.apimodel.response.GeneralConfirmationResponse
import id.co.bri.brimo.models.apimodel.response.HistoryResponse
import id.co.bri.brimo.models.apimodel.response.InquiryBrivaRevampResponse
import id.co.bri.brimo.models.apimodel.response.PlnListResponse
import id.co.bri.brimo.models.apimodel.response.ReceiptRevampResponse
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.SavedResponse
import id.co.bri.brimo.models.apimodel.response.pulsarevamp.FormPulsaDataResponse
import id.co.bri.brimo.presenters.listrikrevamp.reskin.FavoriteType
import id.co.bri.brimo.ui.activities.base.NewSkinBaseActivity
import id.co.bri.brimo.ui.activities.listrikrevamp.reskin.event.ScanEvent
import id.co.bri.brimo.ui.activities.pulsadata.reskin.AddSavedPulsaDataActivity
import id.co.bri.brimo.ui.activities.pulsadata.reskin.ReqInquiry
import id.co.bri.brimo.ui.fragments.UpdateSavedItemNsFragment
import id.co.bri.brimo.ui.fragments.bottomsheet.HapusConfirmationBottomSheetFragment
import id.co.bri.brimo.ui.fragments.bottomsheet.OpenBottomSheetGeneralNewSkinFragment
import id.co.bri.brimo.ui.fragments.bottomsheet.listrik.BSFragmentListrikOption
import id.co.bri.brimo.util.RxBus
import id.co.bri.brimo.util.custom_numpad.CustomNumpadHelper
import id.co.bri.brimo.util.custom_numpad.NumpadType
import id.co.bri.brimo.util.parseFromJson
import io.reactivex.android.schedulers.AndroidSchedulers
import javax.inject.Inject
import kotlin.text.split
import kotlin.text.toInt

@ExperimentalGetImage
class FormListrikReskinActivity: NewSkinBaseActivity(), IFormListrikReskinView,
    View.OnClickListener, SavedAdapterNs.ClickItem, HistoryAdapterNs.ClickItem {
    private var _binding: ActivityFormListrikReskinBinding? = null
    protected val binding get() = _binding!!

    private lateinit var plnList: List<PlnListResponse>

    private var useApi = true

    private lateinit var resData: DataPlnResponse

    @Inject
    lateinit var presenter: IFormListrikReskinPresenter<IFormListrikReskinView>

    lateinit var historyAdapter: HistoryAdapterNs
    lateinit var savedAdapter: SavedAdapterNs

    protected var historyResponses: ArrayList<HistoryResponse> = ArrayList<HistoryResponse>()
    protected var savedResponses: ArrayList<SavedResponse> = ArrayList<SavedResponse>()

    private var isErrorAfterSubmit = false
    private var firstTimeSelectOption = false

    private var isFirstTimeOpenScreen = true

    // Current tab
    private var currentTab = TAB_FAVORIT

    private lateinit var numpadHelper: CustomNumpadHelper

    private lateinit var skeleton: SkeletonScreen

    companion object {
        const val TAG = "InquiryListrikReskinActivity"
        const val INPUT_MAX_LENGTH = 20
        private var currentOption: PlnListResponse? = null

        const val TAB_FAVORIT = 0
        const val TAB_RIWAYAT = 1

        @JvmStatic
        fun launchIntent(caller: Activity, fromFastMenu: Boolean) {
            isFromFastMenu = fromFastMenu
            caller.apply {
                startActivityForResult(Intent(
                    this,
                    FormListrikReskinActivity::class.java
                ), Constant.REQ_PAYMENT)
            }
        }
    }

    protected var activityTextListener: TextWatcher = object : TextWatcher {
        override fun beforeTextChanged(charSequence: CharSequence, i: Int, i1: Int, i2: Int) {
            // Override in child class if needed
        }

        override fun onTextChanged(charSequence: CharSequence, i: Int, i1: Int, i2: Int) {
            changeText(charSequence, i, i1, i2)
        }

        override fun afterTextChanged(editable: Editable) {
            // Override in child class if needed
            binding.bivNoPelanggan.removeAllEndIcons()
            if (editable.toString().isNotEmpty()) {
                binding.bivNoPelanggan.addEndIcon(R.drawable.ic_clear_ns, sizeDp = 24, marginDp = 5) {
                    binding.bivNoPelanggan.clearText()
                    binding.bivNoPelanggan.clearError()
                }
            }
            binding.bivNoPelanggan.addEndIcon(R.drawable.ic_scan_reskin) {
                checkPermissionAndOpenScanner()
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        _binding = ActivityFormListrikReskinBinding.inflate(layoutInflater)
        skeleton = Skeleton.bind(binding.bslContent.binding.llContent)
            .shimmer(true)
            .angle(20)
            .duration(1200)
            .load(R.layout.skeleton_form_listrik_reskin)
            .show()

        window.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_ALWAYS_HIDDEN)

        setContentView(binding.root)
        onBindIntentData()

        injectDependency()

        onBindView()

        RxBus.listen(ScanEvent::class.java)
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe { event ->
                binding.bivNoPelanggan.setText(event.resScan)
            }

        RxBus.listen(FavoriteEvent::class.java)
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe { event ->
                if(event.isAddFavorite) {
                    showSnackbar("Daftar Favorit berhasil ditambahkan.", ALERT_CONFIRM)
                    execForm()
                }
            }

        RxBus.listen(FormListrikEvent::class.java)
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe { event ->
                if(event.isReload) {
                    execForm()
                }
            }
    }

    private fun onBindView() {
        binding.bivNoPelanggan.attachNumpad(this, NumpadType.PHONE, { pin ->

        }, onAttached = { numpad ->
            numpadHelper = numpad
        })
        binding.bivNoPelanggan.isEnabled = currentOption!=null

        binding.bslContent.setTextToolbar(this, "Listrik")
        binding.llListrikType.isEnabled = false
        binding.btnSubmit.setOnClickListener(this)
        binding.etJenisListrik.setOnClickListener { showOptionListrik() }
        binding.llListrikType.setOnClickListener {
            showOptionListrik()
        }
        binding.bivNoPelanggan.apply {
            maxLength(INPUT_MAX_LENGTH)
            setInputType(InputType.TYPE_CLASS_TEXT)
            addTextChangedListener(activityTextListener)
            addEndIcon(R.drawable.ic_scan_reskin) {
                checkPermissionAndOpenScanner()
            }
        }
        binding.btnTokenCheck.setOnClickListener {
            CetakTokenReskinActivity.launchIntent(this@FormListrikReskinActivity, isFromFastMenu)
        }

        binding.llAddSavedList.apply {
            visibility = if(isFromFastMenu) View.GONE else View.VISIBLE
            setOnClickListener {
                AddSavedListrikActivity.launchIntent(this@FormListrikReskinActivity, isFromFastMenu, plnList.toMutableList(), savedResponses.toMutableList())
            }
        }

        setupAdapters()
        setupTabFunctionality()

        binding.searchviewBriva.setOnClickListener {
            SearchSavedHistoryListrikActivity.launchIntent(
                this,
                savedResponses,
                historyResponses,
                isFromFastMenu
            )
        }

//        Handler(Looper.getMainLooper()).postDelayed({
//            bindWithMock()
//        }, 3000)
    }

    private fun bindWithMock() {
        if(!useApi) {
            skeleton.hide()
            val data: DataPlnResponse = parseFromJson(this, "data/mockjson/listrik/formlistrik.json")

            historyResponses.apply {
                clear()
                addAll(data.history)
            }
            savedResponses.apply {
                clear()
                addAll(data.saved)
            }
            resData = data
            plnList = data.plnList

            savedAdapter.notifyDataSetChanged()
            historyAdapter.notifyDataSetChanged()

            updateEmptyStates()
            if(isFirstTimeOpenScreen) showOptionListrik()

            isFirstTimeOpenScreen = false
        }
    }

    private fun setupTabFunctionality() {
        // Set up tab click listeners
        if (binding.tabFavorit != null) {
            binding.tabFavorit.setOnClickListener { view -> switchToFavoritTab() }
        }

        if (binding.tabRiwayat != null) {
            binding.tabRiwayat.setOnClickListener { view -> switchToRiwayatTab() }
        }

        // Default to Favorit tab (without clearing search field on initial load)
        switchToFavoritTab()
    }

    private fun setupAdapters() {
        // Saved adapter
        initiateSavedAdapter()

        // History adapter
        initiateHistoryAdapter()
    }

    fun initiateHistoryAdapter() {
        binding.rvRiwayat.layoutManager = LinearLayoutManager(this, RecyclerView.VERTICAL, false)
        historyAdapter =
            HistoryAdapterNs(
                this,
                historyResponses,
                this,
                0,
                isFromFastMenu,
                "listrik"
            )
        binding.rvRiwayat.adapter = historyAdapter
    }

    fun initiateSavedAdapter() {
        binding.rvDaftarFavorit.layoutManager =
            LinearLayoutManager(this, RecyclerView.VERTICAL, false)
        savedAdapter =
            SavedAdapterNs(this, savedResponses, this, 0, isFromFastMenu)
        binding.rvDaftarFavorit.adapter = savedAdapter

    }

    private fun switchToFavoritTab() {
        currentTab = TAB_FAVORIT
        updateTabAppearance()
        showFavoritContent()
    }

    private fun switchToRiwayatTab() {
        currentTab = TAB_RIWAYAT
        updateTabAppearance()
        showRiwayatContent()
    }

    private fun updateTabAppearance() {
        // Reset all tabs
        binding.tabFavorit.setTextColor(ContextCompat.getColor(this, R.color.black))
        binding.tabFavorit.setBackgroundResource(R.drawable.rounded_button_neutral_ns)

        binding.tabRiwayat.setTextColor(ContextCompat.getColor(this, R.color.black))
        binding.tabRiwayat.setBackgroundResource(R.drawable.rounded_button_neutral_ns)

        // Set active tab
        when (currentTab) {
            TAB_FAVORIT -> {
                binding.tabFavorit.setTextColor(
                    ContextCompat.getColor(
                        this,
                        R.color.text_brand_primary_ns
                    )
                )
                binding.tabFavorit.setBackgroundResource(R.drawable.rounded_button_soft_ns)
            }

            TAB_RIWAYAT -> {
                binding.tabRiwayat.setTextColor(
                    ContextCompat.getColor(
                        this,
                        R.color.text_brand_primary_ns
                    )
                )
                binding.tabRiwayat.setBackgroundResource(R.drawable.rounded_button_soft_ns)
            }
        }
    }

    private fun showFavoritContent() {
        binding.contentFavorit.visibility = View.VISIBLE
        binding.contentRiwayat.visibility = View.GONE
    }

    private fun showRiwayatContent() {
        binding.contentFavorit.visibility = View.GONE
        binding.contentRiwayat.visibility = View.VISIBLE
    }

    private fun updateEmptyStates() {
        // Update Favorit tab empty state
        if (savedResponses.isEmpty()) {
            binding.rvDaftarFavorit.visibility = View.GONE
            binding.contentFavorit.visibility = View.VISIBLE
            binding.llNoDataSaved.visibility = View.VISIBLE
        } else {
            binding.rvDaftarFavorit.visibility = View.VISIBLE
            binding.llNoDataSaved.visibility = View.GONE
        }

        // Update Riwayat tab empty state
        if (historyResponses.isEmpty()) {
            binding.rvRiwayat.visibility = View.GONE
            binding.llNoHistory.visibility = View.VISIBLE
        } else {
            binding.rvRiwayat.visibility = View.VISIBLE
            binding.llNoHistory.visibility = View.GONE
        }
    }

    private fun checkPermissionAndOpenScanner() {
        val permissionCheck = ContextCompat.checkSelfPermission(this, Manifest.permission.CAMERA)
        if (permissionCheck != PackageManager.PERMISSION_GRANTED) {
            ActivityCompat.requestPermissions(
                this,
                arrayOf(Manifest.permission.CAMERA),
                Constant.REQUEST_CAMERA
            )
        } else {
            // Permission already granted
            ScannerReskinActivity.launchIntent(this, isFromFastMenu)
//            ScannerActivity.launchIntentScanner(this)
        }
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)

        if (requestCode == Constant.REQUEST_CAMERA) {
            if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                // Permission granted, now open the scanner
//                ScannerActivity.launchIntentScanner(this)
                ScannerReskinActivity.launchIntent(this, isFromFastMenu)
            } else {
                // Permission denied
                showAlertFinish(getString(R.string.notes_need_permission))
            }
        }
    }

    private fun injectDependency() {
        activityComponent.inject(this)

        presenter.apply {
            view = this@FormListrikReskinActivity
//            if(useApi) getDataForm()
            if(useApi) execForm()
            start()
        }
    }

    private fun execForm() {
        presenter.executeRequest(
            url = GeneralHelper.getString(R.string.url_form_pln_v3),
            requestParam = null,
            responseType = DataPlnResponse::class.java
        )
    }

    private fun execInquiry(param: InquiryPlnRequest) {
        showProgress()
        presenter.executeRequest(
            url = GeneralHelper.getString(R.string.url_inquiry_pln_v3),
            requestParam = param,
            responseType = InquiryBrivaRevampResponse::class.java
        )
    }

    private fun execFavorite(url: String, isSaved: FavoriteType, param: SavedListNs) {
        presenter.executeRequest(
            url = url,
            requestParam = param,
            responseType = RestResponse::class.java,
            isSaved = isSaved
        )
    }

    private fun showOptionListrik() {
        BSFragmentListrikOption(plnList) { _, item ->
            currentOption = item
            if(binding.bivNoPelanggan.isError) {
                checkValidation()
            }
            binding.bivNoPelanggan.isEnabled = currentOption!=null

            if(firstTimeSelectOption){
                isErrorAfterSubmit = !isValidationNoMeter()
            }

            binding.etJenisListrik.setText(currentOption?.name)

            //load icon transaction
            GeneralHelper.loadIconTransaction(
                this@FormListrikReskinActivity,
                currentOption?.iconPath,
                "",
                binding.ivArea,
                GeneralHelper.getImageId(this@FormListrikReskinActivity, "bri")
            )
            binding.iconContainer.visibility = View.VISIBLE
            binding.iconContainer.setBackgroundResource(R.drawable.bg_white_full_rounded_ns)
            binding.regionTextview.visibility = View.VISIBLE
            binding.ivArea.setPadding(0, 0, 0, 0)
        }.apply {
            show(supportFragmentManager, BSFragmentListrikOption.TAG)
        }
    }

    private fun onBindIntentData() {
    }

    override fun changeText(charSequence: CharSequence?, i: Int, i1: Int, i2: Int) {
        val isNotEmpty = binding.etJenisListrik.length() != 0
        checkValidation()

        if(isErrorAfterSubmit) {
            if(isValidationNoMeter()) {
                binding.bivNoPelanggan.clearError()
                isErrorAfterSubmit = false
            }
        }

        binding.bivNoPelanggan.removeAllEndIcons()
        if(isNotEmpty) {
            binding.btnSubmit.backgroundTintList = ColorStateList.valueOf(
                GeneralHelper.getColor(R.color.primary_default_ns)
            )
            binding.bivNoPelanggan.addEndIcon(R.drawable.ic_clear_ns, sizeDp = 24, marginDp = 5) {
                binding.bivNoPelanggan.clearText()
                binding.bivNoPelanggan.clearError()
            }
        } else {
            binding.btnSubmit.backgroundTintList = ColorStateList.valueOf(
                GeneralHelper.getColor(R.color.black_ns_300)
            )
        }
        binding.bivNoPelanggan.addEndIcon(R.drawable.ic_scan_reskin) {
            checkPermissionAndOpenScanner()
        }
    }

    override fun onClick(view: View?) {
        when (view?.id) {
            R.id.btnSubmit -> {
                execInquiry(param = InquiryPlnRequest(
                    currentOption?.code ?: "", binding.bivNoPelanggan.getText()
                ))
                numpadHelper.hideKeyboard()
                isErrorAfterSubmit = !isValidationNoMeter()
//                checkValidation()
            }
        }
    }

    override fun onClickSavedItem(savedResponse: SavedResponse?) {
        val item = savedResponse!!.value.mappingTo()

        execInquiry(param = InquiryPlnRequest(
            item!!.billingType, item!!.idPel
        ))

    }

    override fun onClickUpdateItem(
        savedResponse: SavedResponse?,
        position: Int
    ) {
        val updateSavedItemFragment = UpdateSavedItemNsFragment(savedResponse, {savedResponseItem, type, position ->
            savedResponseItem?.let { item ->
                val savedId = item.value.split("|")[0].toInt()
                val productId = item.value!!.split("|")[1]

                when (type) {
                    Constant.EditOptionNs.EDIT -> {
                        plnList.find { it.code == productId }?.let {
                            EditSavedListrikActivity.launchIntent(
                                this@FormListrikReskinActivity, savedResponses,
                                historyResponses, item, type = it ,isFromFastMenu
                            )
                        }
                    }
                    Constant.EditOptionNs.FAV,
                    Constant.EditOptionNs.NON_FAV,
                    Constant.EditOptionNs.HAPUS -> {
                        val (url, favType, withConfirmation) = when (type) {
                            Constant.EditOptionNs.FAV -> Triple(
                                GeneralHelper.getString(R.string.url_favorit_pln_v3),
                                FavoriteType.favorite,
                                false
                            )
                            Constant.EditOptionNs.NON_FAV -> Triple(
                                GeneralHelper.getString(R.string.url_unfavorit_pln_v3),
                                FavoriteType.unfavorite,
                                false
                            )
                            Constant.EditOptionNs.HAPUS -> Triple(
                                GeneralHelper.getString(R.string.url_delete_pln_v3),
                                FavoriteType.removeFavorite,
                                true
                            )
                            else -> return@let
                        }

                        if (withConfirmation) {
                            HapusConfirmationBottomSheetFragment.newInstance(
                                savedResponseItem = item,
                                onConfirm = {
                                    showProgress()
                                    execFavorite(url, favType, SavedListNs(
                                        savedId = savedId,
                                        productId = productId
                                    ))
                                },
                                onCancel = {}
                            ).show(supportFragmentManager, "HapusConfirmationBottomSheet")
                        } else {
                            showProgress()
                            execFavorite(url, favType, SavedListNs(
                                saveAs = item.title,
                                savedId = savedId,
                                productId = productId
                            ))
                        }
                    }
                }
            }

        }, position)
        updateSavedItemFragment.show(supportFragmentManager, "")
    }

    override fun onClickHistoryItem(historyResponse: HistoryResponse?) {
        val item = historyResponse!!.value.mappingTo()

        execInquiry(param = InquiryPlnRequest(
            item!!.billingType, item!!.idPel
        ))
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == Constant.REQ_PAYMENT) {
            if (resultCode == RESULT_OK) {
                this.setResult(RESULT_OK)
                finish()
            } else {
                this.setResult(RESULT_CANCELED, data)
            }
        }
    }

    fun isValidationNoMeter(): Boolean {
        val minLength = when (currentOption?.code) {
            "prepaid" -> 11
            "postpaid" -> 12
            "nontaglis" -> 13
            else -> 0
        }

        return binding.bivNoPelanggan.getText().length >= minLength &&
                binding.etJenisListrik.length() != 0
    }

    fun getOptionProductCode(code: String): Int {
        return when (code) {
            "prepaid" -> 11
            "postpaid" -> 12
            "nontaglis" -> 13
            else -> 0
        }
    }

    fun checkValidation() {
        binding.btnSubmit.isEnabled = isValidationNoMeter()

        if(!isValidationNoMeter())
            if(binding.bivNoPelanggan.isError) binding.bivNoPelanggan.setError(String.format(
                GeneralHelper.getString(R.string.text_minimal_input),
                getOptionProductCode(currentOption?.code?:"")
            ))
        else {
            binding.bivNoPelanggan.clearError()
//            presenter.postInquiry(InquiryPlnRequest(
//                currentOption?.code ?: "", binding.bivNoPelanggan.getText()
//            ))
        }
    }

    override fun dispatchTouchEvent(ev: MotionEvent?): Boolean {
        val view = currentFocus

        if (ev?.action == MotionEvent.ACTION_DOWN && view is EditText) {
            val outRect = Rect()
            view.getGlobalVisibleRect(outRect)

            val tappedOutsideEditText = !outRect.contains(ev.rawX.toInt(), ev.rawY.toInt())
            val tappedOutsideNumpad = !numpadHelper.isTouchInsideNumpad(ev)

            // ❗ hanya hide jika klik di luar EditText DAN di luar numpad
            if (tappedOutsideEditText && tappedOutsideNumpad) {
                view.clearFocus()
                numpadHelper.hideKeyboard()
            }
        }

        return super.dispatchTouchEvent(ev)
    }

    override fun onSuccessAccountList(
        accountList: MutableList<AccountModel>,
        mainAccount: AccountModel
    ) {

    }

    override fun onSuccess(res: ListrikResult) {
        when (res) {
            is ListrikResult.Form -> {
                skeleton.hide()
                hideProgress()
                val data = res.data

                historyResponses.apply {
                    clear()
                    addAll(data.history)
                }
                savedResponses.apply {
                    clear()
                    addAll(data.saved)
                }
                resData = data
                plnList = data.plnList

                savedAdapter.notifyDataSetChanged()
                historyAdapter.notifyDataSetChanged()

                updateEmptyStates()
                if(isFirstTimeOpenScreen) showOptionListrik()

                isFirstTimeOpenScreen = false
            }
            is ListrikResult.Inquiry -> {
                hideProgress()
                val code = currentOption?.code!!
                val data = res.data
                if(code.contains("prepaid"))
                    InquiryListrikReskinActivity.launchIntent(this, isFromFastMenu, data, InquiryPlnRequest(
                        currentOption?.code ?: "", binding.bivNoPelanggan.getText()
                    ))
                else ConfirmClosedBillListrikReskinActivity.launchIntent(this, isFromFastMenu,data)
            }
            is ListrikResult.Favorite -> {
                val type = res.data

                execForm()

                showSnackbar(when (type) {
                    FavoriteType.favorite -> "Daftar berhasil di Pin."
                    FavoriteType.removeFavorite -> "Daftar Favorit berhasil dihapus."
                    FavoriteType.unfavorite -> "Daftar Favorit berhasil diunpin."
                    else -> ""
                }, ALERT_CONFIRM)
            }
            else -> Unit
        }
    }

    override fun onExceptionReskin(exception: ListrikException) {
        when (exception) {
            is ListrikException.KnownError -> handlingError(exception.body)
            is ListrikException.UnknownError -> {
                OpenBottomSheetGeneralNewSkinFragment.showDialogConfirmation(
                    supportFragmentManager,
                    R.drawable.ic_sad_illustration,
                    "ic_sad_illustration",
                    "Terjadi Kesalahan",
                    "Kami mengalami kendala saat memuat data. Silakan coba beberapa saat lagi, ya.",
                    btnFirstFunction = {

                    },
                    btnSecondFunction = {

                    },
                    false,
                    firstBtnTxt = "Coba Lagi",
                    secondBtnTxt = "Tutup",
                    false,
                    showCloseButton = true,
                    showPill = true
                )
            }
        }
    }

    private fun handlingError(data: ResExceptionErr) {
        when (data.code) {
            ListrikErrorCode.EXCEPTION_12.code -> {
                if(GeneralHelperNewSkin.isContainsPartial(Constant.Electic.WRONG_IDPEL, data.desc)){
                    isErrorAfterSubmit = true
                    binding.bivNoPelanggan.apply{
                        clearError()
                        setError("Nomor tidak ditemukan. Coba cek lagi nomor kamu")
                    }
                } else if(data.desc.contains(Constant.Electic.NOT_AVAIL) || data.desc.contains(Constant.Electic.DONE_PAYMENT)) {
                    OpenBottomSheetGeneralNewSkinFragment.showDialogInformation(
                        fragmentManager = supportFragmentManager,
                        imgPath = "",
                        imgName = "ic_checklist_receipt_newskin",
                        titleTxt = "Tagihan Sudah Dibayar",
                        subTitleTxt = "Cek secara berkala untuk informasi tagihan berikutnya, ya.",
                        btnFirstFunction = { },
                        isClickableOutside = true,
                        firstBtnTxt = "Tutup",
                        showCloseButton = true
                    )
                } else {
                    OpenBottomSheetGeneralNewSkinFragment.showDialogConfirmation(
                        supportFragmentManager,
                        R.drawable.ic_sad_illustration,
                        "ic_sad_illustration",
                        "Terjadi Kesalahan",
                        "Kami mengalami kendala saat memuat data. Silakan coba beberapa saat lagi, ya.",
                        btnFirstFunction = {

                        },
                        btnSecondFunction = {

                        },
                        false,
                        firstBtnTxt = "Coba Lagi",
                        secondBtnTxt = "Tutup",
                        false,
                        showCloseButton = true,
                        showPill = true
                    )
                }
            }
            ListrikErrorCode.EXCEPTION_61.code -> {
                OpenBottomSheetGeneralNewSkinFragment.showDialogInformationDismiss(
                    fragmentManager = supportFragmentManager,
                    imgPath = "",
                    imgName = "ic_warning_illustration",
                    titleTxt = GeneralHelper.getString(R.string.txt_limit_harian_title),
                    subTitleTxt = GeneralHelper.getString(R.string.txt_limit_harian_desc),
                    btnFirstFunction = { /* Dismiss */ },
                    isClickableOutside = true,
                    firstBtnTxt = GeneralHelper.getString(R.string.mengerti),
                    showCloseButton = true
                )
            }
            RestResponse.ResponseCodeEnum.RC_FO.value -> {

            }
            else -> showSnackbar(data.desc, ALERT_ERROR)
        }
    }
}

fun String.mappingTo(): ItemElectric? {
    return try {
        val parts = this.split("|")

        when (parts.size) {
            2 -> ItemElectric(parts[0], parts[1])
            3 -> ItemElectric(parts[0], parts[1])
            4 -> ItemElectric(parts[1], parts[2])
            else -> throw IllegalArgumentException("Format tidak dikenali")
        }
    } catch (e: Exception) {
        println("Gagal parsing: ${e.message}")
        null
    }
}

data class ItemElectric(
    val billingType: String, val idPel: String
)

data class FavoriteEvent(
    val isAddFavorite: Boolean = false,
    val isEdit: Boolean = false,
)

data class FormListrikEvent(
    val isReload: Boolean
)

sealed class FavoriteEventAction(val message: String) {
    object Add : FavoriteEventAction("Daftar Favorit berhasil ditambahkan.")
    object Edit : FavoriteEventAction("Nama berhasil diubah.")
}