package id.co.bri.brimo.presenters.lifestyle.shopping

import id.co.bri.brimo.contract.IPresenter.lifestyle.shopping.IWebviewShoppingPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.lifestyle.shopping.IWebviewShoppingView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimo.data.repository.category.CategoryPfmSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.config.AppConfig

import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.RevokeSessionRequest
import id.co.bri.brimo.models.apimodel.request.lifestyle.shopping.ConfirmationMobelanjaRequest
import id.co.bri.brimo.models.apimodel.response.ExceptionResponse
import id.co.bri.brimo.models.apimodel.response.InquiryBrivaRevampResponse
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.disposables.CompositeDisposable
import java.util.concurrent.TimeUnit

class WebviewShoppingPresenter<V>(
    schedulerProvider: SchedulerProvider?,
    compositeDisposable: CompositeDisposable?,
    mBRImoPrefRepository: BRImoPrefSource?,
    apiSource: ApiSource?,
    categoryPfmSource: CategoryPfmSource?,
    transaksiPfmSource: TransaksiPfmSource?,
    anggaranPfmSource: AnggaranPfmSource?
) : MvpPresenter<V>(
    schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource
), IWebviewShoppingPresenter<V> where V : IMvpView?, V : IWebviewShoppingView {

    lateinit var revokeRequest: Any

    lateinit var urlRevokeSessionMobelanja: String

    lateinit var urlConfirmMobelanja: String

    lateinit var confirmMobelanjaRequest: Any

    lateinit var urlPayMobelanja: String

    override fun setUrlRevokeSession(urlRevokeSession: String) {
        urlRevokeSessionMobelanja = urlRevokeSession
    }

    override fun setUrlConfirmationMobelanja(urlConfirmationMobelanja: String) {
        urlConfirmMobelanja = urlConfirmationMobelanja
    }

    override fun setUrlPaymentMobelanja(urlPaymentMobelanja: String) {
        urlPayMobelanja = urlPaymentMobelanja
    }

    override fun postPauseTime(time: Long) {
        brImoPrefRepository.savePauseTime(time)
    }

    override fun fetchPauseTime(): Long {
        return brImoPrefRepository.pauseTime
    }

    override fun getRevokeSession(sessionId: String, isInquiry: Boolean) {
        view?.showProgress()

        if (urlRevokeSessionMobelanja.isEmpty() || !isViewAttached) {
            return
        }

        val seqNum = brImoPrefRepository.seqNumber
        val username = brImoPrefRepository.username
        val tokenKey = brImoPrefRepository.tokenKey

        revokeRequest = RevokeSessionRequest(username, tokenKey, sessionId)

        compositeDisposable.add(
            apiSource.getData(urlRevokeSessionMobelanja, revokeRequest, seqNum) //function(param)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(schedulerProvider.single())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView()?.hideProgress()
                        getView()?.onException(errorMessage)
                        onLoad = false
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        onLoad = false
                        getView()?.hideProgress()
                        getView()?.onSuccessRevokeSession(isInquiry)
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        onLoad = false
                        getView()?.hideProgress()
                        getView()?.onSuccessRevokeSession(isInquiry)
                    }
                })
        )
    }

    override fun getConfirmationMobelanja(orderNumber: String) {
        view?.showProgress()

        if (urlConfirmMobelanja.isEmpty() || !isViewAttached) return

        val seqNum = brImoPrefRepository.seqNumber

        confirmMobelanjaRequest = ConfirmationMobelanjaRequest(orderNumber)

        compositeDisposable.add(
            apiSource.getData(urlConfirmMobelanja, confirmMobelanjaRequest, seqNum) //function(param)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView()?.hideProgress()
                        getView()?.onException(errorMessage)
                        onLoad = false
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView()?.hideProgress()
                        val inquiryBrivaRevampResponse = response.getData(
                            InquiryBrivaRevampResponse::class.java
                        )
                        getView()?.onSuccessGetConfirmationMobelanja(
                            inquiryBrivaRevampResponse,
                            urlPayMobelanja
                        )
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        onLoad = false
                        getView()?.hideProgress()
                        when(restResponse.code) {
                            RestResponse.ResponseCodeEnum.RC_SESSION_END.value -> getView()?.onSessionEnd(restResponse.desc)
                            RestResponse.ResponseCodeEnum.RC_HIT_EXCEEDED.value -> view.onException06(restResponse.getData(ExceptionResponse::class.java))
                            RestResponse.ResponseCodeEnum.RC_99.value -> getView()?.onException99(restResponse.desc)
                            else -> getView()?.onException(restResponse.desc)
                        }
                    }
                })
        )
    }

}