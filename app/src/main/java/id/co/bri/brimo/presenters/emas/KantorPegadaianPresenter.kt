package id.co.bri.brimo.presenters.emas

import id.co.bri.brimo.contract.IPresenter.emas.IKantorPegadaianPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.emas.IKantorPegadaianView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimo.data.repository.category.CategoryPfmSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.config.AppConfig

import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.emas.SearchPegadaianRequest
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.emas.KantorPegadaianResponse
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.disposables.CompositeDisposable
import java.util.concurrent.TimeUnit

class KantorPegadaianPresenter<V>(schedulerProvider: SchedulerProvider?,
                                  compositeDisposable: CompositeDisposable?,
                                  mBRImoPrefRepository: BRImoPrefSource?, apiSource: ApiSource?,
                                  categoryPfmSource: CategoryPfmSource?,
                                  transaksiPfmSource: TransaksiPfmSource?,
                                  anggaranPfmSource: AnggaranPfmSource?) : MvpPresenter<V>(
    schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource
),
        IKantorPegadaianPresenter<V> where V : IMvpView?, V : IKantorPegadaianView {

    var urlData : String = ""

    override fun setUrlSearch(url: String) {
        this.urlData = url
    }

    override fun searchPegadaian(request: SearchPegadaianRequest) {
        if (isViewAttached) {
            val seqNum = brImoPrefRepository.seqNumber
            compositeDisposable.add(
                    apiSource.getData(urlData, request, seqNum)
                            .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                            .subscribeOn(schedulerProvider.io())
                            .observeOn(schedulerProvider.mainThread())
                            .subscribeWith(object : ApiObserver(view, seqNum) {
                                override fun onFailureHttp(errorMessage: String) {
                                    getView().onException(errorMessage)
                                }

                                override fun onApiCallSuccess(response: RestResponse) {
                                    if (response.code.equals(RestResponse.ResponseCodeEnum.RC_SUCCESS.value, ignoreCase = true)){
                                        val listKantorResponse = response.getData(KantorPegadaianResponse::class.java)
                                        getView().onSuccessSearchPegadaian(listKantorResponse)
                                    }else if (response.code.equals(RestResponse.ResponseCodeEnum.RC_01.value, ignoreCase = true)){
                                        getView().onFailedGedData()
                                    }
                                }

                                override fun onApiCallError(restResponse: RestResponse) {
                                    getView().hideProgress()
                                    if (restResponse.code.equals(
                                                    RestResponse.ResponseCodeEnum.RC_SESSION_END.value,
                                                    ignoreCase = true
                                            )
                                    ) getView().onSessionEnd(restResponse.desc) else if (restResponse.code.equals(
                                                    RestResponse.ResponseCodeEnum.RC_12.value,
                                                    ignoreCase = true
                                            )
                                    ) else getView().onException(restResponse.desc)
                                }
                            })
            )
        }
    }


}