package id.co.bri.brimo.presenters.voucherstreaming;


import java.util.concurrent.TimeUnit;

import id.co.bri.brimo.contract.IPresenter.voucherstreaming.IDetailVoucherStreamingPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.voucherstreaming.IDetailVoucherStreamingView;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.config.AppConfig;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.apimodel.request.DetailVoucherStreamingRequest;
import id.co.bri.brimo.models.apimodel.request.InquiryStreamingRequest;
import id.co.bri.brimo.models.apimodel.response.GeneralInquiryResponse;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.models.apimodel.response.VoucStreamingList;
import id.co.bri.brimo.presenters.MvpPresenter;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.schedulers.Schedulers;

public class DetailVoucherStreamingPresenter<V extends IMvpView & IDetailVoucherStreamingView> extends MvpPresenter<V> implements IDetailVoucherStreamingPresenter<V> {

    protected String urlInquiry;
    protected String urlKonfirmasi;
    protected String urlPayment;
    protected String urlDetail;

    public DetailVoucherStreamingPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void getDetailVoucherStreaming(DetailVoucherStreamingRequest detailVoucherStreamingRequest) {
        if (isViewAttached()) {

            String seqNum = getBRImoPrefRepository().getSeqNumber();
            getView().isHideSkeleton(false);
            getCompositeDisposable().add(
                    getApiSource().getData(urlDetail, detailVoucherStreamingRequest, seqNum)
                            .subscribeOn(getSchedulerProvider().io())
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .observeOn(getSchedulerProvider().mainThread())
                            .subscribeWith(new ApiObserver(getView(),seqNum) {
                                @Override
                                protected void onFailureHttp(String type) {
                                    getView().isHideSkeleton(true);
                                    getView().onException(type);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    VoucStreamingList voucherResponse = response.getData(VoucStreamingList.class);
                                    getView().isHideSkeleton(true);
                                    getView().onSuccessGetDetail(voucherResponse);
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue())){
                                        getView().onSessionEnd(restResponse.getDesc());
                                    }else{
                                        getView().isHideSkeleton(true);
                                        getView().onException(restResponse.getDesc());
                                    }


                                }
                            })
            );
        }
    }

    @Override
    public void sendInquiry(String streamingId, String userId, String zoneId, String productCode, boolean isFromFastMenu) {
        if (isViewAttached()) {
            getView().showProgress();

            InquiryStreamingRequest inquiryStreamingRequest = new InquiryStreamingRequest(streamingId, userId, zoneId, productCode);

            String seqNum = getBRImoPrefRepository().getSeqNumber();

            getCompositeDisposable()
                    .add(getApiSource().getData(urlInquiry, inquiryStreamingRequest, seqNum)
                            .subscribeOn(Schedulers.io())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribeWith(new ApiObserver(getView(),seqNum) {

                                @Override
                                protected void onFailureHttp(String errorMessage) {
                                    getView().onException(errorMessage);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    getView().hideProgress();
                                    if (urlKonfirmasi != null && urlPayment != null) {
                                        GeneralInquiryResponse generalResponse = response.getData(GeneralInquiryResponse.class);
                                        getView().onSuccessGetInquiry(generalResponse, urlKonfirmasi, urlPayment, isFromFastMenu);
                                    }
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse ) {
                                    getView().hideProgress();
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                        getView().onSessionEnd(restResponse.getDesc());
                                    else
                                        getView().onException(restResponse.getDesc());
                                }
                            }));
        }
    }


    @Override
    public void setInquiryUrl(String urlInquiry) {
        this.urlInquiry = urlInquiry;
    }

    @Override
    public void setKonfirmasiUrl(String urlKonfirmasi) {
        this.urlKonfirmasi = urlKonfirmasi;
    }

    @Override
    public void setPaymentUrl(String urlPayment) {
        this.urlPayment = urlPayment;
    }

    @Override
    public void setDetailUrl(String urlDetail) {
        this.urlDetail = urlDetail;
    }
}