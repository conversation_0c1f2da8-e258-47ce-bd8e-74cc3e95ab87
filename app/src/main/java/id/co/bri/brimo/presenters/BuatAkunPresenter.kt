package id.co.bri.brimo.presenters

import android.util.Log
import id.co.bri.brimo.contract.BuatAkunContract
import id.co.bri.brimo.models.inputlayout.error.InputError
import id.co.bri.brimo.models.inputlayout.result.BuatAkunInputResult

class BuatAkunPresenter(private var view: BuatAkunContract.View) :
        BuatAkunContract.Presenter,
        BuatAkunInputResult.BuatAkunResultListener{

    private var TAG = "BuatAkunPresenter"

    override fun start() {
    }

    override fun stop() {
        TODO("not implemented") //To change body of created functions use File | Settings | File Templates.
    }


    /**
     * Checking the input layout results within BuatAkunInputResult model
     *
     * @param stringList: List of inputs from layouts put in string type
     */
    override fun onCheckDataInput(stringList: ArrayList<String>) {
        Log.d(TAG, "onCheckDataInput : " + stringList.toString())
        val inputResultModel = BuatAkunInputResult(stringList, this)
        inputResultModel.checkIfCorrect()
    }


    /**
     * Show the wrong alert in main fragment
     *
     * @param inputError: Error that will be shown for particular input layout
     */
    override fun onError(inputError: InputError) {
        Log.d(TAG, "onError : " + inputError.message)
        view.onWrongInput(inputError)
    }

    /**
     * Show that the inputs are correct
     */
    override fun onCorrect() {
        view.onCorrectInput()
    }
}