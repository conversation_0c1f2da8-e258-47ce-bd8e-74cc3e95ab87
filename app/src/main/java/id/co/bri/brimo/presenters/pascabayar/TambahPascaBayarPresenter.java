package id.co.bri.brimo.presenters.pascabayar;

import android.util.Log;

import id.co.bri.brimo.contract.IPresenter.pascabayar.ITambahPascaPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.pascabayar.ITambahPascaView;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.config.AppConfig;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.apimodel.request.InquiryPascaRequest;
import id.co.bri.brimo.models.apimodel.request.pascaBayarRevamp.AddSavedListPascaRequest;
import id.co.bri.brimo.models.apimodel.response.GeneralInquiryResponse;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.models.apimodel.response.transferrevamp.SavedDataExistResponse;
import id.co.bri.brimo.models.apimodel.response.transferrevamp.SavedDataResponse;
import id.co.bri.brimo.presenters.MvpPresenter;

import java.util.concurrent.TimeUnit;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.schedulers.Schedulers;

public class TambahPascaBayarPresenter <V extends IMvpView & ITambahPascaView> extends MvpPresenter<V> implements ITambahPascaPresenter<V> {

    private static final String TAG = "TambahPascaPresenter";

    protected String inquiryUrl;

    public TambahPascaBayarPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void getDataInquiry(InquiryPascaRequest request) {
        if (inquiryUrl == null || !isViewAttached()) {
            Log.d(TAG, "getDataInquiry: view atau inquiry urlInformasi null");
            return;
        }

        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        getCompositeDisposable()
                .add(getApiSource().getData(inquiryUrl, request, seqNum)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(Schedulers.io())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(new ApiObserver(getView(),seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                               onFailure(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                //TO-DO onSuccess
                                getView().hideProgress();
                                GeneralInquiryResponse pascaResponse = response.getData(GeneralInquiryResponse.class);
                                getView().onSuccessGetInquiry(pascaResponse);
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                onApiError(restResponse);
                            }
                        }));
    }

    @Override
    public void getDataInquiryAddSavedList(AddSavedListPascaRequest addSavedListPascaRequest) {
        if (inquiryUrl == null || !isViewAttached()) {
            Log.d(TAG, "getDataInquiry: view atau inquiry urlInformasi null");
            return;
        }

        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        getCompositeDisposable()
                .add(getApiSource().getData(inquiryUrl, addSavedListPascaRequest, seqNum)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(Schedulers.io())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(new ApiObserver(getView(), seqNum) {
                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                getView().hideProgress();
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_01.getValue())) {
                                    SavedDataExistResponse resData = response.getData(SavedDataExistResponse.class);
                                    getView().onExceptionAlreadySaved(resData.getTitle(), resData.getSubtitle());
                                } else {
                                    SavedDataResponse resData = response.getData(SavedDataResponse.class);
                                    getView().onSuccessGetSavedDataInquiry(resData);
                                }
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                onApiError(restResponse);
                            }
                        })
                );
    }

    @Override
    public void setInquiryUrl(String url) {
        inquiryUrl = url;
    }
}