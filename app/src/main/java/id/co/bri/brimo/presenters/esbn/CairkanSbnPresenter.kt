package id.co.bri.brimo.presenters.esbn

import id.co.bri.brimo.contract.IPresenter.esbn.ICairkanSbnPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.esbn.ICairkanSbnView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimo.data.repository.category.CategoryPfmSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.config.AppConfig

import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.esbn.GetRedeemRequest
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.esbn.earlyredeem.CairkanSbnResponse
import id.co.bri.brimo.models.apimodel.response.esbn.earlyredeem.HistoryRedeemSbnResponse
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.disposables.CompositeDisposable
import java.util.concurrent.TimeUnit

class CairkanSbnPresenter<V>(
    schedulerProvider: SchedulerProvider?,
    compositeDisposable: CompositeDisposable?,
    mBRImoPrefRepository: BRImoPrefSource?,
    apiSource: ApiSource?,
    categoryPfmSource: CategoryPfmSource?,
    transaksiPfmSource: TransaksiPfmSource?,
    anggaranPfmSource: AnggaranPfmSource?
) :
    MvpPresenter<V>(
        schedulerProvider,
        compositeDisposable,
        mBRImoPrefRepository,
        apiSource,
        transaksiPfmSource
    ),
    ICairkanSbnPresenter<V> where V : IMvpView?, V : ICairkanSbnView? {
    var urlCairkan: String? = null
    var urlHistory: String? = null
    override fun start() {
        super.start()
    }

    override fun stop() {
        super.stop()
    }

    override fun setIUrlGetInquiry(url: String?) {
        urlCairkan = url
    }

    override fun getInquiryPencairan() {
        view!!.showProgress()
        val seqNum = brImoPrefRepository.seqNumber
        compositeDisposable.add(
            apiSource.getData(urlCairkan, "", seqNum)
                .subscribeOn(schedulerProvider.single())
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(type: String) {
                        getView()!!.onException(type)
                        view!!.hideProgress()
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        view!!.hideProgress()
                        if (response.code.equals(RestResponse.ResponseCodeEnum.RC_SUCCESS.value, ignoreCase = true)) {
                            val dataResponse = response.getData(
                                CairkanSbnResponse::class.java
                            )
                            getView()!!.onSuccessGetRedeem(dataResponse)
                        }
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView()!!.hideProgress()
                        if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value, ignoreCase = true))
                            getView()!!.onSessionEnd(restResponse.desc)
                        else if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_12.value, ignoreCase = true))
                            getView()!!.onException(restResponse.desc)
                        else
                            getView()!!.onException(restResponse.desc)
                    }
                })
        )
    }

    override fun setUrlGetHistory(url: String?) {
        urlHistory = url
    }

    override fun getHistoryRedeem(request: GetRedeemRequest) {
        view!!.showProgress()
        val seqNum = brImoPrefRepository.seqNumber
        compositeDisposable.add(
            apiSource.getData(urlHistory, request, seqNum)
                .subscribeOn(schedulerProvider.single())
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(type: String) {
                        getView()!!.onException(type)
                        view!!.hideProgress()
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        view!!.hideProgress()
                        if (response.code.equals(RestResponse.ResponseCodeEnum.RC_SUCCESS.value, ignoreCase = true)) {
                            val dataResponse = response.getData(
                                HistoryRedeemSbnResponse::class.java
                            )
                            getView()!!.onSuccessGetHistory(dataResponse)
                        }
                        else if (response.code.equals(RestResponse.ResponseCodeEnum.RC_01.value, ignoreCase = true)) {
                            getView()!!.onSuccessGetHistory01()
                        }
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView()!!.hideProgress()
                        if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value, ignoreCase = true))
                            getView()!!.onSessionEnd(restResponse.desc)
                        else if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_01.value, ignoreCase = true))
                            getView()!!.onSuccessGetHistory01()
                        else
                            getView()!!.onException(restResponse.desc)

                    }
                })
        )
    }
}