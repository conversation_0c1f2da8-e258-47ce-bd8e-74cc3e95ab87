package id.co.bri.brimo.presenters.virtualdebitcard

import id.co.bri.brimo.contract.IPresenter.virtualdebitcard.IOnBoardingVDCPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.virtualdebitcard.IOnBoardingVDCView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimo.data.repository.category.CategoryPfmSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.config.AppConfig

import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.AccountRequest
import id.co.bri.brimo.models.apimodel.request.BranchRequest
import id.co.bri.brimo.models.apimodel.request.InquiryBrizziRequest
import id.co.bri.brimo.models.apimodel.response.EmptyStateResponse
import id.co.bri.brimo.models.apimodel.response.GeneralResponse
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.virtualdebitcard.AccountResponse
import id.co.bri.brimo.models.apimodel.response.virtualdebitcard.CheckStatusCreateVDCResponse
import id.co.bri.brimo.models.apimodel.response.virtualdebitcard.DetailVDCResponse
import id.co.bri.brimo.models.apimodel.response.virtualdebitcard.ListProductVDCResponse
import id.co.bri.brimo.models.apimodel.response.virtualdebitcard.ListVDCDataResponse
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.disposables.CompositeDisposable
import java.util.concurrent.TimeUnit

class OnBoardingVDCPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBRImoPrefRepository: BRImoPrefSource,
    apiSource: ApiSource,
    categoryPfmSource: CategoryPfmSource,
    transaksiPfmSource: TransaksiPfmSource,
    anggaranPfmSource: AnggaranPfmSource
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    mBRImoPrefRepository,
    apiSource,
    transaksiPfmSource
), IOnBoardingVDCPresenter<V> where V : IMvpView, V : IOnBoardingVDCView {

    private lateinit var urlGetListVirtualCard: String
    private lateinit var urlGetListVirtualCardAccount: String
    private lateinit var urlGetListProductVDC: String
    private lateinit var urlCheckStatusVDC: String
    private lateinit var urlGetDetailVirtualCard: String
    private lateinit var requestListProductVDC: BranchRequest
    private lateinit var requestCheckStatusVDC: AccountRequest
    private lateinit var requestDetailVDC: InquiryBrizziRequest

    override fun setUrlGetListVirtualCard(url: String) {
        this.urlGetListVirtualCard = url
    }

    override fun getListVirtualCard(username: String) {
        if (urlGetListVirtualCard.isEmpty() && !isViewAttached) return

        view.showSkeleton()

        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable
            .add(
                apiSource.getDataForm(urlGetListVirtualCard, seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(schedulerProvider.io())
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView().onException(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            if (response.code.equals(RestResponse.ResponseCodeEnum.RC_SUCCESS.value)) {
                                val responseGLVC =
                                    response.getData(ListVDCDataResponse::class.java)
                                getView().onSuccessGetListVirtualCard(responseGLVC)
                            } else if (response.code.equals("NF")) {
                                val responseNF = response.getData(GeneralResponse::class.java)
                                getView().notFoundListVirtualCard(responseNF)
                            }
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value))
                                getView().onSessionEnd(restResponse.desc)
                            else getView().onException(restResponse.desc)
                        }
                    })
            )
    }

    override fun setUrlGetListAccount(url: String) {
        this.urlGetListVirtualCardAccount = url
    }

    override fun getListAccount(username: String) {
        if (urlGetListVirtualCardAccount.isEmpty() && !isViewAttached) return

        view.showProgress()

        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable
            .add(
                apiSource.getDataForm(urlGetListVirtualCardAccount, seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(schedulerProvider.io())
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView().hideProgress()
                            getView().onException(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            getView().hideProgress()
                            if (response.code.equals(RestResponse.ResponseCodeEnum.RC_SUCCESS.value)) {
                                val responseAccount =
                                    response.getData(AccountResponse::class.java)
                                getView().onSuccessGetListVirtualCardAccount(responseAccount)
                            } else if (response.code.equals("NF")) {
                                val responseNF = response.getData(GeneralResponse::class.java)
                                getView().notFoundListVirtualCardAccount(responseNF)
                            } else if (response.code.equals("US")) {
                                val responseSM = response.getData(GeneralResponse::class.java)
                                getView().showSafetyMode(responseSM)
                            }
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView().hideProgress()
                            if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value))
                                getView().onSessionEnd(restResponse.desc)
                            else if (restResponse.code.equals("FO", ignoreCase = true)) {
                                val response = restResponse.getData(EmptyStateResponse::class.java)
                                getView().onExceptionFO(response)
                            } else getView().onException(restResponse.desc)
                        }
                    })
            )
    }

    override fun setUrlGetListProductVDC(url: String) {
        this.urlGetListProductVDC = url
    }

    override fun getListProductVDC(account: String) {
        if (urlGetListProductVDC.isEmpty() && !isViewAttached) return

        requestListProductVDC = BranchRequest(account = account)

        view.showProgress()

        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable
            .add(
                apiSource.getData(urlGetListProductVDC, requestListProductVDC, seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(schedulerProvider.io())
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView().hideProgress()
                            getView().onException(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            getView().hideProgress()
                            if (response.code.equals(RestResponse.ResponseCodeEnum.RC_SUCCESS.value)) {
                                val responseProductVDC =
                                    response.getData(ListProductVDCResponse::class.java)
                                getView().onSuccessGetListProductVDC(responseProductVDC)
                            } else if (response.code.equals("NF")) {
                                val responseNF = response.getData(GeneralResponse::class.java)
                                getView().notFoundListProductVDC(responseNF)
                            }
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView().hideProgress()
                            if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value))
                                getView().onSessionEnd(restResponse.desc)
                            else getView().onException(restResponse.desc)
                        }
                    })
            )
    }

    override fun setUrlCheckStatusCreateVDC(url: String) {
        this.urlCheckStatusVDC = url
    }

    override fun checkStatusCreateVDC(account: String) {
        if (urlCheckStatusVDC.isEmpty() && !isViewAttached) return

        requestCheckStatusVDC = AccountRequest(account)

        view.showProgress()

        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable
            .add(
                apiSource.getData(urlCheckStatusVDC, requestCheckStatusVDC, seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(schedulerProvider.io())
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView().hideProgress()
                            getView().onException(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            getView().hideProgress()
                            if (response.code.equals(RestResponse.ResponseCodeEnum.RC_SUCCESS.value)) {
                                val responseCheckStatus =
                                    response.getData(CheckStatusCreateVDCResponse::class.java)
                                getView().onSuccessCheckStatusVDC(responseCheckStatus)
                            } else if (response.code.equals(RestResponse.ResponseCodeEnum.RC_OP.value)) {
                                val responseOnProcess =
                                    response.getData(GeneralResponse::class.java)
                                getView().onProcessCheckStatusVDC(responseOnProcess)
                            } else if (response.code.equals(RestResponse.ResponseCodeEnum.RC_01.value)) {
                                val responseMaxLimit = response.getData(GeneralResponse::class.java)
                                getView().onMaxLimitCheckStatusVDC(responseMaxLimit)
                            }
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView().hideProgress()
                            if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value))
                                getView().onSessionEnd(restResponse.desc)
                            else getView().onException(restResponse.desc)
                        }
                    })
            )
    }

    override fun setUrlDetailVDC(url: String) {
        this.urlGetDetailVirtualCard = url
    }

    override fun getDetailVDC(cardNumber: String) {
        if (urlGetDetailVirtualCard.isEmpty() && !isViewAttached) return

        requestDetailVDC = InquiryBrizziRequest(cardNumber)

        view.showProgress()

        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable
            .add(
                apiSource.getData(urlGetDetailVirtualCard, requestDetailVDC, seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(schedulerProvider.io())
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView().hideProgress()
                            getView().onException(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            getView().hideProgress()
                            val responseDetailVDC =
                                response.getData(DetailVDCResponse::class.java)
                            getView().onSuccessGetDetailVDC(responseDetailVDC)
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView().hideProgress()
                            if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value))
                                getView().onSessionEnd(restResponse.desc)
                            else getView().onException(restResponse.desc)
                        }
                    })
            )
    }
}