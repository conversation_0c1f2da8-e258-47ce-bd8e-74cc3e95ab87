package id.co.bri.brimo.presenters.saldo;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

import id.co.bri.brimo.R;
import id.co.bri.brimo.contract.IPresenter.saldo.ISumberDanaPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.saldo.ISumberDanaView;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.config.AppConfig;
import id.co.bri.brimo.domain.converter.MapperHelper;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.AccountModel;
import id.co.bri.brimo.models.apimodel.response.cc.DetailCcSofResponse;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.models.apimodel.response.SaldoReponse;
import id.co.bri.brimo.presenters.MvpPresenter;
import io.reactivex.Observable;
import io.reactivex.ObservableSource;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.functions.Function;
import io.reactivex.observers.DisposableObserver;

/**
 * Created by FNS
 */

public class SumberDanaPresenter<V extends IMvpView & ISumberDanaView> extends MvpPresenter<V> implements ISumberDanaPresenter<V> {


    private final List<Integer> accountModelListFailed = new ArrayList<>();

    public SumberDanaPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }


    @Override
    public void getSaldo(List<AccountModel> list) {
        getCompositeDisposable().add(Observable.fromIterable(list)
                .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                .flatMap(new Function<AccountModel, ObservableSource<AccountModel>>() {
                    @Override
                    public ObservableSource<AccountModel> apply(AccountModel account) throws Exception {
                        if (account.getAccountType().equalsIgnoreCase("CC"))
                            return getSaldoObservableCc(account);
                        else
                            return getSaldoObservable(account);
                    }
                })
                .subscribeWith(new DisposableObserver<AccountModel>() {

                    @Override
                    public void onNext(AccountModel account) {
                        if (account == null) {
                            return;
                        }

                        if (account.getSaldoReponse() == null) {
                            return;
                        }

                        int postion = list.indexOf(account);
                        if (postion == -1)
                            return;

                        if (account.getAccountType().equalsIgnoreCase("CC")) {
                            list.get(postion).setDetailCcSofResponse(account.getDetailCcSofResponse());
                        } else {
                            list.get(postion).setSaldoReponse(account.getSaldoReponse());
                        }

                        if (account.getSaldoReponse().getBalanceString() == null) {
                            return;
                        }

                        if (account.getIsDefault() == 1) {
                            if (account.getSaldoReponse().getBalanceString() != null) {
                                if (!GeneralHelper.isContains(R.array.response_code_failed_saldo, account.getSaldoReponse().getBalanceString())) {
                                    SaldoReponse saldoReponse = account.getSaldoReponse();

                                    getBRImoPrefRepository().saveSaldoRekeningUtama(String.valueOf(saldoReponse.getBalance()));
                                    getBRImoPrefRepository().saveSaldoRekeningUtamaString(saldoReponse.getBalanceString());
                                    getBRImoPrefRepository().saveCurrency(saldoReponse.getCurrency());
                                    getBRImoPrefRepository().saveNameRekeningUtama(saldoReponse.getName());
                                } else {
                                    getBRImoPrefRepository().saveSaldoRekeningUtama(String.valueOf(0));
                                    getBRImoPrefRepository().saveSaldoRekeningUtamaString("-");

                                }
                            } else {
                                getBRImoPrefRepository().saveSaldoRekeningUtama(String.valueOf(0));
                                getBRImoPrefRepository().saveSaldoRekeningUtamaString("-");
                            }

                        }

                        addBalanceString(account, postion);


                        getView().onGetSaldo(list);
                    }

                    @Override
                    public void onError(Throwable e) {

                    }

                    @Override
                    public void onComplete() {
                        getView().onGetSaldoComplete(accountModelListFailed);
                    }
                }));
    }


    @Override
    public void getSaldoFailed(List<AccountModel> list, List<Integer> listPosition) {
        getCompositeDisposable().add(Observable.fromIterable(list)
                .flatMap(new Function<AccountModel, ObservableSource<AccountModel>>() {
                    @Override
                    public ObservableSource<AccountModel> apply(AccountModel account) throws Exception {
                        int postion = list.indexOf(account);
                        if (GeneralHelper.isContains(listPosition, postion))
                            if (account.getAccountType().equalsIgnoreCase("CC"))
                                return getSaldoObservableCc(account);
                            else
                                return getSaldoObservable(account);
                        else
                            return Observable.just(account);
                    }
                })
                .subscribeWith(new DisposableObserver<AccountModel>() {

                    @Override
                    public void onNext(AccountModel account) {
                        int postion = list.indexOf(account);
                        if (postion == -1)
                            return;
                        if (account.getAccountType().equalsIgnoreCase("CC")) {
                            list.get(postion).setDetailCcSofResponse(account.getDetailCcSofResponse());
                        } else {
                            list.get(postion).setSaldoReponse(account.getSaldoReponse());
                        }

                        if (account.getIsDefault() == 1) {
                            SaldoReponse saldoReponse = account.getSaldoReponse();

                            if (saldoReponse == null) {
                                return;
                            }

                            if (!GeneralHelper.isContains(R.array.response_code_failed_saldo, saldoReponse.getBalanceString())) {
                                getBRImoPrefRepository().saveSaldoRekeningUtama(String.valueOf(saldoReponse.getBalance()));
                                getBRImoPrefRepository().saveSaldoRekeningUtamaString(saldoReponse.getBalanceString());
                                getBRImoPrefRepository().saveCurrency(saldoReponse.getCurrency());
                                getBRImoPrefRepository().saveNameRekeningUtama(saldoReponse.getName());
                            } else {
                                getBRImoPrefRepository().saveSaldoRekeningUtama(String.valueOf(0));
                                getBRImoPrefRepository().saveSaldoRekeningUtamaString("-");
                            }
                        }

                        addBalanceString(account, postion);

                        getView().onGetSaldo(list);
                    }

                    @Override
                    public void onError(Throwable e) {

                    }

                    @Override
                    public void onComplete() {
                        getView().onGetSaldoComplete(accountModelListFailed);
                    }
                }));
    }

    @Override
    public void getSaldoFM(List<AccountModel> list) {
        getCompositeDisposable().add(Observable.fromIterable(list)
                .flatMap(new Function<AccountModel, ObservableSource<AccountModel>>() {
                    @Override
                    public ObservableSource<AccountModel> apply(AccountModel account) throws Exception {
                        return getSaldoObservableFM(account);
                    }
                })
                .subscribeWith(new DisposableObserver<AccountModel>() {

                    @Override
                    public void onNext(AccountModel account) {
                        if (account == null) {
                            return;
                        }
                        int postion = list.indexOf(account);
                        if (postion == -1)
                            return;
                        if (account.getAccountType().equalsIgnoreCase("CC")) {
                            list.get(postion).setDetailCcSofResponse(account.getDetailCcSofResponse());
                        } else {
                            list.get(postion).setSaldoReponse(account.getSaldoReponse());
                        }

                        if (account.getIsDefault() == 1) {
                            if (!GeneralHelper.isContains(R.array.response_code_failed_saldo, account.getSaldoReponse().getBalanceString())) {
                                SaldoReponse saldoReponse = account.getSaldoReponse();
                                if (saldoReponse == null){
                                    return;
                                }

                                getBRImoPrefRepository().saveSaldoRekeningUtama(String.valueOf(saldoReponse.getBalance()));
                                getBRImoPrefRepository().saveSaldoRekeningUtamaString(saldoReponse.getBalanceString());
                                getBRImoPrefRepository().saveCurrency(saldoReponse.getCurrency());
                                getBRImoPrefRepository().saveNameRekeningUtama(saldoReponse.getName());
                            } else {
                                getBRImoPrefRepository().saveSaldoRekeningUtama(String.valueOf(0));
                                getBRImoPrefRepository().saveSaldoRekeningUtamaString("-");

                            }
                        }

                        addBalanceString(account, postion);

                        getView().onGetSaldo(list);
                    }

                    @Override
                    public void onError(Throwable e) {

                    }

                    @Override
                    public void onComplete() {
                        getView().onGetSaldoComplete(accountModelListFailed);
                    }
                }));
    }

    private void addBalanceString(AccountModel account, int position) {
        if (account.getSaldoReponse() == null) {
            return;
        }
        if(account.getAccountType().equalsIgnoreCase("CC")) {
            String balance = account.getDetailCcSofResponse().getBalanceString();
            if (balance == null){
                return;
            }
            if (GeneralHelper.isContains(R.array.response_code_failed_saldo, account.getDetailCcSofResponse().getBalanceString()))
                accountModelListFailed.add(position);
        } else {
            String balance = account.getSaldoReponse().getBalanceString();
            if (balance == null) {
                return;
            }
            if (GeneralHelper.isContains(R.array.response_code_failed_saldo, account.getSaldoReponse().getBalanceString()))
                accountModelListFailed.add(position);
        }
    }


    @Override
    public void getSaldoFailedFM(List<AccountModel> list, List<Integer> listPosition) {
        getCompositeDisposable().add(Observable.fromIterable(list)
                .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                .flatMap(new Function<AccountModel, ObservableSource<AccountModel>>() {
                    @Override
                    public ObservableSource<AccountModel> apply(AccountModel account) throws Exception {
                        int postion = list.indexOf(account);
                        if (GeneralHelper.isContains(listPosition, postion))
                            return getSaldoObservableFM(account);
                        else
                            return Observable.just(account);
                    }
                })
                .subscribeWith(new DisposableObserver<AccountModel>() {

                    @Override
                    public void onNext(AccountModel account) {
                        int postion = list.indexOf(account);
                        if (postion == -1)
                            return;
                        if (account.getAccountType().equalsIgnoreCase("CC")) {
                            list.get(postion).setDetailCcSofResponse(account.getDetailCcSofResponse());
                        } else {
                            list.get(postion).setSaldoReponse(account.getSaldoReponse());
                        }

                        if (account.getIsDefault() == 1) {
                            if (account.getSaldoReponse() == null) {
                                return;
                            }

                            SaldoReponse saldoReponse = account.getSaldoReponse();

                            if (!GeneralHelper.isContains(R.array.response_code_failed_saldo, saldoReponse.getBalanceString())) {
                                getBRImoPrefRepository().saveSaldoRekeningUtama(String.valueOf(saldoReponse.getBalance()));
                                getBRImoPrefRepository().saveSaldoRekeningUtamaString(saldoReponse.getBalanceString());
                                getBRImoPrefRepository().saveCurrency(saldoReponse.getCurrency());
                                getBRImoPrefRepository().saveNameRekeningUtama(saldoReponse.getName());
                            } else {
                                getBRImoPrefRepository().saveSaldoRekeningUtama(String.valueOf(0));
                                getBRImoPrefRepository().saveSaldoRekeningUtamaString("-");
                            }
                        }

                        addBalanceString(account, postion);

                        getView().onGetSaldo(list);
                    }

                    @Override
                    public void onError(Throwable e) {

                    }

                    @Override
                    public void onComplete() {
                        getView().onGetSaldoComplete(accountModelListFailed);
                    }
                }));
    }

    private Observable<AccountModel> getSaldoObservableFM(AccountModel account) {
        String seqNum = getBRImoPrefRepository().getSeqNumber();

        return getApiSource().getSaldoNormalFM(getBRImoPrefRepository().getUsername(), getBRImoPrefRepository().getTokenKey(), account.getAcoount(), seqNum)

                .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                .subscribeOn(getSchedulerProvider().io())
                .observeOn(getSchedulerProvider().mainThread())
                .map(new Function<String, AccountModel>() {
                    @Override
                    public AccountModel apply(String stringResponse) throws Exception {
                        SaldoReponse saldoReponse;
                        RestResponse restResponse = null;

                        //get checksum response
                        String responseCheck = MapperHelper.getIdResponse(stringResponse);

                        //jika check
                        if (responseCheck.isEmpty()) {
                            saldoReponse = new SaldoReponse();
                            saldoReponse.setBalanceString(RestResponse.ResponseCodeEnum.RC_TO.getValue());
                        }

                        //coba konversi String ke RestResponse model
                        restResponse = MapperHelper.stringToRestResponse(stringResponse, seqNum);

                        //jika response tidak kosong
                        if (restResponse != null) {

                            if (GeneralHelper.isContains(R.array.response_code_success, restResponse.getCode())) {
                                if (restResponse.getDesc().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_01.getValue())) {
                                    saldoReponse = new SaldoReponse();
                                    saldoReponse.setBalanceString(restResponse.getDesc());
                                } else
                                    saldoReponse = restResponse.getData(SaldoReponse.class);
                            } else if (restResponse.getCode().equals(RestResponse.ResponseCodeEnum.RC_12.getValue())) {
                                saldoReponse = new SaldoReponse();
                                saldoReponse.setBalanceString(restResponse.getCode());
                                saldoReponse.setName(restResponse.getDesc());
                            } else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue())) {
                                saldoReponse = new SaldoReponse();
                                saldoReponse.setBalanceString(restResponse.getCode());
                                getView().onSessionEnd(restResponse.getDesc());
                            } else {
                                saldoReponse = new SaldoReponse();
                                saldoReponse.setBalanceString(RestResponse.ResponseCodeEnum.RC_TO.getValue());
                            }
                        } else {
                            saldoReponse = new SaldoReponse();
                            saldoReponse.setBalanceString(RestResponse.ResponseCodeEnum.RC_TO.getValue());
                        }
                        account.setSaldoReponse(saldoReponse);
                        return account;
                    }
                });
    }


    private Observable<AccountModel> getSaldoObservable(AccountModel account) {
        String seqNum = getBRImoPrefRepository().getSeqNumber();

        return getApiSource().getSaldoNormal(account.getAcoount(), seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                .subscribeOn(getSchedulerProvider().newThread())
                .observeOn(getSchedulerProvider().mainThread())
                .map(new Function<String, AccountModel>() {
                    @Override
                    public AccountModel apply(String stringResponse) throws Exception {
                        SaldoReponse saldoReponse;
                        RestResponse restResponse = null;

                        //get checksum response
                        String responseCheck = MapperHelper.getIdResponse(stringResponse);

                        //jika check
                        if (responseCheck.isEmpty()) {
                            saldoReponse = new SaldoReponse();
                            saldoReponse.setBalanceString(RestResponse.ResponseCodeEnum.RC_TO.getValue());
                        }

                        //coba konversi String ke RestResponse model
                        restResponse = MapperHelper.stringToRestResponse(stringResponse, seqNum);

                        if (restResponse != null) {

                            if (GeneralHelper.isContains(R.array.response_code_success, restResponse.getCode())) {

                                if (restResponse.getDesc().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_01.getValue())) {
                                    saldoReponse = new SaldoReponse();
                                    saldoReponse.setBalanceString(restResponse.getDesc());
                                } else {
                                    saldoReponse = restResponse.getData(SaldoReponse.class);
                                }

                            } else if (restResponse.getCode().equals(RestResponse.ResponseCodeEnum.RC_12.getValue())) {
                                saldoReponse = new SaldoReponse();
                                saldoReponse.setBalanceString(restResponse.getCode());
                                saldoReponse.setName(restResponse.getDesc());
                            } else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue())) {
                                saldoReponse = new SaldoReponse();
                                saldoReponse.setBalanceString(restResponse.getCode());
                                getView().onSessionEnd(restResponse.getDesc());
                            } else {
                                saldoReponse = new SaldoReponse();
                                saldoReponse.setBalanceString(RestResponse.ResponseCodeEnum.RC_TO.getValue());
                            }
                        } else {
                            saldoReponse = new SaldoReponse();
                            saldoReponse.setBalanceString(RestResponse.ResponseCodeEnum.RC_TO.getValue());
                        }
                        account.setSaldoReponse(saldoReponse);
                        return account;
                    }
                });
    }

    private Observable<AccountModel> getSaldoObservableCc(AccountModel account) {
        String seqNum = getBRImoPrefRepository().getSeqNumber();

        return getApiSource().getSaldoCc(account.getAcoount(), seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                .subscribeOn(getSchedulerProvider().newThread())
                .observeOn(getSchedulerProvider().mainThread())
                .map(new Function<String, AccountModel>() {
                    @Override
                    public AccountModel apply(String stringResponse) throws Exception {
                        SaldoReponse saldoReponse = null;
                        DetailCcSofResponse detailCcSofResponse;
                        RestResponse restResponse = null;

                        //get checksum response
                        String responseCheck = MapperHelper.getIdResponse(stringResponse);

                        //jika check
                        if (responseCheck.isEmpty()) {
                            detailCcSofResponse = new DetailCcSofResponse();
                            detailCcSofResponse.setBalanceString(RestResponse.ResponseCodeEnum.RC_TO.getValue());
                        }

                        //coba konversi String ke RestResponse model
                        restResponse = MapperHelper.stringToRestResponse(stringResponse, seqNum);

                        if (restResponse != null) {

                            if (GeneralHelper.isContains(R.array.response_code_success, restResponse.getCode())) {

                                if (restResponse.getDesc().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_01.getValue())) {
                                    detailCcSofResponse = new DetailCcSofResponse();
                                    detailCcSofResponse.setBalanceString(restResponse.getDesc());
                                } else {
                                    detailCcSofResponse = restResponse.getData(DetailCcSofResponse.class);
                                    saldoReponse = restResponse.getData(SaldoReponse.class);
//                                    account.setAlias(detailCcSofResponse.getName());
                                }

                            } else if (restResponse.getCode().equals(RestResponse.ResponseCodeEnum.RC_12.getValue())) {
                                detailCcSofResponse = new DetailCcSofResponse();
                                detailCcSofResponse.setBalanceString(restResponse.getCode());
                                detailCcSofResponse.setName(restResponse.getDesc());
                                saldoReponse = new SaldoReponse();
                                saldoReponse.setBalanceString(restResponse.getCode());
                                saldoReponse.setName(restResponse.getDesc());
                            } else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue())) {
                                detailCcSofResponse = new DetailCcSofResponse();
                                detailCcSofResponse.setBalanceString(restResponse.getCode());
                                saldoReponse = new SaldoReponse();
                                saldoReponse.setBalanceString(restResponse.getCode());
                                getView().onSessionEnd(restResponse.getDesc());
                            } else {
                                detailCcSofResponse = new DetailCcSofResponse();
                                detailCcSofResponse.setBalanceString(RestResponse.ResponseCodeEnum.RC_TO.getValue());
                                saldoReponse = new SaldoReponse();
                                saldoReponse.setBalanceString(RestResponse.ResponseCodeEnum.RC_TO.getValue());
                            }
                        } else {
                            detailCcSofResponse = new DetailCcSofResponse();
                            detailCcSofResponse.setBalanceString(RestResponse.ResponseCodeEnum.RC_TO.getValue());
                        }
                        account.setDetailCcSofResponse(detailCcSofResponse);
                        account.setSaldoReponse(saldoReponse);
                        return account;
                    }
                });
    }

    @Override
    public void stop() {
        super.stop();
    }
}