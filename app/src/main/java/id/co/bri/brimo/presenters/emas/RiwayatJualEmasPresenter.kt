package id.co.bri.brimo.presenters.emas

import id.co.bri.brimo.contract.IPresenter.emas.IRiwayatEmasJualPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.emas.IRiwayatEmasJualView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimo.data.repository.category.CategoryPfmSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.config.AppConfig
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.emas.RiwayatFilterRequest
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.emas.RiwayatTransaksiEmasResponse
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.disposables.Disposable
import io.reactivex.schedulers.Schedulers
import java.util.concurrent.TimeUnit

class RiwayatJualEmasPresenter<V>(schedulerProvider: SchedulerProvider?,
                                  compositeDisposable: CompositeDisposable?,
                                  mBRImoPrefRepository: BRImoPrefSource?, apiSource: ApiSource?,
                                  categoryPfmSource: CategoryPfmSource?,
                                  transaksiPfmSource: TransaksiPfmSource?,
                                  anggaranPfmSource: AnggaranPfmSource?) : MvpPresenter<V>(
    schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource
),
        IRiwayatEmasJualPresenter<V> where V : IMvpView?, V : IRiwayatEmasJualView {

    var mUrlFilter :String = ""
    private var urlRiwayat : String = ""


    override fun setUrlFilter(urlFilter: String) {
        mUrlFilter = urlFilter
    }

    override fun getMutationRange(request: RiwayatFilterRequest?) {
        if (mUrlFilter == null || !isViewAttached) {
            return
        }

        val seq = brImoPrefRepository.seqNumber

        compositeDisposable.add(
                apiSource.getData(mUrlFilter, request, seq) //function(param)
                        .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                        .subscribeOn(Schedulers.io())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(object : ApiObserver(view, seq) {
                            override fun onFailureHttp(errorMessage: String) {
                                getView().onException(errorMessage)
                            }

                            override fun onApiCallSuccess(response: RestResponse) {
                                    val mutasiResponse = response.getData(RiwayatTransaksiEmasResponse::class.java)
                                    getView().onSuccessGetMutation(mutasiResponse)
                            }

                            override fun onApiCallError(restResponse: RestResponse) {
                                when(restResponse.code){
                                    RestResponse.ResponseCodeEnum.RC_12.value -> getView().onException(restResponse.desc)
                                    else -> onApiError(restResponse)
                                }
                            }
                        })
        )
    }

    override fun setUrlRiwayatTransaksi(url: String) {
        urlRiwayat = url
    }

    override fun getDataRiwayatTransaksi() {
        view?.showProgress()
        val seqNum = brImoPrefRepository.seqNumber
        val disposable: Disposable =
            apiSource.getDataForm(urlRiwayat, seqNum) //function(param)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView()?.onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView()?.hideProgress()

                        val responseRiwayat = response.getData(
                            RiwayatTransaksiEmasResponse::class.java
                        )
                        getView()?.onSuccessGetMutation(responseRiwayat)
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView()?.hideProgress()
                         getView()?.onException(
                            restResponse.desc
                        )
                    }
                })
        compositeDisposable.add(disposable)
    }
}