package id.co.bri.brimo.presenters.applyvcc

import id.co.bri.brimo.R
import id.co.bri.brimo.contract.IPresenter.applyvcc.IApplyCcDataFormPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.onboardingcc.IApplyCcDataFormView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimo.data.repository.category.CategoryPfmSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.applyccrevamp.ApplyCcGetPostalCodeRequest
import id.co.bri.brimo.models.apimodel.response.applyccrevamp.ApplyCcGetPostalCodeResponse
import id.co.bri.brimo.models.applyccrevamp.toApplyCcGetPostalCodeModel
import id.co.bri.brimo.presenters.MvpPresenter
import id.co.bri.brimo.util.extension.getDataWithOrWithoutRequestState
import id.co.bri.brimo.util.extension.handleBaseResponseConvertData
import io.reactivex.disposables.CompositeDisposable

class ApplyCcDataFormPresenter<V>(
    schedulerProvider: SchedulerProvider?,
    compositeDisposable: CompositeDisposable?,
    mBRImoPrefRepository: BRImoPrefSource?,
    apiSource: ApiSource?,
    categoryPfmSource: CategoryPfmSource?,
    transaksiPfmSource: TransaksiPfmSource?,
    anggaranPfmSource: AnggaranPfmSource?,
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    mBRImoPrefRepository,
    apiSource,
    transaksiPfmSource
), IApplyCcDataFormPresenter<V> where V : IMvpView, V : IApplyCcDataFormView {
    override fun getDataForm() {
        view.getDataWithOrWithoutRequestState(
            GeneralHelper.getString(R.string.url_apply_cc_get_data_from),
            brImoPrefRepository,
            compositeDisposable,
            apiSource,
            schedulerProvider,
        ) {
            view.onGetDataFormSuccessState(it)
        }
    }

    override fun getPostalCode(keyword: String) {
        val request = ApplyCcGetPostalCodeRequest(keyword)
        view.getDataWithOrWithoutRequestState(
            GeneralHelper.getString(R.string.url_apply_cc_get_postal_code),
            brImoPrefRepository,
            compositeDisposable,
            apiSource,
            schedulerProvider,
            request,
        ) {
            view.getPostalCodeSuccessState(it.handleBaseResponseConvertData { restResponse ->
                restResponse.getData(ApplyCcGetPostalCodeResponse::class.java).toApplyCcGetPostalCodeModel()
            })
        }
    }
}