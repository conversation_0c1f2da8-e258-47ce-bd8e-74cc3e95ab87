package id.co.bri.brimo.presenters.registrasirevamp

import com.google.gson.Gson
import id.co.bri.brimo.contract.IPresenter.registrasirevamp.IRegistrasiDokumenPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.registrasirevamp.IRegistrasiDokumenView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimo.data.repository.category.CategoryPfmSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.config.AppConfig
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.RegisIdRequest
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.onboardingrevamp.ForceUpdateResponse
import id.co.bri.brimo.models.apimodel.response.registrasi.RegisProgressResponse
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.Schedulers
import java.util.concurrent.TimeUnit

class RegistrasiDokumenPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBRImoPrefRepository: BRImoPrefSource,
    apiSource: ApiSource,
    transaksiPfmSource: TransaksiPfmSource
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    mBRImoPrefRepository,
    apiSource,
    transaksiPfmSource
), IRegistrasiDokumenPresenter<V> where V : IMvpView, V : IRegistrasiDokumenView {

    private lateinit var urlProgress: String

    override fun setUrlProgress(url: String) {
        urlProgress = url
    }

    override fun getProgressRegis() {
        if (urlProgress.isNotEmpty() && isViewAttached) {

            view.showProgress()
            val seqNum = brImoPrefRepository.seqNumber

            val regisIdRequest = RegisIdRequest(brImoPrefRepository.deviceId)

            compositeDisposable.add(
                apiSource.getData(urlProgress, regisIdRequest, seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView().hideProgress()
                            getView().onExceptionNoBackAction(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            getView().hideProgress()
                            val regisProgressResponse =
                                response.getData(RegisProgressResponse::class.java)

                            when (response.code) {
                                RestResponse.ResponseCodeEnum.RC_SUCCESS.value,
                                RestResponse.ResponseCodeEnum.R_01.value ->
                                    if (regisProgressResponse.status == 0) {
                                        getView().onSuccessGetProgress()
                                    } else {
                                        getView().onSuccessCheckPoint(
                                            Gson().toJson(response.data),
                                            regisProgressResponse
                                        )
                                    }

                                RestResponse.ResponseCodeEnum.RC_02.value -> {
                                    val forceUpdateRes =
                                        response.getData(ForceUpdateResponse::class.java)
                                    getView().onUpdateVersion(forceUpdateRes)
                                }
                            }
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView().hideProgress()
                            getView().onExceptionRevamp(restResponse.desc)
                        }
                    })
            )
        }
    }
}