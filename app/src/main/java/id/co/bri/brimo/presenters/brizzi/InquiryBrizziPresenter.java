package id.co.bri.brimo.presenters.brizzi;

import id.co.bri.brimo.contract.IPresenter.brizzi.IInquiryBrizziPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.base.IBaseInquiryView;
import id.co.bri.brimo.contract.IView.brizzi.IInquiryBrizziView;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.config.AppConfig;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.apimodel.request.ConfirmationRequest;
import id.co.bri.brimo.models.apimodel.request.FastConfirmationRequest;
import id.co.bri.brimo.models.apimodel.response.GeneralConfirmationResponse;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.presenters.base.BaseInquiryPresenter;

import java.util.concurrent.TimeUnit;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.disposables.Disposable;
import io.reactivex.schedulers.Schedulers;

public class InquiryBrizziPresenter<V extends IMvpView & IBaseInquiryView & IInquiryBrizziView> extends BaseInquiryPresenter<V> implements IInquiryBrizziPresenter<V> {

    protected static final String TAG = "InquiryBrivaPresenter";
    protected String urlConfirmation = null;
    protected Object confirmationRequest;


    public InquiryBrizziPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, categoryPfmSource, transaksiPfmSource, anggaranPfmSource);
    }

    @Override
    public void getDataConfirmationBrizzi(String refNum, String accountNum, String amount, String save, boolean fromFastMenu) {
        if (isViewAttached() && urlConfirmation != null) {

            getView().showProgress();
            if (fromFastMenu)
                confirmationRequest = new FastConfirmationRequest(getFastMenuRequest(), refNum, accountNum, amount, save);
            else
                confirmationRequest = new ConfirmationRequest(refNum, accountNum, amount, save);

            String seqNum = getBRImoPrefRepository().getSeqNumber();

            Disposable disposable = getApiSource().getData(urlConfirmation, confirmationRequest, seqNum)//function(param)
                    .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(new ApiObserver(getView(),seqNum) {

                        @Override
                        protected void onFailureHttp(String errorMessage) {
                            getView().onException(errorMessage);
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            try {
                                getView().hideProgress();
                                GeneralConfirmationResponse brivaResponse = response.getData(GeneralConfirmationResponse.class);
                                getView().onSuccessGetConfirmationBrizzi(brivaResponse);
                            }catch (Exception e){
                                getView().onException("Koneksi Terputus");
                            }

                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            getView().hideProgress();
                            if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                getView().onSessionEnd(restResponse.getDesc());
                            else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.getValue()))
                                getView().onException93(restResponse.getDesc());
                            else
                                getView().onException(restResponse.getDesc());
                        }
                    });

            getCompositeDisposable().add(disposable);
        }
    }

    @Override
    public void setUrlConfirmationBrizzi(String urlConfirmation) {
        this.urlConfirmation = urlConfirmation;
    }


}