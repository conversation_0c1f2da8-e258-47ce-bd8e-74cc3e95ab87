package id.co.bri.brimo.presenters.onboardingrevamp

import com.google.gson.Gson
import id.co.bri.brimo.contract.IPresenter.onboardingrevamp.IOnboardingPendingPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.onboardingrevamp.IOnboardingPendingView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimo.data.repository.category.CategoryPfmSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.config.AppConfig
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.onboardingrevamp.OnboardingIdRequest
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.StatusResponse
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.Schedulers
import java.util.concurrent.TimeUnit

class OnboardingPendingPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBRImoPrefRepository: BRImoPrefSource,
    apiSource: ApiSource,
    categoryPfmSource: CategoryPfmSource,
    transaksiPfmSource: TransaksiPfmSource,
    anggaranPfmSource: AnggaranPfmSource
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    mBRImoPrefRepository,
    apiSource,
    transaksiPfmSource
), IOnboardingPendingPresenter<V> where V : IMvpView, V : IOnboardingPendingView {

    private var urlProgress: String = ""
    override fun setUrlProgress(url: String) {
        urlProgress = url
    }

    override fun getProgressOnboarding() {
        if (urlProgress.isEmpty() || !isViewAttached) return
        view.showProgress()
        val seqNum = brImoPrefRepository.seqNumber
        val request = OnboardingIdRequest(brImoPrefRepository.deviceId)

        compositeDisposable.add(
            apiSource.getData(urlProgress, request, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView().hideProgress()
                        getView().onExceptionNoBackAction(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView().hideProgress()
                        val statusResponse =
                            response.getData(StatusResponse::class.java)

                        getView().progress30s()

                        when (statusResponse.status) {
                            7 -> {
                                getView().onSuccessKyc(
                                    Gson().toJson(response.data),
                                    statusResponse
                                )
                            }
                            51 -> {
                                getView().onFailedKycKtp(
                                    Gson().toJson(response.data),
                                    statusResponse
                                )
                            }
                            52 -> {
                                getView().onFailedKycKtpVideo(
                                    Gson().toJson(response.data),
                                    statusResponse
                                )
                            }
                            53 -> {
                                getView().onFailedKycVideo(
                                    Gson().toJson(response.data),
                                    statusResponse
                                )
                            }
                            85 -> {
                                getView().onTermConditionData(
                                    Gson().toJson(response.data),
                                    statusResponse.status
                                )
                            }
                        }
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView().hideProgress()
                        getView().onExceptionRevamp(restResponse.desc)
                    }
                })
        )
    }
}