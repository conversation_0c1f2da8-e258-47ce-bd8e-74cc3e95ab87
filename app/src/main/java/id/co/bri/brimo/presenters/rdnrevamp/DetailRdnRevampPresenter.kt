package id.co.bri.brimo.presenters.rdnrevamp

import id.co.bri.brimo.contract.IPresenter.rdnrevamp.IDetailRdnRevampPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.rdnrevamp.IDetailRdnRevampView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.rdn.InquiryRdnRequest
import id.co.bri.brimo.models.apimodel.request.rdn.RdnHistoryListWithdrawRequest
import id.co.bri.brimo.models.apimodel.request.rdn.RdnInquiryWithdrawRequest
import id.co.bri.brimo.models.apimodel.response.GeneralResponse
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.rdn.InquiryRdnResponse
import id.co.bri.brimo.models.apimodel.response.rdnrevamp.RdnHistoryListWithdrawResponse
import id.co.bri.brimo.models.apimodel.response.rdnrevamp.RdnInquiryWithdrawResponse
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.disposables.Disposable

class DetailRdnRevampPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBRImoPrefRepository: BRImoPrefSource,
    apiSource: ApiSource,
    transaksiPfmSource: TransaksiPfmSource
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    mBRImoPrefRepository,
    apiSource,
    transaksiPfmSource
), IDetailRdnRevampPresenter<V> where V : IMvpView, V : IDetailRdnRevampView {
    private var urlTopup: String? = null
    private var urlWithdraw: String? = null
    private var urlHistory: String? = null

    override fun setUrlTopUp(urlTopup: String) {
        this.urlTopup = urlTopup
    }

    override fun setUrlWithdraw(urlWithdraw: String) {
        this.urlWithdraw = urlWithdraw
    }

    override fun setUrlHistoryRdn(urlHistory: String) {
        this.urlHistory = urlHistory
    }

    override fun getTopUpRdn(request: InquiryRdnRequest) {
        urlTopup.let { urlString ->
            view.showProgress()
            val seqNum = brImoPrefRepository.seqNumber
            val disposable: Disposable =
                apiSource.getData(urlString, request, seqNum)
                    .subscribeOn(schedulerProvider.io())
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView().hideProgress()
                            getView().onException(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            getView().hideProgress()
                            val detailResponse = response.getData(
                                InquiryRdnResponse::class.java
                            )
                            getView().onSuccessGetTopUp(detailResponse)
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView().hideProgress()
                            getView().onException(restResponse.desc)
                        }
                    })
            compositeDisposable.add(disposable)
        }
    }

    override fun getWithdrawRdn(request: RdnInquiryWithdrawRequest) {
        urlWithdraw.let { urlString ->
            view.showProgress()
            val seqNum = brImoPrefRepository.seqNumber
            val disposable: Disposable =
                apiSource.getData(urlString, request, seqNum)
                    .subscribeOn(schedulerProvider.io())
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView().hideProgress()
                            getView().onException(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            getView().hideProgress()
                            val detailResponse = response.getData(RdnInquiryWithdrawResponse::class.java)

                            when (detailResponse.inquiryStatus) {
                                RestResponse.ResponseCodeEnum.RC_SUCCESS.value -> getView().onSuccessGetWithdraw(detailResponse)
                                RestResponse.ResponseCodeEnum.RC_INSUFFICIENT_BALANCE_RDN.value -> getView().onExceptionInsufficientBalanceRdn(detailResponse)
                            }
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView().hideProgress()
                            when (restResponse.code) {
                                RestResponse.ResponseCodeEnum.RC_LIMIT_EXCEEDED.value, RestResponse.ResponseCodeEnum.RC_SM.value -> getView().onExceptionLimitExceed(restResponse.getData(GeneralResponse::class.java))
                                else -> getView().onException(restResponse.desc)
                            }
                        }
                    })
            compositeDisposable.add(disposable)
        }
    }

    override fun getWithdrawRdnHistoryList(request: RdnHistoryListWithdrawRequest) {
        urlHistory.let { urlString ->
            view.showProgress()
            val seqNum = brImoPrefRepository.seqNumber
            val disposable: Disposable =
                apiSource.getData(urlString, request, seqNum)
                    .subscribeOn(schedulerProvider.io())
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView().hideProgress()
                            getView().onException(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            getView().hideProgress()
                            val detailResponse = response.getData(RdnHistoryListWithdrawResponse::class.java)
                            getView().onSuccessGetHistoryListWithdraw(detailResponse)
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView().hideProgress()
                            getView().onException(restResponse.desc)
                        }
                    })
            compositeDisposable.add(disposable)
        }
    }

}