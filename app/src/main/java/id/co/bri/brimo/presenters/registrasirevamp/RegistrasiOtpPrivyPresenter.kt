package id.co.bri.brimo.presenters.registrasirevamp

import id.co.bri.brimo.contract.IPresenter.registrasirevamp.IRegistrasiOtpPrivyPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.registrasirevamp.IRegistrasiOtpPrivyView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimo.data.repository.category.CategoryPfmSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.config.AppConfig
import id.co.bri.brimo.domain.config.Constant

import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.RegisIdModel
import id.co.bri.brimo.models.apimodel.request.RegisSendOtpReq
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.registrasi.RegisOtpPrivyRes
import id.co.bri.brimo.models.apimodel.response.registrasi.RegisOtpResponse
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.Schedulers
import java.util.concurrent.TimeUnit

class RegistrasiOtpPrivyPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBRImoPrefRepository: BRImoPrefSource,
    apiSource: ApiSource,
    categoryPfmSource: CategoryPfmSource,
    transaksiPfmSource: TransaksiPfmSource,
    anggaranPfmSource: AnggaranPfmSource
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    mBRImoPrefRepository,
    apiSource,
    transaksiPfmSource
), IRegistrasiOtpPrivyPresenter<V> where V : IMvpView, V : IRegistrasiOtpPrivyView? {

    private var urlSend: String? = null
    private var urlResend: String? = null
    override fun setUrlSendOtp(url: String) {
        urlSend = url
    }

    override fun setUrlResendOtp(url: String) {
        urlResend = url
    }

    override fun sendOtp(regisSendOtpReq: RegisSendOtpReq) {
        if (urlSend != null && isViewAttached) {

            val seqNum = brImoPrefRepository.seqNumber
            view.showProgress()

            compositeDisposable.add(
                apiSource.getData(urlSend, regisSendOtpReq, seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView().hideProgress()
                            getView().deleteAllPin()
                            getView().onExceptionNoBackAction(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            getView().hideProgress()
                            if (response.code.equals(RestResponse.ResponseCodeEnum.RC_SUCCESS.value)) {
                                val regisOtpResponse = response.getData(RegisOtpPrivyRes::class.java)
                                brImoPrefRepository.saveUsername(regisOtpResponse.username)
                                brImoPrefRepository.saveTokenKey(regisOtpResponse.tokenKey)
                                brImoPrefRepository.saveUserType(Constant.IB_TYPE)
                                updateLoginFlag(true)
                                getView().onSuccessDashboard(regisOtpResponse)
                            } else {
                                getView().onSuccessLogin()
                            }
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView().hideProgress()
                            getView().deleteAllPin()
                            getView().onExceptionRevamp(restResponse.desc)
                        }
                    })
            )
        }
    }

    override fun resendOtp(deviceIdRequest: RegisIdModel) {
        if (urlResend != null && isViewAttached) {

            val seqNum = brImoPrefRepository.seqNumber
            view.showProgress()

            compositeDisposable.add(
                apiSource.getData(urlResend, deviceIdRequest, seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView().hideProgress()
                            getView().deleteAllPin()
                            getView().onExceptionNoBackAction(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            getView().hideProgress()
                            val regisOtpResponse =
                                response.getData(RegisOtpResponse::class.java)
                            getView().onSuccessResend(regisOtpResponse)
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView().hideProgress()
                            getView().deleteAllPin()
                            getView().onExceptionRevamp(restResponse.desc)
                        }
                    })
            )
        }
    }
}