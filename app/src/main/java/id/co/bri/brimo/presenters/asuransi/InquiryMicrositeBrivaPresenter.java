package id.co.bri.brimo.presenters.asuransi;

import java.util.concurrent.TimeUnit;

import id.co.bri.brimo.contract.IPresenter.asuransi.IInquiryMicrositeBrivaPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.asuransi.IInquiryMicrositeBrivaView;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.config.AppConfig;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.apimodel.request.InquiryBrilifeRequest;
import id.co.bri.brimo.models.apimodel.request.InquiryBrivaWebviewRequest;
import id.co.bri.brimo.models.apimodel.request.RevokeSessionRequest;
import id.co.bri.brimo.models.apimodel.response.GeneralInquiryResponse;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.presenters.MvpPresenter;
import io.reactivex.disposables.CompositeDisposable;

public class InquiryMicrositeBrivaPresenter<V extends IMvpView & IInquiryMicrositeBrivaView> extends MvpPresenter<V> implements IInquiryMicrositeBrivaPresenter<V> {

    private String url;
    private String url_inquiry_old;
    private String url_revoke;
    private String orderId;
    private String partnerId;
    private String type;
    private String urlLama;

    public InquiryMicrositeBrivaPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void getInitiateResource() {
        getView().onInitiateResourceSuccess(getBRImoPrefRepository().getUsername(), getBRImoPrefRepository().getTokenKey());
    }

    @Override
    public void setOrderId(String orderId2) {
        orderId = orderId2;
    }

    @Override
    public void revokeSession(RevokeSessionRequest request, boolean back) {
//        getView().onGoingRevoke();
        getView().showProgress();
        if (isViewAttached()) {
            String seqNum = getBRImoPrefRepository().getSeqNumber();
            getCompositeDisposable().add(getApiSource().getData(url_revoke, request, seqNum)
                    .subscribeOn(getSchedulerProvider().single())
                    .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                    .observeOn(getSchedulerProvider().mainThread())
                    .subscribeWith(new ApiObserver(getView(),seqNum) {
                        @Override
                        protected void onFailureHttp(String type) {
                            getView().hideProgress();
                            getView().onException(type);
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            if (back){
                                getView().hideProgress();
                                getView().onSuccessRevoke();
                            }else {
                                if (type.toLowerCase().equals("brimo_id")){
                                    getInquiry();
                                }else {
                                    getInquiryOld();
                                }
                            }
                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            getView().hideProgress();
                            if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue())) {
                                getView().onSessionEnd(restResponse.getDesc());
                            } else
                                getView().onException(restResponse.getDesc());
                        }
                    }));
        }
    }

    @Override
    public void getInquiry() {

//        getView().showProgress();
        if (isViewAttached()) {
            String seqNum = getBRImoPrefRepository().getSeqNumber();
            InquiryBrivaWebviewRequest req = new InquiryBrivaWebviewRequest(
                    partnerId,
                    orderId
            );
//            InquiryBrilifeRequest inquiryBrilifeRequest = new InquiryBrilifeRequest(orderId);
            getCompositeDisposable().add(getApiSource().getData(url, req, seqNum)
                    .subscribeOn(getSchedulerProvider().single())
                    .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                    .observeOn(getSchedulerProvider().mainThread())
                    .subscribeWith(new ApiObserver(getView(),seqNum) {
                        @Override
                        protected void onFailureHttp(String type) {
                            getView().hideProgress();
                            getView().onException(type);
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            getView().hideProgress();
                            GeneralInquiryResponse responsebriva = response.getData(GeneralInquiryResponse.class);
                            getView().onSuccess(responsebriva);
                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            getView().hideProgress();
                            if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue())) {
                                getView().onSessionEnd(restResponse.getDesc());
                            } else
                                getView().onException(restResponse.getDesc());
                        }
                    }));
        }
    }

    @Override
    public void getInquiryOld() {

//        getView().showProgress();
        if (isViewAttached()) {
            String seqNum = getBRImoPrefRepository().getSeqNumber();
//            InquiryBrivaWebviewRequest req = new InquiryBrivaWebviewRequest(
//                    partnerId,
//                    orderId
//            );
            InquiryBrilifeRequest inquiryBrilifeRequest = new InquiryBrilifeRequest(orderId);
            getCompositeDisposable().add(getApiSource().getData(url_inquiry_old, inquiryBrilifeRequest, seqNum)
                    .subscribeOn(getSchedulerProvider().single())
                    .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                    .observeOn(getSchedulerProvider().mainThread())
                    .subscribeWith(new ApiObserver(getView(),seqNum) {
                        @Override
                        protected void onFailureHttp(String type) {
                            getView().hideProgress();
                            getView().onException(type);
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            getView().hideProgress();
                            GeneralInquiryResponse responsebriva = response.getData(GeneralInquiryResponse.class);
                            getView().onSuccess(responsebriva);

                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            getView().hideProgress();
                            if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue())) {
                                getView().onSessionEnd(restResponse.getDesc());
                            }
                            else
                                getView().onException(restResponse.getDesc());
                        }
                    }));
        }
    }
    @Override
    public void getInquiryLama(String orderId) {
        getView().showProgress();
        if (isViewAttached()) {
            String seqNum = getBRImoPrefRepository().getSeqNumber();
            InquiryBrilifeRequest inquiryBrilifeRequest = new InquiryBrilifeRequest(orderId);
            getCompositeDisposable().add(getApiSource().getData(urlLama, inquiryBrilifeRequest, seqNum)
                    .subscribeOn(getSchedulerProvider().single())
                    .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                    .observeOn(getSchedulerProvider().mainThread())
                    .subscribeWith(new ApiObserver(getView(),seqNum) {
                        @Override
                        protected void onFailureHttp(String type) {
                            getView().hideProgress();
                            getView().onException(type);
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            getView().hideProgress();
                            GeneralInquiryResponse responsebriva = response.getData(GeneralInquiryResponse.class);
                            getView().onSuccess(responsebriva);

                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            getView().hideProgress();
                            if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue())) {
                                getView().onSessionEnd(restResponse.getDesc());
                            }
                            else
                                getView().onException(restResponse.getDesc());
                        }
                    }));
        }
    }

    @Override
    public void setUrlLama(String url) {
        this.urlLama = url;
    }

    @Override
    public void setUrl(String url) {
        this.url = url;
    }

    @Override
    public void setUrlOld(String url) {
        this.url_inquiry_old = url;
    }

    @Override
    public void setType(String type) {
        this.type = type;
    }

    @Override
    public void setUrlRevoke(String url) {
        this.url_revoke = url;
    }

    @Override
    public void setPartnerId(String url) {
        this.partnerId = url;
    }

    @Override
    public void onNullOrderId() {
        getView().onOrderIdNull("Order ID NULL");
    }

    @Override
    public void start() {
        super.start();

        getInitiateResource();
    }
}