package id.co.bri.brimo.presenters.britamarencanarevamp

import id.co.bri.brimo.contract.IPresenter.britamarencanarevamp.IInquiryPencairanRencanaPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.britamarencanarevamp.IInquiryPencairanRencanaView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimo.data.repository.category.CategoryPfmSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource

import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.dashboardrencanarevamp.KonfirmasiPencairanRencanaRequest
import id.co.bri.brimo.models.apimodel.response.GeneralConfirmationResponse
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.disposables.Disposable

class InquiryPencairanRencanaPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBRImoPrefRepository: BRImoPrefSource,
    apiSource: ApiSource,
    categoryPfmSource: CategoryPfmSource,
    transaksiPfmSource: TransaksiPfmSource,
    anggaranPfmSource: AnggaranPfmSource
) : MvpPresenter<V>(
    schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource,
    transaksiPfmSource
), IInquiryPencairanRencanaPresenter<V> where V : IMvpView, V : IInquiryPencairanRencanaView {

    private lateinit var mUrlKonfirmasi: String

    override fun setUrlKonfirmasi(urlKonfirmasi: String) {
        this.mUrlKonfirmasi = urlKonfirmasi
    }

    override fun getDataKonfirmasi(konfirmasiRequest: KonfirmasiPencairanRencanaRequest) {
        if (mUrlKonfirmasi.isEmpty() || !isViewAttached) return

        view.showProgress()

        val seqNum = brImoPrefRepository.seqNumber
        val disposable: Disposable =
            apiSource.getData(mUrlKonfirmasi, konfirmasiRequest, seqNum)
                .subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView().hideProgress()
                        getView().onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView().hideProgress()
                        val generalKonfirmasiResponse = response.getData(
                            GeneralConfirmationResponse::class.java
                        )

                        getView().onGetDataKonfirmasiSukses(generalKonfirmasiResponse)
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView().hideProgress()
                        if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value)) {
                            getView().onSessionEnd(restResponse.desc)
                        } else {
                            getView().onException(restResponse.desc)
                        }
                    }

                })
        compositeDisposable.add(disposable)
    }
}