package id.co.bri.brimo.presenters.onboardingrevamp

import id.co.bri.brimo.contract.IPresenter.onboardingrevamp.IOnboardingOtpPrivyPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.onboardingrevamp.IOnboardingOtpPrivyView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.config.AppConfig

import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.onboardingrevamp.OnboardingIdRequest
import id.co.bri.brimo.models.apimodel.request.onboardingrevamp.ValidateOtpPrivyReq
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.onboardingrevamp.OnboardingOtpRes
import id.co.bri.brimo.models.apimodel.response.onboardingrevamp.OnboardingReceiptResponse
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.Schedulers
import java.util.concurrent.TimeUnit

class OnboardingOtpPrivyPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBRImoPrefRepository: BRImoPrefSource,
    apiSource: ApiSource,
    transaksiPfmSource: TransaksiPfmSource
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    mBRImoPrefRepository,
    apiSource,
    transaksiPfmSource
), IOnboardingOtpPrivyPresenter<V> where V : IMvpView, V : IOnboardingOtpPrivyView {

    private var urlResend: String = ""
    private var urlSend: String = ""

    override fun setUrlSendOtp(url: String) {
        urlSend = url
    }

    override fun setUrlResendOtp(url: String) {
        urlResend = url
    }

    override fun sendOtp(request: ValidateOtpPrivyReq) {
        if (urlSend.isEmpty() || !isViewAttached) return

        val seqNum = brImoPrefRepository.seqNumber
        view.showProgress()

        compositeDisposable.add(
            apiSource.getData(urlSend, request, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT_ONBOARDING.toLong(), TimeUnit.SECONDS)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView().hideProgress()
                        getView().deleteAllPin()
                        getView().onExceptionNoBackAction(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView().hideProgress()
                        when (response.code) {
                            RestResponse.ResponseCodeEnum.RC_SUCCESS.value -> {
                                val receiptResponse =
                                    response.getData(OnboardingReceiptResponse::class.java)
                                getView().onSuccessConfirm(receiptResponse)
                            }
                            RestResponse.ResponseCodeEnum.RC_01.value -> getView().onGenerateAccount()
                            RestResponse.ResponseCodeEnum.RC_02.value -> getView().onGenerateUser()
                        }
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView().hideProgress()
                        getView().deleteAllPin()
                        if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_C1.value))
                            getView().onExceptionLogin()
                        else if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_C2.value))
                            getView().onExceptionRegistration()
                        else
                            getView().onExceptionRevamp(restResponse.desc)
                    }
                })
        )
    }

    override fun resendOtp() {
        if (urlResend.isEmpty() || !isViewAttached) return

        val seqNum = brImoPrefRepository.seqNumber
        val request = OnboardingIdRequest(brImoPrefRepository.deviceId)
        view.showProgress()

        compositeDisposable.add(
            apiSource.getData(urlResend, request, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView().hideProgress()
                        getView().deleteAllPin()
                        getView().onExceptionNoBackAction(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView().hideProgress()
                        val onboardingOtpRes =
                            response.getData(OnboardingOtpRes::class.java)
                        getView().onResend(onboardingOtpRes)
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView().hideProgress()
                        getView().deleteAllPin()
                        getView().onExceptionRevamp(restResponse.desc)
                    }
                })
        )
    }

    override fun getDeviceId(): String {
        return brImoPrefRepository.deviceId
    }
}