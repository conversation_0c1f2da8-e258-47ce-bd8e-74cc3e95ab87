package id.co.bri.brimo.presenters.brivarevamp

import id.co.bri.brimo.contract.IPresenter.brivarevamp.IFormBrivaRevampPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.base.IBaseFormRevampView
import id.co.bri.brimo.contract.IView.brivarevamp.IFormBrivaRevampView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.config.AppConfig
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.config.enumconfig.JourneyType
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.InquiryBrivaRequest
import id.co.bri.brimo.models.apimodel.request.UpdateSavedRequest
import id.co.bri.brimo.models.apimodel.response.*
import id.co.bri.brimo.presenters.base.BaseFormRevampPresenter
import io.reactivex.disposables.CompositeDisposable
import java.util.concurrent.TimeUnit

class FormBrivaRevampPresenter<V>(
        schedulerProvider: SchedulerProvider,
        compositeDisposable: CompositeDisposable,
        mBRImoPrefRepository: BRImoPrefSource,
        apiSource: ApiSource,
        transaksiPfmSource: TransaksiPfmSource,
) : BaseFormRevampPresenter<V>(
        schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource,
),
        IFormBrivaRevampPresenter<V> where V : IMvpView, V : IBaseFormRevampView, V : IFormBrivaRevampView {

    override fun setUpdateItem(
        url: String,
        savedResponse: SavedResponse,
        position: Int,
        type: Int,
        journeyType: JourneyType?
    ) {
        if (url.isEmpty() || !isViewAttached || onLoad) return

        onLoad = true
        val saveId = savedResponse.value
        val updateSavedRequest = UpdateSavedRequest(saveId, "")
        val seqNum = brImoPrefRepository.seqNumber
        view.showProgress()

        compositeDisposable.add(apiSource.getData(url, updateSavedRequest, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        onLoad = false
                        getView().hideProgress()
                        getView().onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        onLoad = false
                        getView().hideProgress()
                        getView().onSuccessUpdate(savedResponse, position, type)
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        onApiError(restResponse)
                    }
                })
        )
    }

    override fun getDataInquiry(briva: String, isFromFastMenu: Boolean) {
        if (mUrlInquiry.isEmpty() && !isViewAttached) return

        view.showProgress()

        val inquiryRequest = if (isFromFastMenu)
            InquiryBrivaRequest(
                    briva,
                    brImoPrefRepository.username,
                    brImoPrefRepository.tokenKey
            )
        else
            InquiryBrivaRequest(briva)

        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable.add(apiSource.getData(mUrlInquiry, inquiryRequest, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView().hideProgress()
                        getView().onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        //TO-DO onSuccess
                        getView().hideProgress()
                        val responsebriva = response.getData(
                                InquiryBrivaRevampResponse::class.java
                        )
                        getView().onSuccessGetInquiry(
                                responsebriva,
                                mUrlConfirm,
                                mUrlPayment
                        )
                    }

                        override fun onApiCallError(restResponse: RestResponse) {
                        getView().hideProgress()
                        if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value))
                            getView().onSessionEnd(restResponse.desc)
                        else if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_58.value))
                            getView().onException58(restResponse.desc)
                        else if(restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_59.value))
                            getView().onException59(restResponse.desc)
                        else if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_88_BILL_ALREADY_PAID.value))
                            getView().onException88(restResponse.desc)
                        else if (restResponse.code.equals(Constant.RE_EXPIRED_BILL))
                            getView().onExpiredBill(restResponse.desc)
                        else if (restResponse.code.equals(Constant.RE_BRIVA_POPUP_KCIC))
                            getView().onException60(restResponse.desc)
                        else getView().onException(restResponse.desc)
                    }
                })
        )
    }
}