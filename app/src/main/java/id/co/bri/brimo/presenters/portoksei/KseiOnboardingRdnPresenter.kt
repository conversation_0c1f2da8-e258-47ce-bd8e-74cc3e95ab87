package id.co.bri.brimo.presenters.portoksei

import id.co.bri.brimo.contract.IPresenter.portoksei.IKseiOnboardingPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.portoksei.IKseiOnboardingView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimo.data.repository.category.CategoryPfmSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.config.AppConfig

import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.OnboardingRDNSBNRequest
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.rdn.RdnOnBoardingResponse
import id.co.bri.brimo.models.apimodel.response.rdn.RdnOnCheckpointResponse
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.Schedulers
import java.util.concurrent.TimeUnit

class KseiOnboardingRdnPresenter<V>(schedulerProvider: SchedulerProvider?,
                                    compositeDisposable: CompositeDisposable?,
                                    mBRImoPrefRepository: BRImoPrefSource?, apiSource: ApiSource?,
                                    categoryPfmSource: CategoryPfmSource?,
                                    transaksiPfmSource: TransaksiPfmSource?,
                                    anggaranPfmSource: AnggaranPfmSource?) : MvpPresenter<V>(
    schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource
),
        IKseiOnboardingPresenter<V> where V : IMvpView?, V : IKseiOnboardingView {

    private var urlOnboarding: String? = null

    override fun setUrlOnBoarding(urlOnBoarding: String?) {
        urlOnboarding = urlOnBoarding
    }

    override fun onGetOnBoarding() {
        if (isViewAttached) {
            view.showProgress()
            val request = OnboardingRDNSBNRequest(brImoPrefRepository.firstRdn)
            val seqNum = brImoPrefRepository.seqNumber
            compositeDisposable.add(apiSource.getData(urlOnboarding, request, seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView().hideProgress()
                            getView().onException(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            if (response.code.equals(RestResponse.ResponseCodeEnum.RC_SUCCESS.value, ignoreCase = true)) {
                                val rdnOnBoardingResponse = response.getData(RdnOnBoardingResponse::class.java)
                                getView().onSuccessOnBoarding(rdnOnBoardingResponse)
                                getView().hideProgress()
                            } else if (response.code.equals(RestResponse.ResponseCodeEnum.RC_01.value, ignoreCase = true)) {
                                val rdnOnBoardingResponse = response.getData(RdnOnCheckpointResponse::class.java)
                                getView().onSuccessCheckPoint(rdnOnBoardingResponse)
                                getView().hideProgress()
                            }
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView().hideProgress()
                            if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value, ignoreCase = true))
                                getView().onSessionEnd(restResponse.desc)
                            else
                                getView().onException(restResponse.desc)
                        }
                    }))
        }
    }
}