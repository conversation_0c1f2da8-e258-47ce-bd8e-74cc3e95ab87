package id.co.bri.brimo.presenters.dplkrevamp

import id.co.bri.brimo.contract.IPresenter.dplkrevamp.IFormTopupDplkPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.base.IBaseFormRevampView
import id.co.bri.brimo.contract.IView.dplkrevamp.IFormTopupDplkView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.config.AppConfig
import id.co.bri.brimo.domain.config.enumconfig.JourneyType
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.dplkrevamp.DetailKlaimDplkRequest
import id.co.bri.brimo.models.apimodel.request.dplkrevamp.FormTopupDplkRequest
import id.co.bri.brimo.models.apimodel.response.ReceiptRevampResponse
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.SavedResponse
import id.co.bri.brimo.models.apimodel.response.dompetdigitalrevamp.InquiryDompetDigitalResponse
import id.co.bri.brimo.presenters.base.BaseFormRevampPresenter
import io.reactivex.disposables.CompositeDisposable
import java.util.concurrent.TimeUnit

class FormTopupDplkPresenter<V>(schedulerProvider: SchedulerProvider,
                                compositeDisposable: CompositeDisposable,
                                mBRImoPrefRepository: BRImoPrefSource, apiSource: ApiSource,
                                transaksiPfmSource: TransaksiPfmSource
) : BaseFormRevampPresenter<V>(
    schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource
),
    IFormTopupDplkPresenter<V> where V : IMvpView, V : IBaseFormRevampView, V : IFormTopupDplkView {
    private var mUrlDetailClaimBrifine: String = ""
    override fun setUpdateItem(
        url: String,
        savedResponse: SavedResponse,
        position: Int,
        type: Int,
        journeyType: JourneyType?
    ) {
        if (url.isEmpty() || !isViewAttached || onLoad) return

        onLoad = true
        val saveId = savedResponse.description
        val updateSavedRequest = FormTopupDplkRequest(saveId)
        val seqNum = brImoPrefRepository.seqNumber
        view.showProgress()

        compositeDisposable.add(apiSource.getData(url, updateSavedRequest, seqNum)
            .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
            .subscribeOn(schedulerProvider.io())
            .observeOn(schedulerProvider.mainThread())
            .subscribeWith(object : ApiObserver(view, seqNum) {
                override fun onFailureHttp(errorMessage: String) {
                    onLoad = false
                    getView().hideProgress()
                    getView().onException(errorMessage)
                }

                override fun onApiCallSuccess(response: RestResponse) {
                    onLoad = false
                    getView().hideProgress()
                    getView().onSuccessUpdate(savedResponse, position, type)
                }

                override fun onApiCallError(restResponse: RestResponse) {
                    onApiError(restResponse)
                }
            })
        )
    }

    override fun getClaimDplkUrl(url: String) {
        mUrlDetailClaimBrifine = url
    }

    override fun getDataInquiry(request : FormTopupDplkRequest) {
        if (mUrlInquiry.isEmpty() && !isViewAttached) return

        view.showProgress()


        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable.add(apiSource.getData(mUrlInquiry, request, seqNum)
            .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
            .subscribeOn(schedulerProvider.io())
            .observeOn(schedulerProvider.mainThread())
            .subscribeWith(object : ApiObserver(view, seqNum) {
                override fun onFailureHttp(errorMessage: String) {
                    getView().hideProgress()
                    getView().onException(errorMessage)
                }

                override fun onApiCallSuccess(response: RestResponse) {
                    //TO-DO onSuccess
                    getView().hideProgress()
                    val responsebriva = response.getData(
                        InquiryDompetDigitalResponse::class.java
                    )
                    getView().onSuccessGetInquiry(
                        responsebriva,
                        mUrlConfirm,
                        mUrlPayment
                    )
                }

                override fun onApiCallError(restResponse: RestResponse) {
                    onApiError(restResponse)
                }
            })
        )
    }

    override fun getDetailClaimBrifine(request: DetailKlaimDplkRequest) {
        if (isViewAttached) {
            view.showProgress()

            val seqNum = brImoPrefRepository.seqNumber
            val listConnectableObservable =
                apiSource.getData(mUrlDetailClaimBrifine,request, seqNum).subscribeOn(
                    schedulerProvider.io()
                ).timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .observeOn(schedulerProvider.mainThread())
                    .replay()
            compositeDisposable.add(
                listConnectableObservable
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(schedulerProvider.io())
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(type: String) {
                            getView().hideProgress()
                            getView().onException(type)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            view.hideProgress()
                            val data = response.getData(ReceiptRevampResponse::class.java)

                            getView().onSuccessGetHistoryDetailClaimDplk(data)

                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView().hideProgress()
                            getView().onException(restResponse.desc)
                        }

                    })
            )
            listConnectableObservable.connect()
        }
    }
}