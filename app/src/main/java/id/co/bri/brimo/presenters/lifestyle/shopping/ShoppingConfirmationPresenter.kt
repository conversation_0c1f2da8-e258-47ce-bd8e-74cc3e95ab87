package id.co.bri.brimo.presenters.lifestyle.shopping

import id.co.bri.brimo.contract.IPresenter.lifestyle.shopping.IShoppingConfirmationPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.lifestyle.shopping.IShoppingConfirmationView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserverKonfirmasi
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimo.data.repository.category.CategoryPfmSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource

import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.revampbriva.PayBrivaRevampRequest
import id.co.bri.brimo.models.apimodel.response.ExceptionResponse
import id.co.bri.brimo.models.apimodel.response.InquiryBrivaRevampResponse
import id.co.bri.brimo.models.apimodel.response.ReceiptRevampResponse
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.disposables.Disposable

class ShoppingConfirmationPresenter <V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBRImoPrefRepository: BRImoPrefSource,
    apiSource: ApiSource,
    categoryPfmSource: CategoryPfmSource,
    transaksiPfmSource: TransaksiPfmSource,
    anggaranPfmSource: AnggaranPfmSource
) : MvpPresenter<V>(
    schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource
),
    IShoppingConfirmationPresenter<V> where V : IMvpView, V : IShoppingConfirmationView {

    private lateinit var mUrlPay: String
    private lateinit var paymentRequest: Any

    override fun getDefaultSaldo() {
        var saldo = 0.0
        val saldoText = brImoPrefRepository.saldoRekeningUtama
        if (saldoText != "") {
            saldo = java.lang.Double.valueOf(saldoText)
        }
        val defaultAcc = brImoPrefRepository.accountDefault
        val saldoString = brImoPrefRepository.saldoRekeningUtamaString
        val saldoHold = brImoPrefRepository.saldoHold

        view.setDefaultSaldo(saldo, saldoString, defaultAcc, saldoHold)
    }

    override fun setUrlPayment(url: String) {
        mUrlPay = url
    }

    override fun getDataPaymentRevamp(
        pin: String, mInquiryBrivaRevampResponse: InquiryBrivaRevampResponse,
        account: String, saveAs: String, note: String, fromFastMenu: Boolean
    ) {
        if (mUrlPay.isEmpty() || !isViewAttached) return

        view.showProgress()
        val seqNum = brImoPrefRepository.seqNumber

        paymentRequest = PayBrivaRevampRequest(
            mInquiryBrivaRevampResponse.referenceNumber,
            account, mInquiryBrivaRevampResponse.amount.toString(), saveAs,
            note, pin, mInquiryBrivaRevampResponse.pfmCategory.toString()
        )

        val disposable: Disposable =
            apiSource.getData(mUrlPay, paymentRequest, seqNum) //function(param)
                .subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserverKonfirmasi(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String?) {
                        getView().hideProgress()
                        getView().onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView().hideProgress()

                        val receiptRevampResponse =
                            response.getData(ReceiptRevampResponse::class.java)

                        getView().onSuccessGetPaymentRevamp(receiptRevampResponse)
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView().hideProgress()
                        when(restResponse.code) {
                            RestResponse.ResponseCodeEnum.RC_01.value -> getView().onExceptionWithDialog(restResponse.desc)
                            RestResponse.ResponseCodeEnum.RC_SESSION_END.value -> getView().onSessionEnd(restResponse.desc)
                            RestResponse.ResponseCodeEnum.RC_HIT_EXCEEDED.value -> getView().onException06(restResponse.getData(ExceptionResponse::class.java))
                            RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.value -> getView().onExceptionTrxExpired(restResponse.desc)
                            RestResponse.ResponseCodeEnum.RC_94.value -> getView().onExceptionSnackbarBack(restResponse.desc)
                            RestResponse.ResponseCodeEnum.RC_99.value -> getView().onException99(restResponse.desc)
                            else -> getView().onException(restResponse.desc)
                        }
                    }
                })

        compositeDisposable.add(disposable)
    }
}