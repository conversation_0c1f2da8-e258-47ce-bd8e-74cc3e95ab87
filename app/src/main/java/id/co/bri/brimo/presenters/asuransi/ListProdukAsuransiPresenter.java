package id.co.bri.brimo.presenters.asuransi;

import java.util.concurrent.TimeUnit;

import id.co.bri.brimo.contract.IPresenter.asuransi.IListProdukAsuransiPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.asuransi.IListProdukAsuransiView;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.config.AppConfig;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.apimodel.request.WebviewBrilifeLamaRequest;
import id.co.bri.brimo.models.apimodel.request.WebviewBrilifeRequest;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.models.apimodel.response.WebviewBrilifeLamaResponse;
import id.co.bri.brimo.models.apimodel.response.WebviewBrilifeResponse;
import id.co.bri.brimo.presenters.MvpPresenter;
import io.reactivex.disposables.CompositeDisposable;

public class ListProdukAsuransiPresenter<V extends IMvpView & IListProdukAsuransiView> extends MvpPresenter<V> implements IListProdukAsuransiPresenter<V> {

    private String urlWebview;
    private String urlLama;

    public ListProdukAsuransiPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void getProduk(String idProduk) {

        if (isViewAttached()){
            getView().showProgress();

            String seqNum = getBRImoPrefRepository().getSeqNumber();

            WebviewBrilifeRequest request = new WebviewBrilifeRequest(idProduk);

            getCompositeDisposable().add(getApiSource().getData(urlWebview,request,seqNum)
            .subscribeOn(getSchedulerProvider().single())
            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
            .observeOn(getSchedulerProvider().mainThread())
            .subscribeWith(new ApiObserver(getView(),seqNum) {
                @Override
                protected void onFailureHttp(String type) {
                    getView().hideProgress();
                    getView().onException(type);
                }

                @Override
                protected void onApiCallSuccess(RestResponse response) {
                    getView().hideProgress();
                    WebviewBrilifeResponse webviewBrilifeResponse = response.getData(WebviewBrilifeResponse.class);
                    getView().onSuccessWebview(webviewBrilifeResponse);
                }

                @Override
                protected void onApiCallError(RestResponse restResponse) {
                    getView().hideProgress();
                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue())) {
                        getView().onSessionEnd(restResponse.getDesc());
                    }
                    else if(restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_12.getValue())){
                        getView().onException12(restResponse.getDesc());
                    }
                    else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.getValue())){
                        getView().onException12(restResponse.getDesc());
                    }
                    else
                        getView().onException(restResponse.getDesc());
                }
            }));
        }
    }

    @Override
    public void getProdukLama(String idProduk) {

        if (isViewAttached()){
            getView().showProgress();

            String seqNum = getBRImoPrefRepository().getSeqNumber();

            WebviewBrilifeLamaRequest request = new WebviewBrilifeLamaRequest(Integer.parseInt(idProduk));

            getCompositeDisposable().add(getApiSource().getData(urlLama,request,seqNum)
                    .subscribeOn(getSchedulerProvider().single())
                    .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                    .observeOn(getSchedulerProvider().mainThread())
                    .subscribeWith(new ApiObserver(getView(),seqNum) {
                        @Override
                        protected void onFailureHttp(String type) {
                            getView().hideProgress();
                            getView().onException(type);
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            getView().hideProgress();
                            WebviewBrilifeLamaResponse webviewBrilifeResponse = response.getData(WebviewBrilifeLamaResponse.class);
                            getView().onSuccessWebviewLama(webviewBrilifeResponse);
                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            getView().hideProgress();
                            if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue())) {
                                getView().onSessionEnd(restResponse.getDesc());
                            }
                            else if(restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_12.getValue())){
                                getView().onException12(restResponse.getDesc());
                            }
                            else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.getValue())){
                                getView().onException12(restResponse.getDesc());
                            }
                            else
                                getView().onException(restResponse.getDesc());
                        }
                    }));
        }
    }

    @Override
    public void setUrlProduk(String url) {
        urlWebview = url;
    }

    @Override
    public void setUrlProdukLama(String urlProdukLama) {
        urlLama = urlProdukLama;
    }

}