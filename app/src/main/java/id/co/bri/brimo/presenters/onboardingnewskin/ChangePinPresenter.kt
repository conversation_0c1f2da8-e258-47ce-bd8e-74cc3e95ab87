package id.co.bri.brimo.presenters.onboardingnewskin

import id.co.bri.brimo.contract.IPresenter.newskinonboarding.IChangePinPresenter
import id.co.bri.brimo.contract.IView.newskinonboarding.IChangePinView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.domain.config.AppConfig
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.onboardingrevamp.PinChangeRequest
import id.co.bri.brimo.models.apimodel.response.onboardingrevamp.PinCheckSuccessData
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.Schedulers
import java.util.concurrent.TimeUnit

class ChangePinPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    private val brImoPrefRepository: BRImoPrefSource,
    private val apiSource: ApiSource
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    brImoPrefRepository,
    apiSource,
    null
), IChangePinPresenter<V> where V : IChangePinView {

    private var urlValidatePin = ""
    private var errorMassage = ""
    override fun setChangePinUrl(url: String) {
        urlValidatePin = url
    }

    override fun onCheckPinCreate(referenceNumber: String) {
        if (!isViewAttached || urlValidatePin.isEmpty()) return

        view.showProgress()

        val seqNum = brImoPrefRepository.seqNumber

        val requestBody = PinChangeRequest(reference_number = referenceNumber)

        compositeDisposable.add(
            apiSource.getData(urlValidatePin, requestBody, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onApiCallSuccess(response: RestResponse) {
                        view.hideProgress()
                        val checkResponse = response.getData(PinCheckSuccessData::class.java)
                        getView().onPinValid(checkResponse.referenceNumber, code = response.code)

                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        view.hideProgress()
                        val res = restResponse.getData(PinCheckSuccessData::class.java)
                        getView().onPinCheckFailed(res, restResponse.code)
                        errorMassage = restResponse.desc
                    }

                    override fun onFailureHttp(errorMessage: String) {
                        view.hideProgress()
                        getView().onFailure(true)
                    }
                })
        )
    }
}