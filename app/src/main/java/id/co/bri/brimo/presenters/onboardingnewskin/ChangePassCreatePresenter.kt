package id.co.bri.brimo.presenters.onboardingnewskin

import id.co.bri.brimo.contract.IPresenter.newskinonboarding.IChangePassCreatePresenter
import id.co.bri.brimo.contract.IView.newskinonboarding.IChangePassCreateView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.domain.config.AppConfig
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.onboardingrevamp.PassChangeCreateRequest
import id.co.bri.brimo.models.apimodel.request.onboardingrevamp.RequestChangePass
import id.co.bri.brimo.models.apimodel.response.onboardingrevamp.PassCheckData
import id.co.bri.brimo.models.apimodel.response.onboardingrevamp.PassCheckResponse
import id.co.bri.brimo.models.apimodel.response.onboardingrevamp.PassSubmitSuccessData
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.Schedulers
import java.util.concurrent.TimeUnit

class ChangePassCreatePresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    private val brImoPrefRepository: BRImoPrefSource,
    private val apiSource: ApiSource
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    brImoPrefRepository,
    apiSource,
    null
), IChangePassCreatePresenter<V> where V : IChangePassCreateView {

    private var urlValidatePass = ""
    private var urlSubmitPass = ""
    override fun setValidatePassUrl(url: String) {
        urlValidatePass = url
    }

    override fun setSubmitPassUrl(url: String) {
        urlSubmitPass = url
    }

    override fun onCheckPassCreate(pass: String, referenceNumber: String) {
        if (!isViewAttached || urlValidatePass.isEmpty()) return

//        view.showProgress()

        val seqNum = brImoPrefRepository.seqNumber

        val requestBody =
            PassChangeCreateRequest(new_password = pass, reference_number = referenceNumber)

        compositeDisposable.add(
            apiSource.getData(urlValidatePass, requestBody, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onApiCallSuccess(response: RestResponse) {
                        view.hideProgress()
                        val checkResponse = response.getData(PassCheckResponse::class.java)
                        getView().onPassValid(checkResponse, code = response.code)

                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        view.hideProgress()
                        val res = restResponse.getData(PassCheckData::class.java)
                        getView().onPassCheckFailed(res, restResponse.code)
                    }

                    override fun onFailureHttp(errorMessage: String) {
                        view.hideProgress()
                        getView().onFailureCheck(errorMessage)
                    }
                })
        )
    }

    override fun onCreatePass(referenceNumber: String) {
        if (!isViewAttached || urlSubmitPass.isEmpty()) return

        view.showProgress()

        val seqNum = brImoPrefRepository.seqNumber

        val requestBody = RequestChangePass(reference_number = referenceNumber)

        compositeDisposable.add(
            apiSource.getData(urlSubmitPass, requestBody, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onApiCallSuccess(response: RestResponse) {
                        view.hideProgress()
                        val checkResponse = response.getData(PassSubmitSuccessData::class.java)
                        getView().onSubmitSuccess(checkResponse, code = response.code)

                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        view.hideProgress()
                        val res = restResponse.getData(PassSubmitSuccessData::class.java)
                        getView().onPassSubmitFailed(res, restResponse.code)
                    }

                    override fun onFailureHttp(errorMessage: String) {
                        view.hideProgress()
                        getView().onFailureSubmit(errorMessage)
                    }
                })
        )
    }
}


