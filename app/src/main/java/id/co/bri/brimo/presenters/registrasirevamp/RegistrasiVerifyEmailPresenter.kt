package id.co.bri.brimo.presenters.registrasirevamp

import com.google.gson.Gson
import id.co.bri.brimo.contract.IPresenter.registrasirevamp.IRegistrasiVerifyEmailPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.registrasirevamp.IRegistrasiVerifyEmailView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimo.data.repository.category.CategoryPfmSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.config.AppConfig
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.RegisIdModel
import id.co.bri.brimo.models.apimodel.request.registrasi.RegisEmailRequest
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.registrasi.RegisEmailResponse
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.Schedulers
import java.util.concurrent.TimeUnit

class RegistrasiVerifyEmailPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBRImoPrefRepository: BRImoPrefSource,
    apiSource: ApiSource,
    categoryPfmSource: CategoryPfmSource,
    transaksiPfmSource: TransaksiPfmSource,
    anggaranPfmSource: AnggaranPfmSource
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    mBRImoPrefRepository,
    apiSource,
    transaksiPfmSource
), IRegistrasiVerifyEmailPresenter<V> where V : IMvpView, V : IRegistrasiVerifyEmailView? {

    private var urlResend: String? = null
    private var urlCheckProgress: String? = null

    override fun setUrlResend(url: String) {
        urlResend = url
    }

    override fun setUrlCheckProgress(url: String) {
        urlCheckProgress = url
    }

    override fun sendResend(regisEmailRequest: RegisEmailRequest) {
        if (urlResend != null && isViewAttached) {
            val seqNum = brImoPrefRepository.seqNumber
            view.showProgress()

            compositeDisposable.add(
                apiSource.getData(urlResend, regisEmailRequest, seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView().hideProgress()
                            getView().onExceptionNoBackAction(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            getView().hideProgress()
                            val regisEmailResponse =
                                response.getData(RegisEmailResponse::class.java)
                            getView().onSuccessGetResend(regisEmailResponse)
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView().hideProgress()
                            if(restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_STATUS_NOT_MATCH.value))
                                getView().onExceptionStatusNotMatch()
                            else getView().onExceptionRevamp(restResponse.desc)
                        }
                    })
            )
        }
    }

    override fun sendCheckProgress() {
        if (urlCheckProgress != null && isViewAttached) {
            val seqNum = brImoPrefRepository.seqNumber
            val regisIdModel = RegisIdModel(brImoPrefRepository.deviceId)

            view.showProgress()

            compositeDisposable.add(
                apiSource.getData(urlCheckProgress, regisIdModel, seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView().hideProgress()
                            getView().onExceptionNoBackAction(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            getView().hideProgress()
                            val regisEmailResponse =
                                response.getData(RegisEmailResponse::class.java)
                            if (regisEmailResponse.status == 3) {
                                getView().onSuccessGetResend(regisEmailResponse)
                            } else
                                getView().onSuccessProgress(Gson().toJson(response.data))
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView().hideProgress()
                            getView().onExceptionRevamp(restResponse.desc)
                        }
                    })
            )
        }
    }

}