package id.co.bri.brimo.presenters.registrasiib.nds;

import android.util.Log;

import java.security.NoSuchAlgorithmException;
import java.util.concurrent.TimeUnit;

import id.co.bri.brimo.contract.IPresenter.registrasiib.nds.IRegisUsernameNdsPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.registrasiib.nds.IRegisUsernameNdsView;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.config.AppConfig;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.DeviceIDHelper;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.apimodel.request.StatLinkRequest;
import id.co.bri.brimo.models.apimodel.response.RegisNdsFailedResponse;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.presenters.MvpPresenter;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.schedulers.Schedulers;

import static id.co.bri.brimo.data.preference.BRImoPrefRepository.VERSION_APP;

public class RegisUsernameNdsPresenter<V extends IMvpView & IRegisUsernameNdsView> extends MvpPresenter<V> implements IRegisUsernameNdsPresenter<V> {

    protected String url;
    protected boolean isLoad = false;

    public RegisUsernameNdsPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void start() {
        super.start();

        if (!isLoad) {
            isUpdateVersionApps();
            checkDevice();
        }
    }

    @Override
    public void checkDevice() {
        if (isLoad)
            return;

        isLoad = true;

        if (getBRImoPrefRepository().isContains(Constant.USER_TYPE))
            getBRImoPrefRepository().saveUserExist(true);

        if (!getBRImoPrefRepository().isContains(Constant.DEVICE_ID)) {
            generateDeviceId();
        }
    }

    @Override
    public void generateDeviceId() {
        try {
            getBRImoPrefRepository().saveDeviceId(DeviceIDHelper.setDeviceID());
            getBRImoPrefRepository().deleteSeqNumber();

        } catch (NoSuchAlgorithmException e) {
            if (!GeneralHelper.isProd())
                Log.e("TAG", ": ", e);
        }
    }

    protected boolean isUpdateVersionApps() {
        String versionId = GeneralHelper.getLastAppVersionCode();

        if (getBRImoPrefRepository().isContains(VERSION_APP)) {
            String savedVersion = getBRImoPrefRepository().getVersionApp();
            if (!versionId.equals(savedVersion)) {
                getBRImoPrefRepository().saveVersionApp(versionId);
                return true;
            } else {
                getBRImoPrefRepository().saveVersionApp(versionId);
                return false;
            }
        } else {
            getBRImoPrefRepository().saveVersionApp(versionId);
            return true;
        }
    }

    @Override
    public void setUrlStatus(String url) {
        this.url = url;
    }

    @Override
    public void getStatusLink(StatLinkRequest statLinkRequest) {
        if (getView() != null) {
            getView().showProgress();

            String seqNum = getBRImoPrefRepository().getSeqNumber();

            getCompositeDisposable().add(getApiSource().getMagicLinkRegisNds(statLinkRequest, seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                    .subscribeOn(Schedulers.single())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(new ApiObserver(getView(),seqNum) {
                        @Override
                        protected void onFailureHttp(String errorMessage) {
                            getView().hideProgress();
                            getView().onException(errorMessage);
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            getView().hideProgress();

                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            getView().hideProgress();
                            if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.getValue())) {
                                RegisNdsFailedResponse response = restResponse.getData(RegisNdsFailedResponse.class);
                                getView().onExceptionRegisExist(response);
                            } else
                                getView().onException(restResponse.getDesc());
                        }
                    }));
        }
    }
}