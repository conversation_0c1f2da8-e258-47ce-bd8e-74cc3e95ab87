package id.co.bri.brimo.presenters.ubahpin;

import java.util.concurrent.TimeUnit;

import javax.inject.Inject;

import id.co.bri.brimo.contract.IPresenter.ubahpin.IUbahPinBaru2Presenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.ubahpin.IUbahPinBaru2View;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.config.AppConfig;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.apimodel.request.UbahPinOtpRequest;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.models.apimodel.response.UbahPasswordBaruResponse;
import id.co.bri.brimo.models.apimodel.response.UbahPinBaruResponse;
import id.co.bri.brimo.presenters.MvpPresenter;
import io.reactivex.disposables.CompositeDisposable;

public class UbahPinBaru2Presenter<V extends IMvpView & IUbahPinBaru2View> extends MvpPresenter<V> implements IUbahPinBaru2Presenter<V> {

    private String url = "";
    private String urlConfirm = "";

    @Inject
    public UbahPinBaru2Presenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }


    @Override
    public void setUrl(String url) {
        this.url = url;
    }

    @Override
    public void setUrlConfirm(String url) {
        this.urlConfirm = url;
    }

    @Override
    public void confirmPin(String pin, String refNumber) {
        if (getView() == null) {
            return;
        }

        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        UbahPinOtpRequest ubahPinOtpRequest = new UbahPinOtpRequest(pin, refNumber);

        if (isViewAttached()) {
            getCompositeDisposable().add(
                    getApiSource().getData(urlConfirm, ubahPinOtpRequest, seqNum)
                            .subscribeOn(getSchedulerProvider().single())
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .observeOn(getSchedulerProvider().mainThread())
                            .subscribeWith(new ApiObserver(getView(), seqNum) {
                                @Override
                                protected void onFailureHttp(String type) {
                                    getView().hideProgress();
                                    getView().onException(type);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    getView().hideProgress();
                                    UbahPasswordBaruResponse ubahPinResponse = response.getData(UbahPasswordBaruResponse.class);
                                    getView().onSuccessConfirm(ubahPinResponse);
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().hideProgress();
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue())) {
                                        getView().onSessionEnd(restResponse.getDesc());
                                    } else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.getValue())) {
                                        getView().onException93(restResponse.getDesc());
                                    } else {
                                        getView().onError();
                                    }
                                }
                            })
            );
        }

    }

    @Override
    public void ubahPin(String pin, String refnumber) {
        if (getView() == null) {
            return;
        }

        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        UbahPinOtpRequest ubahPinOtpRequest = new UbahPinOtpRequest(pin, refnumber);

        if (isViewAttached()) {
            getCompositeDisposable().add(
                    getApiSource().getData(url, ubahPinOtpRequest, seqNum)
                            .subscribeOn(getSchedulerProvider().single())
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .observeOn(getSchedulerProvider().mainThread())
                            .subscribeWith(new ApiObserver(getView(), seqNum) {
                                @Override
                                protected void onFailureHttp(String type) {
                                    getView().hideProgress();
                                    getView().onException(type);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    getView().hideProgress();
                                    UbahPinBaruResponse ubahPinResponse = response.getData(UbahPinBaruResponse.class);
                                    getView().onSuccess(ubahPinResponse);
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().hideProgress();
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue())) {
                                        getView().onSessionEnd(restResponse.getDesc());
                                    } else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.getValue())) {
                                        getView().onException93(restResponse.getDesc());
                                    } else {
                                        getView().onError();
                                    }
                                }
                            })
            );
        }
    }


}