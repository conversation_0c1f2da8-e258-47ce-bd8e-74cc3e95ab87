package id.co.bri.brimo.presenters.signal

import id.co.bri.brimo.contract.IPresenter.signal.IFormSignalPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.signal.IFormSignalView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimo.data.repository.category.CategoryPfmSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource

import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.signal.InquirySignalRequest
import id.co.bri.brimo.models.apimodel.response.InquiryBrivaRevampResponse
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.signal.FormSignalResponse
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.disposables.Disposable

class FormSignalPresenter<V>(schedulerProvider: SchedulerProvider?, compositeDisposable: CompositeDisposable?, mBRImoPrefRepository: BRImoPrefSource?, apiSource: ApiSource?, categoryPfmSource: CategoryPfmSource?, transaksiPfmSource: TransaksiPfmSource?, anggaranPfmSource: AnggaranPfmSource?) :
    MvpPresenter<V>(
        schedulerProvider,
        compositeDisposable,
        mBRImoPrefRepository,
        apiSource,
        transaksiPfmSource
    ),

    IFormSignalPresenter<V> where V : IMvpView, V : IFormSignalView {
    var mUrlForm: String = ""
    var mUrlInquiry: String = ""

    override fun setUrlForm(urlForm: String) {
        mUrlForm = urlForm
    }

    override fun setUrlInquiry(urlInquiry: String) {
        mUrlInquiry = urlInquiry
    }

    override fun getDataForm() {
        if (mUrlForm.isEmpty() || !isViewAttached) {
            return
        }
        view?.showSkeleton()
        val seqNum = brImoPrefRepository.seqNumber
        compositeDisposable
            .add(apiSource.getDataTanpaRequest(mUrlForm, seqNum)
                    .subscribeOn(schedulerProvider.io())
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView()?.hideSkeleton()
                            getView()?.onException(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            getView()?.hideSkeleton()
                            val formSignalResponse : FormSignalResponse = response.getData(FormSignalResponse::class.java)
                            getView().onSuccessGetForm(formSignalResponse)
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView().hideSkeleton()
                            if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value, ignoreCase = true)){
                                getView().onSessionEnd(restResponse.desc)
                            } else {
                                getView().onException(restResponse.desc)
                            }
                        }
                    })
            )
    }

    override fun getInquiry(paymentNumber : String) {
        if (mUrlInquiry.isEmpty() || !isViewAttached) {
            return
        }

        if (view != null) {
            val inquirySignalRequest = InquirySignalRequest(paymentNumber = paymentNumber)
            view!!.showProgress()
            val seqNum = brImoPrefRepository.seqNumber
            val disposable: Disposable = apiSource.getData(mUrlInquiry, inquirySignalRequest, seqNum) //function(param)
                .subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView()!!.hideProgress()
                        getView()!!.onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView()!!.hideProgress()

                        val generalConfirmationResponse = restResponse.getData(InquiryBrivaRevampResponse::class.java)
                        getView()!!.onSuccessInquiry(generalConfirmationResponse)
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView()!!.hideProgress()
                        if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value, ignoreCase = true))
                            getView()!!.onSessionEnd(restResponse.desc)
                        else
                            getView()!!.onException(restResponse.desc)
                    }
                })
            compositeDisposable.add(disposable)
        }
    }




}