@file:JvmName("Extensions")

package id.co.bri.brimo.util

import android.app.Activity
import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.content.Intent
import android.content.res.ColorStateList
import android.graphics.Paint
import android.os.Build
import android.os.Parcelable
import android.text.InputType
import android.util.Log
import android.widget.EditText
import com.google.android.material.textfield.TextInputLayout
import id.co.bri.brimo.util.custom_numpad.CustomNumpadHelper
import id.co.bri.brimo.util.custom_numpad.NumpadType
import java.util.Calendar
import android.os.Bundle
import android.view.ActionMode
import android.view.Menu
import android.view.MenuItem
import android.view.View
import android.widget.TextView
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import id.co.bri.brimo.domain.config.AppConfig
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import io.reactivex.Observable
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.observers.DisposableObserver
import java.io.InputStreamReader
import java.text.NumberFormat
import java.util.Locale
import java.util.concurrent.TimeUnit

inline fun <reified T : Parcelable> Intent.getParcelableCompat(key: String): T? = when {
    Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU -> getParcelableExtra(key, T::class.java)
    else -> @Suppress("DEPRECATION") getParcelableExtra(key) as? T
}

inline fun <reified T : Parcelable> Intent.getParcelableArrayCompat(key: String): List<T> = when {
    Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU ->
        getParcelableArrayExtra(key, T::class.java)?.toList().orEmpty()

    else -> @Suppress("DEPRECATION") (getParcelableArrayExtra(key) as? Array<T>)?.toList().orEmpty()
}


inline fun <reified T : Parcelable> Bundle.getParcelableCompat(key: String): T? = when {
    Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU -> getParcelable(key, T::class.java)
    else -> @Suppress("DEPRECATION") getParcelable(key) as? T
}

inline fun <reified T : Parcelable> Bundle.getParcelableArrayCompat(key: String): List<T> = when {
    Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU ->
        getParcelableArray(key, T::class.java)?.toList().orEmpty()

    else -> @Suppress("DEPRECATION") (getParcelableArray(key) as? Array<T>)?.toList().orEmpty()
}

// Enum representing different time periods of the day
enum class TimePeriod {
    EARLY_MORNING, // 00:01 - 04:59
    MORNING,       // 05:00 - 09:59
    NOON,          // 10:00 - 10:59
    AFTERNOON,     // 11:00 - 14:59
    EVENING,       // 15:00 - 17:59
    NIGHT,         // 18:00 - 18:29
    LATE_NIGHT     // 18:30 - 00:00
}

// Extension function to map the current time (hour and minute) to a TimePeriod
fun Calendar.toTimePeriod(): TimePeriod {
    val hour = get(Calendar.HOUR_OF_DAY)
    val minute = get(Calendar.MINUTE)

    return when {
        // Between 00:01 and 04:59
        (hour == 0 && minute in 1..59) || (hour in 1..4) -> TimePeriod.EARLY_MORNING
        // Between 05:00 and 09:59
        hour in 5..9 -> TimePeriod.MORNING
        // Between 10:00 and 10:59
        hour == 10 -> TimePeriod.NOON
        // Between 11:00 and 14:59
        hour in 11..14 -> TimePeriod.AFTERNOON
        // Between 15:00 and 17:59
        hour in 15..17 -> TimePeriod.EVENING
        // Between 18:00 and 18:29
        hour == 18 && minute in 0..29 -> TimePeriod.NIGHT
        // Between 18:30 and 00:00 (also includes 00:00 exactly)
        else -> TimePeriod.LATE_NIGHT
    }
}

fun EditText.attachNumpad(
    activity: Activity,
    type: NumpadType,
    onPinComplete: (String) -> Unit,
    onFocusChanged: ((Boolean) -> Unit)? = null
): CustomNumpadHelper {
    val helper = CustomNumpadHelper(activity, this, type, onPinComplete)
    inputType = InputType.TYPE_NULL
    showSoftInputOnFocus = false

//    setTextIsSelectable(false)
//    customSelectionActionModeCallback = object: ActionMode.Callback {
//        override fun onCreateActionMode(mode: ActionMode?, menu: Menu?): Boolean = true
//
//        override fun onPrepareActionMode(
//            mode: ActionMode?,
//            menu: Menu?
//        ): Boolean = false
//
//        override fun onActionItemClicked(mode: ActionMode?, item: MenuItem?): Boolean = false
//
//        override fun onDestroyActionMode(mode: ActionMode?) {}
//    }

    setOnFocusChangeListener { _, hasFocus ->
        Log.e("Numpad", "Focus changed: $hasFocus on $id")
        if (hasFocus) {
            helper.showKeyboard()
        }
        onFocusChanged?.invoke(hasFocus)
    }

    return helper
}

fun TextInputLayout.attachNumpad(
    activity: Activity,
    type: NumpadType,
    onPinComplete: (String) -> Unit,
    onAttached: ((CustomNumpadHelper) -> Unit)? = null,
    onFocusChanged: ((Boolean) -> Unit)? = null
) {
    this.post {
        val editText = this.editText
        if (editText == null) {
            Log.e("Numpad", "❌ EditText belum tersedia di TextInputLayout $id")
            return@post
        }

        val helper = editText.attachNumpad(activity, type, onPinComplete, onFocusChanged)
        onAttached?.invoke(helper)
    }
}

fun <T> Observable<T>.subscribeWithObserver(
    compositeDisposable: CompositeDisposable,
    schedulerProvider: SchedulerProvider,
    createObserver: () -> DisposableObserver<Any>
) {
    val disposable = this
        .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
        .subscribeOn(schedulerProvider.io())
        .observeOn(schedulerProvider.mainThread())
        .subscribeWith(createObserver())

    compositeDisposable.add(disposable)
}

fun Context.copyToClipboard(label: String = "Copied Text", text: String) {
    val clipboard = getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
    val clip = ClipData.newPlainText(label, text)
    clipboard.setPrimaryClip(clip)
}

fun TextView.textWithStrikethrough(value: String) {
    paintFlags = paintFlags or Paint.STRIKE_THRU_TEXT_FLAG
    text = value
}

fun View.fadeIn(duration: Long = 300) {
    this.apply {
        alpha = 0f
        animate()
            .alpha(1f)
            .setDuration(duration)
            .setListener(null)
    }
}

fun EditText.applyStateColor(enabledColor: Int, disabledColor: Int) {
    val states = arrayOf(
        intArrayOf(-android.R.attr.state_enabled), // Disabled
        intArrayOf(android.R.attr.state_enabled)   // Enabled
    )

    val colors = intArrayOf(
        disabledColor,
        enabledColor
    )

    val colorStateList = ColorStateList(states, colors)
    setTextColor(colorStateList)
}

fun Int.toRupiah(): String {
    val formatter = NumberFormat.getCurrencyInstance(Locale("in", "ID"))
    return formatter.format(this).replace(",00", "")
}

inline fun <reified T> parseFromJson(context: Context, assetPath: String): T {
    context.assets.open(assetPath).use { inputStream ->
        InputStreamReader(inputStream).use { reader ->
            val gson = Gson()
            val type = object : TypeToken<T>() {}.type
            return gson.fromJson(reader, type)
        }
    }
}