package id.co.bri.brimo.presenters.bripoin;

import java.util.concurrent.TimeUnit;

import id.co.bri.brimo.contract.IPresenter.bripoin.IDetailAkunPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.bripoin.IDetailAkunView;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.config.AppConfig;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.apimodel.request.PinRequest;
import id.co.bri.brimo.models.apimodel.response.BripoinResponse;
import id.co.bri.brimo.models.apimodel.response.BripoinWebviewRes;
import id.co.bri.brimo.models.apimodel.response.CheckPengkinianResponse;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.presenters.MvpPresenter;
import io.reactivex.disposables.CompositeDisposable;

public class DetailAkunPresenter<V extends IMvpView & IDetailAkunView> extends MvpPresenter<V>
        implements IDetailAkunPresenter<V> {

    protected String url;
    protected String urlWebView;
    protected String urlEditEmail;
    protected String urlPengkinianData;

    public DetailAkunPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable,
                               BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource,
                               TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void setUrl(String url) {
        this.url = url;
    }

    @Override
    public void setUrlWebView(String urlWebView) {
        this.urlWebView = urlWebView;
    }

    @Override
    public void setUrlEditEmail(String urlEditEmail) {
        this.urlEditEmail = urlEditEmail;
    }

    @Override
    public void setUrlPengkinianData(String urlPengkinianData) {
        this.urlPengkinianData = urlPengkinianData;
    }

    @Override
    public void onLoadBripoin() {
        if (isViewAttached()) {
            String seqNum = getBRImoPrefRepository().getSeqNumber();
            getCompositeDisposable().add(
                    getApiSource().getDataForm(url, seqNum)
                            .subscribeOn(getSchedulerProvider().io())
                            .observeOn(getSchedulerProvider().mainThread())
                            .subscribeWith(new ApiObserver(getView(), seqNum) {

                                @Override
                                protected void onFailureHttp(String errorMessage) {
                                    getView().onExceptionBripoin(errorMessage);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    BripoinResponse briPointResponse = response.getData(BripoinResponse.class);
                                    getView().onSuccessLoadBripoin(briPointResponse);
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                        getView().onSessionEnd(restResponse.getDesc());
                                    else
                                        getView().onExceptionBripoin(restResponse.getDesc());

                                }
                            })
            );
        }
    }

    @Override
    public void onGetWebViewBripoin(String pin) {
        if (isViewAttached()) {
            getView().showProgress();

            PinRequest pinRequest = new PinRequest(pin);
            String seqNum = getBRImoPrefRepository().getSeqNumber();

            getCompositeDisposable().add(getApiSource().getData(urlWebView, pinRequest, seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                    .subscribeOn(getSchedulerProvider().io())
                    .observeOn(getSchedulerProvider().mainThread())
                    .subscribeWith(new ApiObserver(getView(), seqNum) {

                        @Override
                        protected void onFailureHttp(String errorMessage) {
                            getView().hideProgress();
                            getView().onException(errorMessage);
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            getView().hideProgress();
                            BripoinWebviewRes bripoinWebviewRes = response.getData(BripoinWebviewRes.class);
                            getView().onSuccessGetBripoin(bripoinWebviewRes);
                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            getView().hideProgress();
                            if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                getView().onSessionEnd(restResponse.getDesc());
                            else
                                getView().onException12Web(restResponse.getDesc());
                        }
                    }));
        }
    }

    @Override
    public void onLoadEditEmail(String pin) {
        if (isViewAttached()) {

            getView().showProgress();

            PinRequest pinRequest = new PinRequest(pin);
            String seqNum = getBRImoPrefRepository().getSeqNumber();

            getCompositeDisposable().add(
                    getApiSource().getData(urlEditEmail, pinRequest, seqNum)
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .subscribeOn(getSchedulerProvider().io())
                            .observeOn(getSchedulerProvider().mainThread())
                            .subscribeWith(new ApiObserver(getView(), seqNum) {

                                @Override
                                protected void onFailureHttp(String errorMessage) {
                                    getView().hideProgress();
                                    getView().onException(errorMessage);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    getView().hideProgress();
                                    getView().onSuccessEditEmail();
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().hideProgress();
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                        getView().onSessionEnd(restResponse.getDesc());
                                    else
                                        getView().onException(restResponse.getDesc());
                                }
                            })
            );
        }
    }

    @Override
    public void onCheckPengkinianData() {
        if (urlPengkinianData != null && isViewAttached()) {

            getView().showProgress();
            String seqNum = getBRImoPrefRepository().getSeqNumber();

            getCompositeDisposable().add(
                    getApiSource().getDataTanpaRequest(urlPengkinianData, seqNum)
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .subscribeOn(getSchedulerProvider().io())
                            .observeOn(getSchedulerProvider().mainThread())
                            .subscribeWith(new ApiObserver(getView(), seqNum) {

                                @Override
                                protected void onFailureHttp(String errorMessage) {
                                    getView().hideProgress();
                                    getView().onException(errorMessage);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    getView().hideProgress();
                                    CheckPengkinianResponse checkResponse = response.getData(CheckPengkinianResponse.class);
                                    if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SUCCESS.getValue())) {
                                        getView().onSuccessCheckPengkinian(checkResponse);
                                    } else getView().onFailedCheckPengkinian(checkResponse);
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().hideProgress();
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                        getView().onSessionEnd(restResponse.getDesc());
                                    else
                                        getView().onException(restResponse.getDesc());
                                }
                            })
            );
        }
    }

}