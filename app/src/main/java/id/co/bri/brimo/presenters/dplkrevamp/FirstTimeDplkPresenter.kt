package id.co.bri.brimo.presenters.dplkrevamp

import id.co.bri.brimo.contract.IPresenter.dplkrevamp.IFirstTimeDPLKPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.dplkrevamp.IFirstTimeDplkView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.config.AppConfig
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.dplkrevamp.CheckStatusDplkRequest
import id.co.bri.brimo.models.apimodel.request.dplkrevamp.ListDplkRequest
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.dplkrevamp.ListPilihBrifineResponse
import id.co.bri.brimo.models.apimodel.response.dplkrevamp.PaymentFtuDplkResponse
import id.co.bri.brimo.models.apimodel.response.dplkrevamp.PersonalDataDplkResponse
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.Schedulers
import java.util.concurrent.TimeUnit

class FirstTimeDplkPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBRImoPrefRepository: BRImoPrefSource, apiSource: ApiSource,
    transaksiPfmSource: TransaksiPfmSource,
) : MvpPresenter<V>(
    schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource,
    transaksiPfmSource
),
    IFirstTimeDPLKPresenter<V> where V : IMvpView, V : IFirstTimeDplkView {

    private var mUrlListBrifine: String = ""
    private var mUrlPersonalDataRegistration: String = ""
    private var mUrlCheckStatusDplk: String = ""

    override fun setUrl(url: String) {
    }

    override fun seturlDplkRegis(url: String) {
    }

    override fun setUrlPersonalDataRegistration(url: String) {
        mUrlPersonalDataRegistration = url
    }

    override fun setUrlListBrifine(url: String) {
        mUrlListBrifine = url
    }


    override fun setUrlDashboardDplk(urlDashboard: String) {
    }

    override fun setUrlCheckStatusDplk(url: String) {
        mUrlCheckStatusDplk = url
    }

    override fun getDashboardDplk() {
    }

    override fun getDataListBrifine(request: ListDplkRequest) {
        if (!isViewAttached) {
            return
        }

        view.showProgress()
        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable
            .add(
                apiSource.getData(mUrlListBrifine, request, seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView().hideProgress()
                            getView().onException(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            getView().hideProgress()
                            val data = response.getData(ListPilihBrifineResponse::class.java)
                            getView().onSuccessListBrifine(data)
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView()!!.hideProgress()
                            getView().onException(restResponse.desc)
                        }
                    })
            )
    }

    override fun getPersonalDataRegistration() {
        if (!isViewAttached) {
            return
        }

        view.showProgress()
        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable
            .add(
                apiSource.getDataTanpaRequest(mUrlPersonalDataRegistration, seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView().hideProgress()
                            getView().onException(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            getView().hideProgress()

                            val data = response.getData(PersonalDataDplkResponse::class.java)
                            getView().onSuccessPersonalDataDplk(data)
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView()!!.hideProgress()
                            getView().onException(restResponse.desc)
                        }
                    })
            )
    }

    override fun getCheckStatusDplk(request: CheckStatusDplkRequest) {
        if (!isViewAttached) {
            return
        }
        view.showProgress()
        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable
            .add(
                apiSource.getData(mUrlCheckStatusDplk, request, seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView().hideProgress()
                            getView().onException(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            getView().hideProgress()
                            val data = response.getData(PaymentFtuDplkResponse::class.java)
                            getView().onSuccesCheckStatus(data)
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView()!!.hideProgress()
                            getView().onException(restResponse.desc)
                        }
                    })
            )
    }
}