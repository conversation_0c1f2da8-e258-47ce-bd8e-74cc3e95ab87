package id.co.bri.brimo.presenters.voucher

import id.co.bri.brimo.contract.IPresenter.voucher.IHistoryVoucherPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.voucher.IHistoryVoucherView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimo.data.repository.category.CategoryPfmSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.config.AppConfig
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.config.LifestyleConfig
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.voucher.VoucherCodeRequest
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.voucher.HistoryVoucherGameResponse
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.Schedulers
import java.util.concurrent.TimeUnit

class HistoryVoucherPresenter <V>(schedulerProvider: SchedulerProvider?,
                                  compositeDisposable: CompositeDisposable?,
                                  mBRImoPrefRepository: BRImoPrefSource?,
                                  apiSource: ApiSource?, categoryPfmSource: CategoryPfmSource?,
                                  transaksiPfmSource: TransaksiPfmSource?,
                                  anggaranPfmSource: AnggaranPfmSource?) : MvpPresenter<V>(
    schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource
),
    IHistoryVoucherPresenter<V> where V : IMvpView?, V : IHistoryVoucherView {

    private var urlHistoryVoucher: String? = null
    private var urlVoucherCode: String? = null

    override fun setUrlHistory(url: String) {
        urlHistoryVoucher = url
    }

    override fun setUrlVoucherCode(url: String) {
        this.urlVoucherCode = url
    }

    override fun getDataHistoryVoucher(isRefresh: Boolean) {
        if (!isViewAttached || urlHistoryVoucher!!.isEmpty()) {
            return
        }

        val seqNum = brImoPrefRepository.seqNumber
        val listConnectableObservable =
            apiSource.getDataTanpaRequest(urlHistoryVoucher, seqNum).subscribeOn(
                schedulerProvider.io()
            ).timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .observeOn(schedulerProvider.mainThread()).replay()
        compositeDisposable.add(
            listConnectableObservable.timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(Schedulers.io()).observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView()!!.onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        val historyVocGameResponse =
                            response.getData(HistoryVoucherGameResponse::class.java)

                        getView()!!.onSuccessGetData(historyVocGameResponse, isRefresh)

                        if (!GeneralHelper.isProd()) {
                            GeneralHelper.responseChuck(response)
                        }
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value, ignoreCase = true)) {
                            getView()!!.onSessionEnd(restResponse.desc)
                        } else {
                            getView()!!.onException(restResponse.desc)
                        }
                    }

                })
        )
        listConnectableObservable.connect()
    }

    override fun getVoucherCode(transactionId: String, itemPosition: Int) {
        if (!isViewAttached || urlVoucherCode?.isEmpty() != false) {
            return
        }

        val request = VoucherCodeRequest(transactionId)
        val seqNum = brImoPrefRepository.seqNumber
        val listConnectableObservable =
            apiSource.getData(urlVoucherCode, request, seqNum).subscribeOn(
                schedulerProvider.io()
            ).timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .observeOn(schedulerProvider.mainThread()).replay()
        compositeDisposable.add(
            listConnectableObservable.timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(Schedulers.io()).observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView().onExceptionFailureHttp(errorMessage, itemPosition)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        val historyVoucherResponse =
                            response.getData(HistoryVoucherGameResponse.HistoryVoucher::class.java)
                        if (response.code.equals(Constant.RE_SUCCESS, ignoreCase = true)) {
                            when (historyVoucherResponse.status) {
                                LifestyleConfig.VoucherCode.STATUS_FAILED.VoucherCodeStatus -> getView().onFailureGetVoucherCode(
                                    response,
                                    itemPosition
                                )

                                LifestyleConfig.VoucherCode.STATUS_SUCCESSFUL.VoucherCodeStatus -> getView().onSuccessGetVoucherCode(
                                    historyVoucherResponse,
                                    itemPosition
                                )

                                LifestyleConfig.VoucherCode.STATUS_PENDING.VoucherCodeStatus -> getView().onPendingGetVoucherCode(
                                    itemPosition
                                )
                            }
                        }
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        if (restResponse.code.equals(Constant.RE_SESSION_END, ignoreCase = true)) {
                            getView().onSessionEnd(restResponse.desc)
                        } else if (restResponse.code.equals(Constant.RE12, ignoreCase = true)) {
                            getView().onExceptionAlert(restResponse.desc, itemPosition)
                        } else {
                            getView().onException(restResponse.desc)
                        }
                    }
                })
        )
        listConnectableObservable.connect()
    }
}