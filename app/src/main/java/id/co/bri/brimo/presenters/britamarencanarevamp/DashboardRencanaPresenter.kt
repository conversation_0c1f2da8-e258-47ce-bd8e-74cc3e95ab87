package id.co.bri.brimo.presenters.britamarencanarevamp

import id.co.bri.brimo.contract.IPresenter.britamarencanarevamp.IDashboardRencanaPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.britamarencanarevamp.IDashboardRencanaView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimo.data.repository.category.CategoryPfmSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.config.AppConfig
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.UbahPinRequest
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.dashboardrecana.DashboardRencanaResponse
import id.co.bri.brimo.models.apimodel.response.dashboardrecana.FirstItemRencanaResponse
import id.co.bri.brimo.models.apimodel.response.dashboardrecana.UpdateRekeningRencanaResponse
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.Schedulers
import java.util.concurrent.TimeUnit

class DashboardRencanaPresenter<V>(schedulerProvider: SchedulerProvider,
                                   compositeDisposable: CompositeDisposable,
                                   mBRImoPrefRepository: BRImoPrefSource, apiSource: ApiSource,
                                   categoryPfmSource: CategoryPfmSource,
                                   transaksiPfmSource: TransaksiPfmSource,
                                   anggaranPfmSource: AnggaranPfmSource) : MvpPresenter<V>(
    schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource
),
        IDashboardRencanaPresenter<V> where V : IMvpView, V : IDashboardRencanaView  {

    private var mUrl = ""
    private var mUrlForced = ""
    private var urlString = ""

    override fun setUrlDashboard(url: String) {
        mUrl = url
    }

    override fun setUrlDashboardRencanaForced(url: String) {
        mUrlForced = url
    }

    override fun setUrlUpdateRekening(url: String) {
        urlString = url
    }

    override fun getDataDashboardRencanaForced() {
        if (isViewAttached) {

            val seqNum = brImoPrefRepository.seqNumber
            val listConnectableObservable = apiSource.getDataTanpaRequest(
                mUrlForced,
                seqNum
            ).subscribeOn(schedulerProvider.io())
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .observeOn(schedulerProvider.mainThread())
                .replay()

            compositeDisposable.add(listConnectableObservable
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(type: String) {
                            getView().onException(type)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            val response = response.getData(
                                    DashboardRencanaResponse::class.java
                            )

                            getView().onSuccessDashboardRencana(response)
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value, ignoreCase = true)) {
                                getView().onSessionEnd(restResponse.desc)
                            } else {
                                getView().onException(restResponse.desc)
                            }
                        }

                        override fun onComplete() {
                            super.onComplete()
                        }
                    })
            )
            listConnectableObservable.connect()
        }
    }


    override fun getDataDashboardRencana() {
        if (isViewAttached) {
            val seqNum = brImoPrefRepository.seqNumber
            val listConnectableObservable = apiSource.getDataTanpaRequest(
                mUrl,
                seqNum
            ).subscribeOn(schedulerProvider.io())
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .observeOn(schedulerProvider.mainThread())
                .replay()

            compositeDisposable.add(listConnectableObservable
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(type: String) {
                            getView().onException(type)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SUCCESS.value, ignoreCase = true)) {
                                val response = response.getData(
                                    DashboardRencanaResponse::class.java
                                )
                                getView().onSuccessDashboardRencana(response)
                            } else if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_01.value)) {
                                val responseFirst = response.getData(
                                    FirstItemRencanaResponse::class.java
                                )
                                getView().onSuccessFirstTime(responseFirst)
                            }
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value, ignoreCase = true)) {
                                getView().onSessionEnd(restResponse.desc)
                            } else {
                                getView().onException(restResponse.desc)
                            }
                        }

                        override fun onComplete() {
                            super.onComplete()
                        }
                    })
            )
            listConnectableObservable.connect()
        }
    }

    override fun getUpdateRekening(request: UbahPinRequest) {
        if (isViewAttached) {
            view.showProgress()

            val seqNum = brImoPrefRepository.seqNumber
            val listConnectableObservable =
                apiSource.getData(urlString, request, seqNum).subscribeOn(
                    schedulerProvider.io()
                )
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .observeOn(schedulerProvider.mainThread())
                    .replay()
            compositeDisposable.add(
                listConnectableObservable
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(type: String) {
                            getView().hideProgress()
                            getView().onException(type)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            getView().hideProgress()
                            val data = response.getData(UpdateRekeningRencanaResponse::class.java)
                            if (!data.account.isNullOrEmpty()) {
                                getView().onSuccessUpdate(data)
                            } else {
                                getView().onNoNewAccounts(data)
                            }
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView().hideProgress()
                            if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value, ignoreCase = true)) {
                                getView().onSessionEnd(restResponse.desc)
                            } else {
                                getView().onException(restResponse.desc)
                            }
                        }

                        override fun onComplete() {
                            super.onComplete()
                        }
                    })
            )
            listConnectableObservable.connect()
        }
    }
}