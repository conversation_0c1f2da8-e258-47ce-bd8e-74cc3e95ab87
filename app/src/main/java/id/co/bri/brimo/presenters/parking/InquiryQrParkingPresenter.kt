package id.co.bri.brimo.presenters.parking

import id.co.bri.brimo.contract.IPresenter.parking.IInquiryQrParkingPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.base.IBaseInquiryView
import id.co.bri.brimo.contract.IView.parking.IInquiryQrParkingView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimo.data.repository.category.CategoryPfmSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.config.AppConfig

import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.FastSecureParkingConfirmationRequest
import id.co.bri.brimo.models.apimodel.request.SecureParkingConfirmationRequest
import id.co.bri.brimo.models.apimodel.response.ExtrasResponse
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.presenters.base.BaseInquiryPresenter
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.disposables.Disposable
import java.util.concurrent.TimeUnit

class InquiryQrParkingPresenter<V>(
    schedulerProvider: SchedulerProvider?,
    compositeDisposable: CompositeDisposable?,
    mBRImoPrefRepository: BRImoPrefSource?,
    apiSource: ApiSource?,
    categoryPfmSource: CategoryPfmSource?,
    transaksiPfmSource: TransaksiPfmSource?,
    anggaranPfmSource: AnggaranPfmSource?
) :
    BaseInquiryPresenter<V>(
        schedulerProvider,
        compositeDisposable,
        mBRImoPrefRepository,
        apiSource,
        categoryPfmSource,
        transaksiPfmSource,
        anggaranPfmSource
    ),
    IInquiryQrParkingPresenter<V> where V : IMvpView?, V : IBaseInquiryView?, V : IInquiryQrParkingView? {

    var mUrlConfirmation: String? = null
    override fun setUrlConfirmation(urlConfirmation: String) {
        this.mUrlConfirmation = urlConfirmation
    }

    /**
     * @param refNum
     * @param accountNum
     * @param amount
     * @param save
     * @param tip
     */
    override fun getDataConfirmationParking(refNum: String, accountNum: String, pin: String, isFromFastMenu: Boolean) {
        if (isViewAttached) {
            //flag on Load true
            view?.showProgress()
            confirmationRequest = if (isFromFastMenu) {
                FastSecureParkingConfirmationRequest(getBRImoPrefRepository().getUsername(), getBRImoPrefRepository().getTokenKey(), refNum, accountNum, pin)
            } else {
                SecureParkingConfirmationRequest(refNum, accountNum, pin)
            }
            val seqNum = brImoPrefRepository.seqNumber
            val disposable: Disposable =
                apiSource.getData(mUrlConfirmation, confirmationRequest, seqNum) //function(param)
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(schedulerProvider.io())
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView()?.onException(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            getView()?.hideProgress()
                            val extrasResponse = response.getData(ExtrasResponse::class.java)
                            getView()?.onSuccessGetConfirmationParking(extrasResponse)
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView()?.hideProgress()
                            if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value, ignoreCase = true))
                                getView()?.onSessionEnd(restResponse.desc)
                            else if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.value, ignoreCase = true))
                                getView()?.onException93(restResponse.desc)
                            else
                                getView()?.onException(restResponse.desc)
                        }
                    })
            compositeDisposable.add(disposable)
        }
    }
}