package id.co.bri.brimo.presenters.onboardingrevamp

import id.co.bri.brimo.contract.IPresenter.openaccount.IOnboardingKodePosBottomPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.openaccount.IOnboardingKodePosBottomView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimo.data.repository.category.CategoryPfmSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.config.AppConfig
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.KodePosRequest
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.onboardingrevamp.OnboardingKodePosRes
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.Schedulers
import java.util.concurrent.TimeUnit

class OnboardingKodePosBottomPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBRImoPrefRepository: BRImoPrefSource,
    apiSource: ApiSource,
    categoryPfmSource: CategoryPfmSource,
    transaksiPfmSource: TransaksiPfmSource,
    anggaranPfmSource: AnggaranPfmSource
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    mBRImoPrefRepository,
    apiSource,
    transaksiPfmSource
), IOnboardingKodePosBottomPresenter<V> where V : IMvpView, V : IOnboardingKodePosBottomView {

    private lateinit var urlKodePos: String

    override fun setUrl(url: String) {
        urlKodePos = url
    }

    override fun onSendPostCode(kodePosRequest: KodePosRequest) {
        if (urlKodePos.isEmpty() || !isViewAttached) return
        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable.add(
            apiSource.getData(urlKodePos, kodePosRequest, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView().onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        val onboardingKodePosRes =
                            response.getData(OnboardingKodePosRes::class.java)
                        getView().onDataPostCode(onboardingKodePosRes)
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_12.value))
                            getView().onDataNotFound()
                        else
                            getView().onExceptionRevamp(restResponse.desc)
                    }
                })
        )
    }

    override fun getDeviceId() {
        view.onDeviceId(brImoPrefRepository.deviceId)
    }
}