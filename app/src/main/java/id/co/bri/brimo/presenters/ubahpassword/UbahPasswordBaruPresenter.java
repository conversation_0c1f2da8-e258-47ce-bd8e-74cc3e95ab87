package id.co.bri.brimo.presenters.ubahpassword;

import java.util.concurrent.TimeUnit;

import javax.inject.Inject;

import id.co.bri.brimo.contract.IPresenter.ubahpassword.IUbahPasswordBaruPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.ubahpassword.IUbahPasswordBaruView;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.config.AppConfig;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.apimodel.request.UbahPasswordOtpRequest;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.models.apimodel.response.UbahPasswordBaruResponse;
import id.co.bri.brimo.presenters.MvpPresenter;
import io.reactivex.disposables.CompositeDisposable;

public class UbahPasswordBaruPresenter<V extends IMvpView & IUbahPasswordBaruView> extends MvpPresenter<V> implements IUbahPasswordBaruPresenter<V> {

    private String urlCheckPassKunci = "";
    private String urlCheckOldPass = "";


    @Inject
    public UbahPasswordBaruPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void onUbahPasswordSubmit() {
        if (getView() == null) {
            return;
        }
        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        UbahPasswordOtpRequest ubahPasswordRequest = new UbahPasswordOtpRequest(getView().getRefNumber(), "");
        if (isViewAttached()) {
            getCompositeDisposable().add(
                    getApiSource().getData(urlCheckPassKunci, ubahPasswordRequest, seqNum)
                            .subscribeOn(getSchedulerProvider().single())
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .observeOn(getSchedulerProvider().mainThread())
                            .subscribeWith(new ApiObserver(getView(),seqNum) {
                                @Override
                                protected void onFailureHttp(String type) {
                                    getView().hideProgress();
                                    getView().onException(type);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    getView().hideProgress();
                                    UbahPasswordBaruResponse ubahPasswordBaruResponse = response.getData(UbahPasswordBaruResponse.class);
                                    getView().onSubmitSuccess(ubahPasswordBaruResponse.getReference_number(), ubahPasswordBaruResponse.getCellphone_number());
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().hideProgress();
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue())) {
                                        getView().onSessionEnd(restResponse.getDesc());
                                    }else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.getValue())){
                                        getView().onException93(restResponse.getDesc());
                                    }else
                                        getView().onError();
                                }
                            })
            );
        }

    }

    @Override
    public void setUrlValidatePassKunci(String urlUbahPassword) {
        this.urlCheckPassKunci = urlUbahPassword;
    }

    @Override
    public void setUrlValidateOldPass(String urlCheck) {
        this.urlCheckOldPass = urlCheck;
    }

    @Override
    public void confirmNewPass() {
        if (getView() == null) {
            return;
        }
        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        UbahPasswordOtpRequest ubahPasswordRequest = new UbahPasswordOtpRequest(getView().getRefNumber(), getView().getPasswordBaru());
        if (isViewAttached()) {
            getCompositeDisposable().add(
                    getApiSource().getData(urlCheckOldPass, ubahPasswordRequest, seqNum)
                            .subscribeOn(getSchedulerProvider().single())
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .observeOn(getSchedulerProvider().mainThread())
                            .subscribeWith(new ApiObserver(getView(),seqNum) {
                                @Override
                                protected void onFailureHttp(String type) {
                                    getView().hideProgress();
                                    getView().onException(type);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    getView().hideProgress();
                                    UbahPasswordBaruResponse ubahPasswordBaruResponse = response.getData(UbahPasswordBaruResponse.class);
                                    if (ubahPasswordBaruResponse.getMatch()) {
                                        getView().onSuccessConfirm();
                                    } else {
                                        getView().onFailConfirm();
                                    }
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().hideProgress();
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue())) {
                                        getView().onSessionEnd(restResponse.getDesc());
                                    }else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.getValue())){
                                        getView().onException93(restResponse.getDesc());
                                    }else
                                        getView().onError();
                                }
                            })
            );
        }

    }

    @Override
    public Boolean checkInputUser() {

        if (getView().getPasswordBaru().equals(getBRImoPrefRepository().getUsername())) {
            return false;
        } else {
            return true;
        }
    }

}