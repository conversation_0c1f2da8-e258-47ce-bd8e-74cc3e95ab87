package id.co.bri.brimo.presenters.pengelolaankartu;

import com.google.gson.Gson;

import java.util.concurrent.TimeUnit;

import id.co.bri.brimo.contract.IPresenter.pengelolaankartu.IListCardTypePresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.pengelolaankartu.IListCardTypeView;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.config.AppConfig;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.apimodel.request.ListLimitCardRequest;
import id.co.bri.brimo.models.apimodel.response.LimitCardTypeResponse;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.presenters.MvpPresenter;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.schedulers.Schedulers;

public class ListCardTypePresenter<V extends IMvpView & IListCardTypeView>
        extends MvpPresenter<V> implements IListCardTypePresenter<V> {

    protected String url;
    private ListLimitCardRequest request;

    public ListCardTypePresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable,
                                 BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource,
                                 TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void setUrl(String url) {
        this.url = url;
    }

    @Override
    public void getListCard(String accountNumber, String responseSnack) {
        if (url != null || isViewAttached()) {

            request = new ListLimitCardRequest(accountNumber);
            System.out.println("isinya apa " + new Gson().toJson(request));

            getView().showSkeleton(true);
            String seqNum = getBRImoPrefRepository().getSeqNumber();

            getCompositeDisposable().add(
                    getApiSource().getData(url, request, seqNum)
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .subscribeOn(Schedulers.io())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribeWith(new ApiObserver(getView(), seqNum) {

                                @Override
                                protected void onFailureHttp(String errorMessage) {
                                    getView().showSkeleton(false);
                                    getView().onException(errorMessage);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                   getView().showSkeleton(false);
                                       LimitCardTypeResponse limitCardTypeResponse = response.getData(LimitCardTypeResponse.class);
                                       getView().successGetListLimitCard(limitCardTypeResponse, responseSnack);

                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().showSkeleton(false);
                                    onApiError(restResponse);
                                }
                            })
            );
        }
    }

    @Override
    public void onApiError(RestResponse restResponse) {
        getView().showSkeleton(false);

        if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_12.getValue())) {
            getView().onException12(restResponse.getDesc());
        } else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.getValue())) {
            getView().onExceptionTrxExpired(restResponse.getDesc());
        } else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_99.getValue())) {
            getView().onException99(restResponse.getDesc());
        } else
            super.onApiError(restResponse);
    }
}