package id.co.bri.brimo.presenters.dplkrevamp

import id.co.bri.brimo.contract.IPresenter.dplkrevamp.IHistoryRiwayatProsesClaimDplkPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.dplkrevamp.IHistoryTransaksiProsesClaimView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimo.data.repository.category.CategoryPfmSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.config.AppConfig
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.dplkrevamp.DetailKlaimDplkRequest
import id.co.bri.brimo.models.apimodel.response.PendingResponse
import id.co.bri.brimo.models.apimodel.response.ReceiptRevampResponse
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.dplkrevamp.RiwayatClaimDplkResponse
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.disposables.CompositeDisposable
import java.util.concurrent.TimeUnit

class HistoryRiwayatProsesClaimDplkPresenter<V>(schedulerProvider: SchedulerProvider,
                                                compositeDisposable: CompositeDisposable,
                                                mBRImoPrefRepository: BRImoPrefSource, apiSource: ApiSource,
                                                categoryPfmSource: CategoryPfmSource,
                                                transaksiPfmSource: TransaksiPfmSource,
                                                anggaranPfmSource: AnggaranPfmSource)  : MvpPresenter<V>(
    schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource),
    IHistoryRiwayatProsesClaimDplkPresenter<V> where V : IMvpView, V : IHistoryTransaksiProsesClaimView {


    private var mUrlListKlaimBrifine: String = ""
    private var mUrlDetailKlaimBrifine: String = ""


    override fun seturlListClaimBrifine(url: String) {
        mUrlListKlaimBrifine = url
    }

    override fun setUrlDetailClaimBrifine(url: String) {
        mUrlDetailKlaimBrifine = url
    }

    override fun getListClaimBrifine() {
        if (isViewAttached) {
            val seqNum = brImoPrefRepository.seqNumber
            val listConnectableObservable =
                apiSource.getDataTanpaRequest(mUrlListKlaimBrifine, seqNum).subscribeOn(
                    schedulerProvider.io()
                ).timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .observeOn(schedulerProvider.mainThread())
                    .replay()
            compositeDisposable.add(
                listConnectableObservable
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(schedulerProvider.io())
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(type: String) {
                            getView().hideProgress()
                            getView().onException(type)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            val data = response.getData(RiwayatClaimDplkResponse::class.java)

                            getView().onSuccessGetHistoryListClaimDplk(data)
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView().onException(restResponse.desc)
                        }

                    })
            )
            listConnectableObservable.connect()
        }
    }

    override fun getDetailClaimBrifine(request: DetailKlaimDplkRequest) {
        if (isViewAttached) {
            view.showProgress()

            val seqNum = brImoPrefRepository.seqNumber
            val listConnectableObservable =
                apiSource.getData(mUrlDetailKlaimBrifine,request, seqNum).subscribeOn(
                    schedulerProvider.io()
                ).timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .observeOn(schedulerProvider.mainThread())
                    .replay()
            compositeDisposable.add(
                listConnectableObservable
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(schedulerProvider.io())
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(type: String) {
                            getView().hideProgress()
                            getView().onException(type)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            view.hideProgress()
                            val data = response.getData(ReceiptRevampResponse::class.java)

                            getView().onSuccessGetHistoryDetailClaimDplk(data)
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView().hideProgress()
                            getView().onException(restResponse.desc)
                        }

                    })
            )
            listConnectableObservable.connect()
        }
    }


}