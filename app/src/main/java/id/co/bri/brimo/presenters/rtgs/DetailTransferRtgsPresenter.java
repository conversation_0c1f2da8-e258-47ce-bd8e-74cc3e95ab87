package id.co.bri.brimo.presenters.rtgs;

import java.util.concurrent.TimeUnit;

import id.co.bri.brimo.contract.IPresenter.rtgs.IDetailTransferRtgsPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.rtgs.IDetailTransferRtgsView;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.config.AppConfig;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.apimodel.request.FastInquiryDetailTransferRtgsRequest;
import id.co.bri.brimo.models.apimodel.request.InquiryRtgsDetailRequest;
import id.co.bri.brimo.models.apimodel.response.InquiryTransferRtgsResponse;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.presenters.MvpPresenter;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.schedulers.Schedulers;

public class DetailTransferRtgsPresenter <V extends IMvpView & IDetailTransferRtgsView> extends MvpPresenter<V> implements IDetailTransferRtgsPresenter<V> {
    private static final String TAG = "DetailTransferRtgsPres";
    String url;
    protected Object inquiryRequest = null;

    public DetailTransferRtgsPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }



    @Override
    public void getDataInquiryDetail(InquiryRtgsDetailRequest inquiryRtgsDetailRequest, boolean isFromFastMenu) {
        if (url == null || !isViewAttached()) {
            return;
        }

        if(isFromFastMenu){
            inquiryRequest = new FastInquiryDetailTransferRtgsRequest(getFastMenuRequest(),
                    inquiryRtgsDetailRequest.getReferenceNumber(), inquiryRtgsDetailRequest.getDestinationName(), inquiryRtgsDetailRequest.getBenefCitizenship(), inquiryRtgsDetailRequest.getBenefCityCode(),
                    inquiryRtgsDetailRequest.getBenefCityName(), inquiryRtgsDetailRequest.getBenefProvinceCode(), inquiryRtgsDetailRequest.getBenefAddress(), inquiryRtgsDetailRequest.getBenefNotes());
        } else {
            inquiryRequest = inquiryRtgsDetailRequest;
        }

        String seqNum = getBRImoPrefRepository().getSeqNumber();
        getView().showProgress();
        getCompositeDisposable()
                .add(getApiSource().getData(url, inquiryRequest, seqNum)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(Schedulers.io())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(new ApiObserver(getView(),seqNum) {


                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().hideProgress();
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                //TO-DO onSuccess
                                getView().hideProgress();
                                InquiryTransferRtgsResponse responsebriva = response.getData(InquiryTransferRtgsResponse.class);
//                                responsebriva.setSaved("bifast");
                                getView().onSuccessInquiryRtgs(responsebriva);
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                    getView().onSessionEnd(restResponse.getDesc());
                                else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_99.getValue()))
                                    getView().onException99(restResponse.getDesc());
                                else
                                    getView().onException(restResponse.getDesc());

                            }
                        }));

    }


    @Override
    public void setUrlInquiryDetail(String url) {
        this.url = url;
    }
}