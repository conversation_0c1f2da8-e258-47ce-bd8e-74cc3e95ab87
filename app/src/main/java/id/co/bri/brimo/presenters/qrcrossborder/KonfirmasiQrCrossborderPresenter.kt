package id.co.bri.brimo.presenters.qrcrossborder

import id.co.bri.brimo.contract.IPresenter.qrcrossborder.IKonfirmasiQrCrossborderPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.qrcrossborder.IKonfirmasiQrCrossborderView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserverKonfirmasi
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimo.data.repository.category.CategoryPfmSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.config.DbConfig

import id.co.bri.brimo.domain.helpers.calendar.CalendarHelper
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.FastPaymentRequest
import id.co.bri.brimo.models.apimodel.request.PaymentRequest
import id.co.bri.brimo.models.apimodel.response.PendingResponse
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.qr.KonfirmasiQrCrossBorderResponse
import id.co.bri.brimo.models.daomodel.Transaksi
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.disposables.Disposable
import io.reactivex.observers.DisposableSingleObserver
import io.reactivex.schedulers.Schedulers

class KonfirmasiQrCrossborderPresenter<V>(
    schedulerProvider: SchedulerProvider?,
    compositeDisposable: CompositeDisposable?,
    mBRImoPrefRepository: BRImoPrefSource?,
    apiSource: ApiSource?,
    categoryPfmSource: CategoryPfmSource?,
    transaksiPfmSource: TransaksiPfmSource?,
    anggaranPfmSource: AnggaranPfmSource?
) :
    MvpPresenter<V>(
        schedulerProvider,
        compositeDisposable,
        mBRImoPrefRepository,
        apiSource,
        transaksiPfmSource
    ),
    IKonfirmasiQrCrossborderPresenter<V> where V : IMvpView?, V : IKonfirmasiQrCrossborderView? {

    private var mUrlPayment: String? = null
    private var paymentRequest: Any? = null
    private var idPayment = 0

    override fun getDataPayment(pin: String, note: String, response: KonfirmasiQrCrossBorderResponse?, fromFast: Boolean) {
        if (mUrlPayment == null) {
            return
        }
        if (!isViewAttached) {
            return
        }
        if (isViewAttached) {
            view!!.showProgress()
            val seqNum = brImoPrefRepository.seqNumber
            if (fromFast) paymentRequest =
                FastPaymentRequest(getFastMenuRequest(), response?.referenceNumber, pin, response?.pfmCategory.toString(), note)
            else
                paymentRequest = PaymentRequest(response?.referenceNumber, pin, response?.pfmCategory.toString(), note)
            val disposable: Disposable =
                apiSource.getData(mUrlPayment, paymentRequest, seqNum) //function(param)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(object : ApiObserverKonfirmasi(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView()!!.onException(errorMessage)
                        }

                        override fun onApiCallSuccess(restResponse: RestResponse) {
                            getView()!!.hideProgress()
                            val brivaResponse = restResponse.getData(PendingResponse::class.java)
                            getView()?.onSuccessGetPayment(brivaResponse)
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView()!!.hideProgress()
                            if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value, ignoreCase = true)) getView()!!.onSessionEnd(restResponse.desc)
                            else if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.value, ignoreCase = true))
                                getView()?.onException93(restResponse.desc)
                            else if (restResponse.code == RestResponse.ResponseCodeEnum.RC_99.value)
                                getView()!!.onException99(restResponse.desc)
                            else
                                getView()!!.onException(restResponse.desc)
                        }
                    })
            compositeDisposable.add(disposable)
        }

    }


    override fun setUrlPayment(urlPayment: String?) {
        this.mUrlPayment = urlPayment;
    }

    override fun setIdPayment(idPay: Long) {
        idPayment = idPay.toInt()
    }

    override fun generateTransaksiModel(kategoriId: Int, amount: Long, referenceNumber: String?, billingName: String?): Transaksi? {
        var transaksi: Transaksi? = null
        try {
            transaksi = Transaksi(
                kategoriId.toLong(),
                1,
                billingName,
                "",
                DbConfig.TRX_OUT,
                brImoPrefRepository.user,
                amount,
                CalendarHelper.getCurrentDate(),
                CalendarHelper.getCurrentTime(),
                java.lang.Long.valueOf(referenceNumber),
                idPayment.toLong()
            )
        } catch (e: Exception) {
            /*
            if(BuildConfig.DEBUG)
            e.printStackTrace();
             */
        }
        return transaksi
    }

    override fun onSaveTransaksiPfm(transaksi: Transaksi?) {
        if (transaksi != null) {
            compositeDisposable.add(
                transaksiPfmSource
                    .saveTransaksiPfm(transaksi)
                    .subscribeOn(schedulerProvider.io())
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : DisposableSingleObserver<Long?>() {
                        override fun onSuccess(aLong: Long) {}
                        override fun onError(e: Throwable) {
                            // do nothing
                        }
                    })
            )
        }
    }

}