package id.co.bri.brimo.presenters.regisSbn;


import java.util.concurrent.TimeUnit;

import id.co.bri.brimo.contract.IPresenter.regisEsbn.IEsbnOnboardingRegisPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.regisSbn.IEsbnOnboardingRegisView;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.config.AppConfig;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.apimodel.request.OnboardingRDNSBNRequest;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.models.apimodel.response.esbn.DashboardDataSbnResponse;
import id.co.bri.brimo.models.apimodel.response.esbn.EsbnExceptionResponse;
import id.co.bri.brimo.models.apimodel.response.rdn.RdnOnBoardingResponse;
import id.co.bri.brimo.models.apimodel.response.rdn.RdnOnCheckpointResponse;
import id.co.bri.brimo.models.apimodel.response.esbn.regisESBN.EsbnProductBriefResponse;
import id.co.bri.brimo.presenters.MvpPresenter;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.schedulers.Schedulers;

public class EsbnOnboardingRegisPresenter<V extends IMvpView & IEsbnOnboardingRegisView> extends MvpPresenter<V> implements IEsbnOnboardingRegisPresenter<V> {

    public String url ;
    public String urlProduct;


    public EsbnOnboardingRegisPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void setUrl(String url) {
        this.url = url;
    }

    @Override
    public void setUrlProduct(String urlProduct) {
        this.urlProduct = urlProduct;
    }

    @Override
    public void onGetData() {
        if (url != null && isViewAttached()) {
            getView().showProgress();
            String seqNum = getBRImoPrefRepository().getSeqNumber();

            getCompositeDisposable()
                    .add(getApiSource().getDataTanpaRequest(url, seqNum)
                            .subscribeOn(Schedulers.io())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribeWith(new ApiObserver(getView(),seqNum) {
                                @Override
                                protected void onFailureHttp(String errorMessage) {
                                    getView().hideProgress();
                                    getView().onException(errorMessage);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    //TO-DO onSuccess
                                    getView().hideProgress();
                                    if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SUCCESS.getValue())) {
                                        EsbnProductBriefResponse productESBNResponse = response.getData(EsbnProductBriefResponse.class);
                                        getView().onSuccessGetData(productESBNResponse);
                                    }
                                    else if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_S1.getValue())) {
                                        DashboardDataSbnResponse dataSbnResponse =response.getData(DashboardDataSbnResponse.class);
                                        getView().onSuccesGetS1(dataSbnResponse);
                                    }
                                    else if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_S2.getValue())){
                                        DashboardDataSbnResponse dataSbnResponse =response.getData(DashboardDataSbnResponse.class);
                                        getView().onSuccesGetS2(dataSbnResponse);
                                    }
                                    else if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_02.getValue())){
                                        EsbnExceptionResponse esbnExceptionResponse = response.getData(EsbnExceptionResponse.class);
                                        getView().onException02(esbnExceptionResponse);
                                        getView().hideProgress();
                                    }

                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().hideProgress();
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                        getView().onSessionEnd(restResponse.getDesc());
                                    else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_99.getValue()))
                                        getView().onException99(restResponse.getDesc());

                                    else
                                        getView().onException(restResponse.getDesc());
                                }
                            }));
        }
    }

    @Override
    public void onGetOnProductBrief() {
        if (isViewAttached()) {
            getView().showProgress();
            OnboardingRDNSBNRequest onboardingRDNSBNRequest = new OnboardingRDNSBNRequest(getBRImoPrefRepository().getFirstRdn());
            String seqNum = getBRImoPrefRepository().getSeqNumber();
            getCompositeDisposable().add(getApiSource().getData(urlProduct, onboardingRDNSBNRequest, seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(new ApiObserver(getView(),seqNum) {
                        @Override
                        protected void onFailureHttp(String errorMessage) {
                            getView().hideProgress();
                            getView().onException(errorMessage);
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SUCCESS.getValue())){
                                RdnOnBoardingResponse rdnOnBoardingResponse = response.getData(RdnOnBoardingResponse.class);
                                getView().onSuccessProductRdn(rdnOnBoardingResponse);
                                getView().hideProgress();
                            }
                            else if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_01.getValue())){
                                RdnOnCheckpointResponse rdnOnBoardingResponse = response.getData(RdnOnCheckpointResponse.class);
                                getView().onSuccessCheckPoint(rdnOnBoardingResponse);
                                getView().hideProgress();
                            }
                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            getView().hideProgress();
                            if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                getView().onSessionEnd(restResponse.getDesc());
                            else
                                getView().onException(restResponse.getDesc());
                        }
                    }));
        }
    }

    @Override
    public void saveFirstRdn(Boolean firstRdn) {
        getBRImoPrefRepository().saveFirstRdn(firstRdn);
    }

    @Override
    public void saveBackGeneral(int status) {
        getBRImoPrefRepository().saveBackGeneral(status);
    }
}