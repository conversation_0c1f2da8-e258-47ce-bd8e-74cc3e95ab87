package id.co.bri.brimo.presenters.rdn;

import android.os.Build;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

import id.co.bri.brimo.R;
import id.co.bri.brimo.contract.IPresenter.rdn.IDashboardRdnPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.rdn.IDashboardRdnView;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.config.AppConfig;
import id.co.bri.brimo.domain.converter.MapperHelper;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.apimodel.request.OnboardingRDNSBNRequest;
import id.co.bri.brimo.models.apimodel.request.rdn.InquiryRdnRequest;
import id.co.bri.brimo.models.apimodel.request.rdn.RdnInfoSaldoRequest;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.models.apimodel.response.SaldoReponse;
import id.co.bri.brimo.models.apimodel.response.rdn.InquiryRdnResponse;
import id.co.bri.brimo.models.apimodel.response.rdn.RdnAccountResponse;
import id.co.bri.brimo.models.apimodel.response.rdn.RdnDashboardFailureResponse;
import id.co.bri.brimo.models.apimodel.response.rdn.RdnOnBoardingResponse;
import id.co.bri.brimo.models.apimodel.response.rdn.RdnOnCheckpointResponse;
import id.co.bri.brimo.models.apimodel.response.rdn.RdnProgressResponse;
import id.co.bri.brimo.presenters.MvpPresenter;
import io.reactivex.Observable;
import io.reactivex.ObservableSource;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.functions.Function;
import io.reactivex.observers.DisposableObserver;
import io.reactivex.schedulers.Schedulers;

public class DashboardRdnPresenter<V extends IMvpView & IDashboardRdnView> extends MvpPresenter<V> implements IDashboardRdnPresenter<V> {

    private String url;
    private String urlInfoSaldo;
    private String urlOnboarding;
    private String urlStatusProgress;

    private String urlInquiryRdn;

    List<RdnAccountResponse.AccountRdn> listRdnAccount = new ArrayList<>();

    boolean isLoading = false;

    public DashboardRdnPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void setUrl(String url) {
        this.url = url;
    }

    @Override
    public void setUrlInfoSaldo(String url) {
        this.urlInfoSaldo = url;
    }

    @Override
    public void setUrlOnBoarding(String urlOnBoarding) {
        this.urlOnboarding = urlOnBoarding;
    }


    @Override
    public void setUrlStatusProgress(String urlStatusProgress) {this.urlStatusProgress = urlStatusProgress; }

    @Override
    public void onGetListAccount() {
        getView().showProgressRdn();
        if(isLoading)
            return;

        if (isViewAttached()){
            isLoading = true;
            String seqNum = getBRImoPrefRepository().getSeqNumber();
            getCompositeDisposable().add(
                    getApiSource().getData(url, "", seqNum)
                            .subscribeOn(getSchedulerProvider().single())
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .observeOn(getSchedulerProvider().mainThread())
                            .subscribeWith(new ApiObserver(getView(),seqNum) {
                                @Override
                                protected void onFailureHttp(String type) {
                                    getView().onException(type);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SUCCESS.getValue())){
                                        RdnAccountResponse rdnAccountResponse = response.getData(RdnAccountResponse.class);
                                        if (listRdnAccount.isEmpty()) {
                                            listRdnAccount.addAll(rdnAccountResponse.getAccountRdn());
                                        } else {
                                            listRdnAccount.clear();
                                            listRdnAccount.addAll(rdnAccountResponse.getAccountRdn());
                                        }
                                        getView().onSuccessGetAccount(rdnAccountResponse);
                                    }
                                    else if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_01.getValue())){
                                        RdnDashboardFailureResponse rdnDashboardFailureResponse = response.getData(RdnDashboardFailureResponse.class);
                                        getView().onException01(rdnDashboardFailureResponse.getTitle(),rdnDashboardFailureResponse.getDescription());
                                        getView().hideProgressRdn();
                                    }
                                    else if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_02.getValue())){
                                        getView().onException02("Halaman Gagal Dimuat",response.getDesc());
                                        getView().hideProgressRdn();
                                    }
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {

                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue())) {
                                        getView().onSessionEnd(restResponse.getDesc());
                                        getView().hideProgressRdn();
                                    }
                                    else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_99.getValue())){
                                        getView().onException02("",restResponse.getDesc());
                                        getView().hideProgressRdn();
                                    }
                                    else {
                                        getView().onException(restResponse.getDesc());
                                        getView().hideProgressRdn();
                                    }
                                }

                                @Override
                                public void onComplete() {
                                    onGetDetailAccount(listRdnAccount,false);
                                }
                            })
            );
        }
    }

    /**
     * RC00 -> ke onBorading
     * RC01 -> ke checkpoint
     */
    @Override
    public void onGetOnBoarding() {
        if (isViewAttached()) {
            getView().showProgress();
            OnboardingRDNSBNRequest request = new OnboardingRDNSBNRequest(getBRImoPrefRepository().getFirstRdn());
            String seqNum = getBRImoPrefRepository().getSeqNumber();
            getCompositeDisposable().add(getApiSource().getData(urlOnboarding, request, seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(new ApiObserver(getView(),seqNum) {
                        @Override
                        protected void onFailureHttp(String errorMessage) {
                            getView().hideProgress();
                            getView().onException(errorMessage);
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SUCCESS.getValue())){
                                RdnOnBoardingResponse rdnOnBoardingResponse = response.getData(RdnOnBoardingResponse.class);
                                getView().onSuccessOnBoarding(rdnOnBoardingResponse);
                                getView().hideProgress();
                            }
                            else if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_01.getValue())){
                                RdnOnCheckpointResponse rdnOnBoardingResponse = response.getData(RdnOnCheckpointResponse.class);
                                getView().onSuccessCheckPoint(rdnOnBoardingResponse);
                                getView().hideProgress();
                            }
                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            getView().hideProgress();
                            if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                getView().onSessionEnd(restResponse.getDesc());
                            else
                                getView().onException(restResponse.getDesc());
                        }
                    }));
        }
    }

    @Override
    public void onGetDetailAccount(List<RdnAccountResponse.AccountRdn> list,boolean isRefreshed) {
        getView().showProgressRdn();
        getCompositeDisposable().add(Observable.fromIterable(list).flatMap(new Function<RdnAccountResponse.AccountRdn, ObservableSource<RdnAccountResponse.AccountRdn>>() {
            @Override
            public ObservableSource<RdnAccountResponse.AccountRdn> apply(RdnAccountResponse.AccountRdn accountRdn) throws Exception {
                return getSaldoRdn(accountRdn);
            }
        }).subscribeWith(new DisposableObserver<RdnAccountResponse.AccountRdn>(){

            @Override
            public void onNext(RdnAccountResponse.AccountRdn accountRdn) {
                int postion = list.indexOf(accountRdn);
                if (postion == -1)
                    return;
                list.set(postion, accountRdn);
                getView().onGetSaldo(list, isRefreshed,postion);
                getView().showSaldoRdn();
            }

            @Override
            public void onError(Throwable e) {
                getView().hideProgressRdn();
            }

            @Override
            public void onComplete() {
                getView().onGetSaldoComplete();
                isLoading = false;
            }
        }));
    }

    @Override
    public Observable<RdnAccountResponse.AccountRdn> getSaldoRdn(RdnAccountResponse.AccountRdn accountRdn) {
        String seqNum = getBRImoPrefRepository().getSeqNumber();

        RdnInfoSaldoRequest request = new RdnInfoSaldoRequest(accountRdn.getAccount());

        return getApiSource().getData(urlInfoSaldo,request,seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                .subscribeOn(getSchedulerProvider().newThread())
                .observeOn(getSchedulerProvider().mainThread())
                .map(new Function<String, RdnAccountResponse.AccountRdn>() {
                    @Override
                    public RdnAccountResponse.AccountRdn apply(String stringResponse) throws Exception {
                        SaldoReponse saldoReponse;


                        //get checksum response
                        String responseCheck = MapperHelper.getIdResponse(stringResponse);

                        //jika checksum response kosong
                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.GINGERBREAD) {
                            if (responseCheck.isEmpty()) {
                                saldoReponse = new SaldoReponse();
                                saldoReponse.setBalanceString("-");
                            }
                        }

                        //coba konversi String ke RestResponse model
                        RestResponse restResponse = MapperHelper.stringToRestResponse(stringResponse, seqNum);

                        if (restResponse != null) {

                            if (GeneralHelper.isContains(R.array.response_code_success, restResponse.getCode())) {

                                if (restResponse.getDesc().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_01.getValue())) {
                                    saldoReponse = new SaldoReponse();
                                    saldoReponse.setBalanceString(restResponse.getDesc());
                                } else {
                                    saldoReponse = new SaldoReponse();
                                    saldoReponse = restResponse.getData(SaldoReponse.class);

                                    //jika rekening adalah default maka saldo local diupdate
                                }
                            } else if (restResponse.getCode().equals(RestResponse.ResponseCodeEnum.RC_12.getValue())) {
                                saldoReponse = new SaldoReponse();
                                saldoReponse.setBalanceString(restResponse.getCode());
                                saldoReponse.setName(restResponse.getDesc());
                                getView().onExceptionTotalSaldo();

                            } else if (restResponse.getCode().equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue())) {
                                saldoReponse = new SaldoReponse();
                                saldoReponse.setBalanceString(restResponse.getCode());
                                getView().onSessionEnd(restResponse.getDesc());
                            } else {
                                saldoReponse = new SaldoReponse();
                                saldoReponse.setBalanceString("-");
                            }
                        } else {
                            saldoReponse = new SaldoReponse();
                            saldoReponse.setBalanceString("-");
                        }
                        accountRdn.setSaldoReponse(saldoReponse);
                        return accountRdn;
                    }
                });
    }

    @Override
    public void getStatusProgress() {
        if(isViewAttached()){
            String seqNum = getBRImoPrefRepository().getSeqNumber();
            getView().showProgress();
            getCompositeDisposable().add(
                    getApiSource().getData(urlStatusProgress, "", seqNum)
                            .subscribeOn(getSchedulerProvider().single())
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .observeOn(getSchedulerProvider().mainThread())
                            .subscribeWith(new ApiObserver(getView(),seqNum){

                                @Override
                                protected void onFailureHttp(String errorMessage) {
                                    getView().hideProgress();
                                    getView().onException(errorMessage);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    getView().hideProgress();
                                    RdnProgressResponse responResend = response.getData(RdnProgressResponse.class);
                                    getView().onSuccesGetProgress(responResend);
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().hideProgress();
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                        getView().onSessionEnd(restResponse.getDesc());
                                    else
                                        getView().onException(restResponse.getDesc());
                                }
                            })
            );
        }
    }

    @Override
    public void saveBackGeneral(int status) {
        getBRImoPrefRepository().saveBackGeneral(status);
    }

    @Override
    public void saveFirstRdn(boolean firstRdn) {
        getBRImoPrefRepository().saveFirstRdn(firstRdn);
    }

    @Override
    public void setUrlInquiryRdn(String url) {
        urlInquiryRdn = url;
    }

    @Override
    public void getTopUpRdn(InquiryRdnRequest request) {
        if(isViewAttached()){
            String seqNum = getBRImoPrefRepository().getSeqNumber();
            getView().showProgress();
            getCompositeDisposable().add(
                    getApiSource().getData(urlInquiryRdn, request, seqNum)
                            .subscribeOn(getSchedulerProvider().single())
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .observeOn(getSchedulerProvider().mainThread())
                            .subscribeWith(new ApiObserver(getView(),seqNum){

                                @Override
                                protected void onFailureHttp(String errorMessage) {
                                    getView().hideProgress();
                                    getView().onException(errorMessage);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    getView().hideProgress();
                                    InquiryRdnResponse responResend = response.getData(InquiryRdnResponse.class);
                                    getView().onSuccessInquiry(responResend);
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().hideProgress();
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                        getView().onSessionEnd(restResponse.getDesc());
                                    else
                                        getView().onException(restResponse.getDesc());
                                }
                            })
            );
        }
    }

    @Override
    public void start() {
        super.start();
        onGetListAccount();
    }

    @Override
    public void stop() {
        if(isViewAttached())
            getView().onGetSaldoComplete();
        super.stop();
    }
}