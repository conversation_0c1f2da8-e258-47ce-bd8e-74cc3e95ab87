package id.co.bri.brimo.presenters.dplkrevamp

import id.co.bri.brimo.contract.IPresenter.dplkrevamp.IPerformanceDetailDplkPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.dplkrevamp.IDetailPerformanceTabView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.config.AppConfig
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.dplkrevamp.ProductFtuDplkRequest
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.dplkrevamp.DetailPerformanceTabDplkResponse
import id.co.bri.brimo.presenters.MvpPresenter
import id.co.bri.brimo.util.extension.fromJsonToObject
import id.co.bri.brimo.util.extension.fromObjectToJson
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.Schedulers
import java.util.concurrent.TimeUnit

class PerformanceDetailDplkPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBRImoPrefRepository: BRImoPrefSource, apiSource: ApiSource,
    transaksiPfmSource: TransaksiPfmSource,
) : MvpPresenter<V>(
    schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource,
    transaksiPfmSource
), IPerformanceDetailDplkPresenter<V> where V : IMvpView?, V : IDetailPerformanceTabView {

    private var mUrlDetailPerformanceTab: String = ""
    override fun setUrlDetailPerformanceTabDplk(urlDetailPerformanceTab: String) {
        mUrlDetailPerformanceTab = urlDetailPerformanceTab
    }

    override fun getDetailPerformanceTabDplk(request: ProductFtuDplkRequest) {
        if (!isViewAttached) {
            return
        }
        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable
            .add(
                apiSource.getData(mUrlDetailPerformanceTab, request, seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView().onException(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            val data =
                                response.getData(DetailPerformanceTabDplkResponse::class.java)
                            getView().onSuccessDetailPerformanceDplk(data)
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView().onException(restResponse.desc)
                        }
                    })
            )
    }

    override fun saveDataGraphicDplk(response: DetailPerformanceTabDplkResponse) {
        getBRImoPrefRepository().saveDataGrapichDplk(response.fromObjectToJson())
    }

    override fun getDataGraphicDplk(): DetailPerformanceTabDplkResponse {
        return getBRImoPrefRepository().getDataGraphicDplk()
            .fromJsonToObject(DetailPerformanceTabDplkResponse())
    }
}