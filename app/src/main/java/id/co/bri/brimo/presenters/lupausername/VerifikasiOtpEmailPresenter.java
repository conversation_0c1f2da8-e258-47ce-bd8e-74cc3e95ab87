package id.co.bri.brimo.presenters.lupausername;

import java.util.concurrent.TimeUnit;

import id.co.bri.brimo.contract.IPresenter.lupausername.IVerifikasiOtpEmailPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.lupausername.IVerifikasiOtpEmailView;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.config.AppConfig;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.apimodel.request.GeneralResendOtpRequest;
import id.co.bri.brimo.models.apimodel.request.forgetuserpass.ValidateOtpUserPassReq;
import id.co.bri.brimo.models.apimodel.response.OtpForgotUsernameRes;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.models.apimodel.response.forgetuserpass.OtpEmailRes;
import id.co.bri.brimo.presenters.MvpPresenter;
import io.reactivex.disposables.CompositeDisposable;

public class VerifikasiOtpEmailPresenter<V extends IVerifikasiOtpEmailView & IMvpView> extends MvpPresenter<V> implements IVerifikasiOtpEmailPresenter<V> {

    protected String urlValidate;
    protected String urlResend;

    public VerifikasiOtpEmailPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable,
                                       BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource,
                                       TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }


    @Override
    public void setUrlValidate(String url) {
        urlValidate = url;
    }

    @Override
    public void setUrlResend(String url) {
        urlResend = url;
    }

    @Override
    public void resendOtp(GeneralResendOtpRequest request) {
        if (urlResend.isEmpty() || !isViewAttached()) return;

        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();

        getCompositeDisposable().add(
                getApiSource().getData(urlResend, request, seqNum)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(getSchedulerProvider().io())
                        .observeOn(getSchedulerProvider().mainThread())
                        .subscribeWith(new ApiObserver(getView(), seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().hideProgress();
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                getView().hideProgress();
                                OtpEmailRes otpEmailRes = response.getData(OtpEmailRes.class);
                                getView().onSuccessResendOtp(otpEmailRes);
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                getView().onException(restResponse.getDesc());
                            }
                        })
        );
    }

    @Override
    public void validateOtp(ValidateOtpUserPassReq request, boolean forgetUsername) {
        if (urlValidate.isEmpty() || !isViewAttached()) return;

        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();

        getCompositeDisposable().add(
                getApiSource().getData(urlValidate, request, seqNum)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(getSchedulerProvider().io())
                        .observeOn(getSchedulerProvider().mainThread())
                        .subscribeWith(new ApiObserver(getView(), seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().hideProgress();
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                getView().hideProgress();
                                OtpForgotUsernameRes forgotResponse = response.getData(OtpForgotUsernameRes.class);
                                if (forgetUsername)
                                    getView().onSuccessValidateOtp(forgotResponse);
                                else
                                    getView().onSuccessForgetPassword(forgotResponse.getReNum());
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                getView().deleteInputOtp();
                                if (restResponse.getCode().equals(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.getValue())) {
                                    getView().onExceptionTrxExpired(restResponse.getDesc());
                                } else {
                                    getView().onException(restResponse.getDesc());
                                }
                            }
                        })
        );
    }
}