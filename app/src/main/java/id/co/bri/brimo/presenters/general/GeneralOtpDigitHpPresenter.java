package id.co.bri.brimo.presenters.general;

import java.util.concurrent.TimeUnit;

import id.co.bri.brimo.contract.IPresenter.general.IGeneralOtpDigitHpPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.general.IGeneralOtpDigitHpView;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.config.AppConfig;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.apimodel.request.GeneralOtpDigitHpReq;
import id.co.bri.brimo.models.apimodel.response.MagicLupaPassResponse;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.presenters.MvpPresenter;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.schedulers.Schedulers;

public class GeneralOtpDigitHpPresenter<V extends IMvpView & IGeneralOtpDigitHpView>
        extends MvpPresenter<V> implements IGeneralOtpDigitHpPresenter<V> {

    protected String url;

    public GeneralOtpDigitHpPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable,
                                      BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource,
                                      TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void setUrl(String url) {
        this.url = url;
    }

    @Override
    public void sendDataConfirm(GeneralOtpDigitHpReq request) {
        if (isViewAttached()) {
            String seqNum = getBRImoPrefRepository().getSeqNumber();

            getView().showProgress();
            getCompositeDisposable().add(getApiSource().getData(url, request, seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(new ApiObserver(getView(),seqNum) {
                        @Override
                        protected void onFailureHttp(String errorMessage) {
                            getView().hideProgress();
                            getView().onException(errorMessage);
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            getView().hideProgress();

                            MagicLupaPassResponse magicResponse = response.getData(MagicLupaPassResponse.class);
                            getView().onDataSuccess(magicResponse);
                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            getView().hideProgress();
                            if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.getValue()))
                                getView().onException93(restResponse.getDesc());
                            else
                                getView().onException(restResponse.getDesc());
                        }
                    }));

        }
    }
}