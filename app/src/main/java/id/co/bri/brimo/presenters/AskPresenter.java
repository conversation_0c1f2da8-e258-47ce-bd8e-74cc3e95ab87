package id.co.bri.brimo.presenters;

import id.co.bri.brimo.contract.IPresenter.IAskPresenter;
import id.co.bri.brimo.contract.IView.IAskView;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import io.reactivex.disposables.CompositeDisposable;

public class AskPresenter<V extends IMvpView & IAskView>
        extends MvpPresenter<V> implements IAskPresenter<V> {
    private String TAG = "AskPresenterTAG";

    public AskPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable,
                        BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource,
                        TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource,
                transaksiPfmSource);
    }



    @Override
    public String getPersistenceId() {
        return getBRImoPrefRepository().getDeviceId2();
    }


}
