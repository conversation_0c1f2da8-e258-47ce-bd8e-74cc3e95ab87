package id.co.bri.brimo.presenters.dplk;

import android.util.Log;

import com.google.gson.Gson;

import java.util.concurrent.TimeUnit;

import id.co.bri.brimo.contract.IPresenter.dplk.IInquiryOpenDplkPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.dplk.IInquiryOpenDplkView;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.config.AppConfig;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.apimodel.request.KonfirmasiSimpedesOpenRequest;
import id.co.bri.brimo.models.apimodel.response.KonfirmasiBrifineResponse;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.presenters.MvpPresenter;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.schedulers.Schedulers;

public class InquiryOpenDplkPresenter<V extends IMvpView & IInquiryOpenDplkView> extends MvpPresenter<V> implements IInquiryOpenDplkPresenter<V> {

    protected String urlKonfirmasi;

    public InquiryOpenDplkPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void setUrlKonfirmasi(String urlKonfirmasi) {
        this.urlKonfirmasi = urlKonfirmasi;
    }

    @Override
    public void getDataKonfirmasi(String refNum, String account) {
        if (isViewAttached()) {

            KonfirmasiSimpedesOpenRequest konfirmasiOpenRequest = new KonfirmasiSimpedesOpenRequest(refNum, account);

            Log.i("TAG", "getDataKonfirmasi: "+new Gson().toJson(konfirmasiOpenRequest));

            getView().showProgress();
            String seq = getBRImoPrefRepository().getSeqNumber();
            getCompositeDisposable()
                    .add(getApiSource().getData(urlKonfirmasi, konfirmasiOpenRequest, seq)
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .subscribeOn(Schedulers.io())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribeWith(new ApiObserver(getView(), seq) {

                                @Override
                                protected void onFailureHttp(String errorMessage) {
                                    getView().hideProgress();
                                    getView().onException(errorMessage);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    getView().hideProgress();
                                    KonfirmasiBrifineResponse generalKonfirmasiResponse = response.getData(KonfirmasiBrifineResponse.class);
                                    getView().getConfirmSuccess(generalKonfirmasiResponse);
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().hideProgress();
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                        getView().onSessionEnd(restResponse.getDesc());
                                    else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.getValue()))
                                        getView().onException93(restResponse.getDesc());
                                    else
                                        getView().onException(restResponse.getDesc());

                                }
                            }));

        }
    }

    protected void getDefaultSaldo() {
        double saldo = 0.0;
        String saldoText = getBRImoPrefRepository().getSaldoRekeningUtama();
        if (!saldoText.equals("")) {
            saldo = Double.valueOf(saldoText);
        }

        String akunDefault = getBRImoPrefRepository().getAccountDefault();
        String saldoString = getBRImoPrefRepository().getSaldoRekeningUtamaString();
        boolean saldoHold = getBRImoPrefRepository().getSaldoHold();

        getView().setDefaultSaldo(saldo, saldoString, akunDefault, saldoHold);
    }

    @Override
    public void start() {
        super.start();
        getDefaultSaldo();
    }
}