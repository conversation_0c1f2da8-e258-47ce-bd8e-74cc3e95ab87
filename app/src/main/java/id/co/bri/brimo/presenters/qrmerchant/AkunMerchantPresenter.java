package id.co.bri.brimo.presenters.qrmerchant;

import id.co.bri.brimo.contract.IPresenter.qrmerchant.IAkunMerchantPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.qrmerchant.IAkunMerchantView;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.config.AppConfig;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.apimodel.request.AkunMerchantRequest;
import id.co.bri.brimo.models.apimodel.response.AkunMerchantResponse;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.presenters.MvpPresenter;

import java.util.concurrent.TimeUnit;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.schedulers.Schedulers;

public class AkunMerchantPresenter <V extends IMvpView & IAkunMerchantView> extends MvpPresenter<V> implements IAkunMerchantPresenter<V> {

    private static final String TAG = "AkunMerchantPresenter";

    protected String url;

    public AkunMerchantPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void setUrl(String url) {
        this.url = url;
    }

    @Override
    public void getDetailAkunMerchant() {
        if (url == null || !isViewAttached()) {
            return;
        }

        AkunMerchantRequest akunMerchantRequest = new AkunMerchantRequest(getBRImoPrefRepository().getMidMerchant());

        String seqNum = getBRImoPrefRepository().getSeqNumber();

        getCompositeDisposable().add(
                getApiSource().getData(url, akunMerchantRequest, seqNum)//function(param)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(Schedulers.io())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(new ApiObserver(getView(),seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                AkunMerchantResponse akunMerchantResponse = response.getData(AkunMerchantResponse.class);
//                                getBRImoPrefRepository().saveMidMerchant(merchantStatusResponse.getMerchantList().get(0).getMerchantId());
                                getView().onSuccessGetAkun(akunMerchantResponse);
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                    getView().onSessionEnd(restResponse.getDesc());
                                else
                                    getView().onException(restResponse.getDesc());
                            }
                        })
        );
    }

    @Override
    public String getMid() {
        return getBRImoPrefRepository().getMidMerchant();
    }

}