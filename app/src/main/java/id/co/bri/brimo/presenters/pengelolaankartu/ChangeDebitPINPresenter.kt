package id.co.bri.brimo.presenters.pengelolaankartu

import id.co.bri.brimo.contract.IPresenter.pengelolaankartu.IChangeDebitPINPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.pengelolaankartu.IChangeDebitPINView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.pengelolaankartu.ChangePinCcRequest
import id.co.bri.brimo.models.apimodel.request.pengelolaankartu.ChangePinRequest
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.disposables.CompositeDisposable

class ChangeDebitPINPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBRImoPrefRepository: BRImoPrefSource,
    apiSource: ApiSource,
    transaksiPfmSource: TransaksiPfmSource
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    mBRImoPrefRepository,
    apiSource,
    transaksiPfmSource
), IChangeDebitPINPresenter<V> where V : IMvpView, V : IChangeDebitPINView {

    private var urlChangePin: String? = null

    override fun setUrlChangePin(url: String?) {
        urlChangePin = url
    }

    override fun changePin(
        authPin: String?,
        newCardPin: String?,
        oldCardPin: String?,
        cardNumber: String?
    ) {
        if (urlChangePin == null) return

        val seqNumb = brImoPrefRepository.seqNumber
        val request = ChangePinRequest(
            authPin = authPin,
            cardNumber = cardNumber,
            oldPin = oldCardPin,
            newPin = newCardPin,
        )

        view?.showProgress()

        compositeDisposable.add(
            apiSource.getData(urlChangePin, request, seqNumb)
                .subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNumb) {
                    override fun onFailureHttp(type: String?) {
                        getView()?.hideProgress()
                        getView()?.onException(type)
                    }

                    override fun onApiCallSuccess(response: RestResponse?) {
                        getView().hideProgress()
                        response?.code?.let { code ->
                            when (code) {
                                Constant.RE_SUCCESS -> response.desc?.let { getView().onSuccessChangePin(it) }
                                Constant.RE_MAX_RETRIES_DEBIT -> getView()?.onMaximumRetries(response.desc)
                                else -> getView()?.onErrorPin()
                            }
                        }
                    }

                    override fun onApiCallError(restResponse: RestResponse?) {
                        getView().hideProgress()
                        getView()?.onErrorPin()
                    }
                })
        )
    }

    override fun changePinCc(
        authPin: String?,
        newCardPin: String?,
        oldCardPin: String?,
        cardToken: String?
    ) {
        if (urlChangePin == null) return

        val seqNumb = brImoPrefRepository.seqNumber
        val request = ChangePinCcRequest(
            authPin = authPin,
            newPin = newCardPin,
            oldPin = oldCardPin,
            cardToken = cardToken
        )

        view?.showProgress()

        compositeDisposable.add(
            apiSource.getData(urlChangePin, request, seqNumb)
                .subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNumb) {
                    override fun onFailureHttp(type: String?) {
                        getView()?.hideProgress()
                        getView()?.onException(type)
                    }

                    override fun onApiCallSuccess(response: RestResponse?) {
                        getView().hideProgress()
                        response?.code?.let { code ->
                            when (code) {
                                Constant.RE_SUCCESS -> response.desc?.let { getView().onSuccessChangePin(it) }
                                else -> getView()?.onException(response.desc)
                            }
                        }
                    }

                    override fun onApiCallError(restResponse: RestResponse?) {
                        getView().hideProgress()
                        restResponse?.code?.let { code ->
                            when (code) {
                                Constant.RE_MAX_RETRIES_CC -> restResponse.desc?.let { getView().onMaximumRetries(it) }
                                else -> getView()?.onErrorPin()
                            }
                        }
                    }
                })
        )
    }
}