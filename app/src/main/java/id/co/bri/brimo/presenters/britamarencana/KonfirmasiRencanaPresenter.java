package id.co.bri.brimo.presenters.britamarencana;

import java.util.concurrent.TimeUnit;

import id.co.bri.brimo.contract.IPresenter.britamarencana.IKonfirmasiRencanaPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.britamarencana.IKonfirmasiRencanaView;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.config.AppConfig;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.apimodel.request.KonfirmasiEditRequest;
import id.co.bri.brimo.models.apimodel.request.KonfirmasiRencanaRequest;
import id.co.bri.brimo.models.apimodel.response.KonfirmasiRencanaResponse;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.presenters.MvpPresenter;
import io.reactivex.disposables.CompositeDisposable;

public class KonfirmasiRencanaPresenter<V extends IMvpView & IKonfirmasiRencanaView> extends MvpPresenter<V>
        implements IKonfirmasiRencanaPresenter<V> {

    String url;

    public KonfirmasiRencanaPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void start() {
        super.start();
        this.getDefaultSaldo();
    }

    protected void getDefaultSaldo() {
        double saldo = 0.0;
        String saldoText = getBRImoPrefRepository().getSaldoRekeningUtama();
        if (!saldoText.equals("")) {
            saldo = Double.valueOf(saldoText);
        }

        String akunDefault = getBRImoPrefRepository().getAccountDefault();
        String saldoString = getBRImoPrefRepository().getSaldoRekeningUtamaString();

        getView().setDefaultSaldo(saldo, saldoString, akunDefault);
    }

    @Override
    public void setUrl(String url) {
        this.url = url;
    }

    @Override
    public void getKonfirmasiRencana(KonfirmasiRencanaRequest konfirmasiRencanaRequest) {
        getView().showProgress();
        if (url == null || !isViewAttached()) {
            return;
        }

        String seqNum = getBRImoPrefRepository().getSeqNumber();

        getCompositeDisposable().add(
                getApiSource().getData(url, konfirmasiRencanaRequest, seqNum)//function(param)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(getSchedulerProvider().single())
                        .observeOn(getSchedulerProvider().mainThread())
                        .subscribeWith(new ApiObserver(getView(),seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().hideProgress();
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                getView().hideProgress();
                                KonfirmasiRencanaResponse konfirmasiRencanaResponse = response.getData(KonfirmasiRencanaResponse.class);
                                getView().onSuccessKonfirmasiRencana(konfirmasiRencanaResponse);
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                if (restResponse.getCode().equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                    getView().onSessionEnd(restResponse.getDesc());
                                else
                                    getView().onException(restResponse.getDesc());
                            }
                        })
        );
    }

    @Override
    public void getKonfirmasiEditRencana(KonfirmasiEditRequest konfirmasiEditRequest) {
        getView().showProgress();
        if (url == null || !isViewAttached()) {
            return;
        }

        String seqNum = getBRImoPrefRepository().getSeqNumber();

        getCompositeDisposable().add(
                getApiSource().getData(url, konfirmasiEditRequest, seqNum)//function(param)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(getSchedulerProvider().single())
                        .observeOn(getSchedulerProvider().mainThread())
                        .subscribeWith(new ApiObserver(getView(),seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().hideProgress();
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                getView().hideProgress();
                                KonfirmasiRencanaResponse konfirmasiRencanaResponse = response.getData(KonfirmasiRencanaResponse.class);
                                getView().onSuccessKonfirmasiRencana(konfirmasiRencanaResponse);
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                if (restResponse.getCode().equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                    getView().onSessionEnd(restResponse.getDesc());
                                else
                                    getView().onException(restResponse.getDesc());
                            }
                        })
        );
    }
}