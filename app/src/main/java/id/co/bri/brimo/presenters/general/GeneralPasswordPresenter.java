package id.co.bri.brimo.presenters.general;

import java.util.concurrent.TimeUnit;

import id.co.bri.brimo.contract.IPresenter.general.IGeneralPasswordPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.general.IGeneralPasswordView;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.config.AppConfig;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.apimodel.request.ForgetPassRequest;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.presenters.MvpPresenter;
import io.reactivex.disposables.CompositeDisposable;

public class GeneralPasswordPresenter<V extends IMvpView & IGeneralPasswordView> extends MvpPresenter<V> implements IGeneralPasswordPresenter<V> {

    private String urlForPass;

    public GeneralPasswordPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void setUrlForgetPass(String urlForPass) {
        this.urlForPass = urlForPass;
    }

    @Override
    public void sendDataForPass(ForgetPassRequest request) {
        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();

        if (getView() != null) {
            getCompositeDisposable().add(
                    getApiSource().getData(urlForPass, request, seqNum)
                            .subscribeOn(getSchedulerProvider().single())
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .observeOn(getSchedulerProvider().mainThread())
                            .subscribeWith(new ApiObserver(getView(),seqNum) {
                                @Override
                                protected void onFailureHttp(String type) {
                                    getView().hideProgress();
                                    getView().onException(type);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    getView().hideProgress();
                                    getView().onDataSuccess();
                                    //delete value key biometric dan sett status aktivasi false
                                    getBRImoPrefRepository().deleteValueKeyBiometric();
                                    getBRImoPrefRepository().saveStatusAktivasi(false);
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().hideProgress();
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.getValue())) {
                                        getView().onException93(restResponse.getDesc());
                                    }
                                    getView().onException(restResponse.getDesc());
                                }
                            })
            );
        }
    }
}