package id.co.bri.brimo.presenters.pulsarevamp.reskin

import android.os.Parcelable
import com.google.gson.annotations.Expose
import com.google.gson.annotations.SerializedName
import id.co.bri.brimo.contract.IPresenter.pulsarevamp.reskin.IPulsaDataReskinPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.pulsarevamp.reskin.IPulsaDataReskinView
import id.co.bri.brimo.contract.IView.pulsarevamp.reskin.PulsaDataException
import id.co.bri.brimo.contract.IView.pulsarevamp.reskin.PulsaDataResult
import id.co.bri.brimo.contract.IView.pulsarevamp.reskin.PulsaErrorCode
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiReskinObserver
import id.co.bri.brimo.data.api.observer.ResExceptionErr
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimo.data.repository.category.CategoryPfmSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.response.GeneralConfirmationResponse
import id.co.bri.brimo.models.apimodel.response.ReceiptRevampResponse
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.pulsarevamp.FormPulsaDataResponse
import id.co.bri.brimo.presenters.base.BaseTransactionPresenter
import id.co.bri.brimo.presenters.listrikrevamp.reskin.FavoriteType
import id.co.bri.brimo.util.subscribeWithObserver
import io.reactivex.disposables.CompositeDisposable
import kotlinx.parcelize.Parcelize
import javax.inject.Inject

open class PulsaDataReskinPresenter<V> @Inject constructor(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBRImoPrefRepository: BRImoPrefSource,
    apiSource: ApiSource,
    categoryPfmSource: CategoryPfmSource,
    transaksiPfmSource: TransaksiPfmSource,
    anggaranPfmSource: AnggaranPfmSource
) : BaseTransactionPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    mBRImoPrefRepository,
    apiSource,
    transaksiPfmSource
), IPulsaDataReskinPresenter<V> where V : IMvpView, V: IPulsaDataReskinView {
    override fun <Req, Res> executeRequest(
        url: String,
        requestParam: Req?,
        responseType: Class<Res>,
        isSaved: FavoriteType?
    ) {
        val seqNum = brImoPrefRepository.seqNumber
        val sObserve = if(requestParam!=null)
            apiSource.getData(url, requestParam, seqNum)
        else apiSource.getDataForm(url, seqNum)

        if (isViewAttached) {
            sObserve.subscribeWithObserver(
                compositeDisposable = compositeDisposable,
                schedulerProvider = schedulerProvider,
                createObserver = {
                    object: ApiReskinObserver(view, seqNum) {
                        override fun onApiCallSuccess(response: RestResponse) {
                            getView().hideProgress()
                            if(RestResponse::class.java.isAssignableFrom(responseType)) {
                                if(isSaved!=null) {
                                    val result = mapDataToPulsaDataResult(isSaved)
                                    (getView() as IPulsaDataReskinView).onSuccess(result)
                                } else {
                                    val result = mapDataToPulsaDataResult(response)
                                    (getView() as IPulsaDataReskinView).onSuccess(result)
                                }
                            } else {
                                val resData = response.getData(responseType)

                                val result = mapDataToPulsaDataResult(resData)
                                (getView() as IPulsaDataReskinView).onSuccess(result)
                            }
                        }

                        override fun onApiCallError(errRes: ResExceptionErr) {
                            println("onApiCallError: ${errRes.code}")
                            val exception = mapErrorToPulsaDataException(errRes)
                            (getView() as IPulsaDataReskinView).onExceptionReskin(exception)
                        }
                    }
                }
            )
        }
    }

    private fun <Res> mapDataToPulsaDataResult(data: Res): PulsaDataResult {
        return when (data) {
            is FormPulsaDataResponse -> PulsaDataResult.Form(data)
            is ReceiptRevampResponse -> PulsaDataResult.Receipt(data)
            is GeneralConfirmationResponse -> PulsaDataResult.Confirmation(data)
            is FavoriteType -> PulsaDataResult.Favorite(data)
            else -> PulsaDataResult.Other(data as Any)
        }
    }

    private fun mapErrorToPulsaDataException(errRes: ResExceptionErr): PulsaDataException {
        val code = errRes.code

        return when (code) {
            PulsaErrorCode.EXCEPTION_93.code ->
                PulsaDataException.KnownError(code, errRes)
            PulsaErrorCode.EXCEPTION_12.code ->
                PulsaDataException.KnownError(code, errRes)
            PulsaErrorCode.EXCEPTION_01.code ->
                PulsaDataException.KnownError(code, errRes)
            PulsaErrorCode.EXCEPTION_61.code ->
                PulsaDataException.KnownError(code, errRes)
            else ->
                PulsaDataException.UnknownError(code, errRes)
        }
    }
}

@Parcelize
class PaymentNS (
    @SerializedName("reference_number") @Expose
    var referenceNumber: String? = null,
    @SerializedName("pin")
    @Expose
    val pin: String? = null,
    @SerializedName("account_number")
    @Expose
    val accountNumber: String? = null,
    @SerializedName("pfm_category")
    @Expose
    val pfmCategory: String? = null,
    @SerializedName("save_as")
    @Expose
    val saveAs: String="",
): Parcelable