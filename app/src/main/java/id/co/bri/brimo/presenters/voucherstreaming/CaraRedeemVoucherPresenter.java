package id.co.bri.brimo.presenters.voucherstreaming;

import java.util.concurrent.TimeUnit;

import id.co.bri.brimo.contract.IPresenter.voucherstreaming.ICaraRedeemVoucherPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.voucherstreaming.ICaraRedeemVoucherView;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.config.AppConfig;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.apimodel.request.DetailVoucherStreamingRequest;
import id.co.bri.brimo.models.apimodel.response.CaraRedeemVoucherResponse;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.presenters.MvpPresenter;
import io.reactivex.disposables.CompositeDisposable;

public class CaraRedeemVoucherPresenter<V extends IMvpView & ICaraRedeemVoucherView> extends MvpPresenter<V> implements ICaraRedeemVoucherPresenter<V> {

    protected String urlCaraRedeemUrl;

    public CaraRedeemVoucherPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void setCaraRedeemUrl(String caraRedeemUrl) {
        this.urlCaraRedeemUrl = caraRedeemUrl;
    }

    @Override
    public void getCaraRedeem(DetailVoucherStreamingRequest detailVoucherStreamingRequest) {
        if (isViewAttached()) {

            String seqNum = getBRImoPrefRepository().getSeqNumber();
            getView().showProgress();
            getCompositeDisposable().add(
                    getApiSource().getData(urlCaraRedeemUrl, detailVoucherStreamingRequest, seqNum)
                            .subscribeOn(getSchedulerProvider().io())
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .observeOn(getSchedulerProvider().mainThread())
                            .subscribeWith(new ApiObserver(getView(),seqNum) {
                                @Override
                                protected void onFailureHttp(String type) {
                                    getView().hideProgress();
                                    getView().onException(type);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    CaraRedeemVoucherResponse caraRedeemVoucherResponse = response.getData(CaraRedeemVoucherResponse.class);
                                    getView().hideProgress();
                                    getView().onSuccessData(caraRedeemVoucherResponse);
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().hideProgress();
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue())){
                                        getView().onSessionEnd(restResponse.getDesc());
                                    }else{
                                        getView().onException(restResponse.getDesc());
                                    }


                                }
                            })
            );
        }
    }
}