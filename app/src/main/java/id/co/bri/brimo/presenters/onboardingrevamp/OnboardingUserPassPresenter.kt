package id.co.bri.brimo.presenters.onboardingrevamp

import id.co.bri.brimo.contract.IPresenter.onboardingrevamp.IOnboardingUserPassPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.onboardingrevamp.IOnboardingUserPassView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimo.data.repository.category.CategoryPfmSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.config.AppConfig
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.onboardingrevamp.OnboardingUserPasRequest
import id.co.bri.brimo.models.apimodel.request.onboardingrevamp.OnboardingUsernameRequest
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.onboardingrevamp.OnboardingUserResponse
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.Schedulers
import java.util.concurrent.TimeUnit

class OnboardingUserPassPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBRImoPrefRepository: BRImoPrefSource,
    apiSource: ApiSource,
    categoryPfmSource: CategoryPfmSource,
    transaksiPfmSource: TransaksiPfmSource,
    anggaranPfmSource: AnggaranPfmSource
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    mBRImoPrefRepository,
    apiSource,
    transaksiPfmSource
), IOnboardingUserPassPresenter<V> where V : IMvpView, V : IOnboardingUserPassView {

    private var urlUsername: String = ""
    private var urlUserPass: String = ""

    override fun setUrlUsername(url: String) {
        urlUsername = url
    }

    override fun sendUserCheck(onboardUsernameRequest: OnboardingUsernameRequest) {
        if (urlUsername.isEmpty() || !isViewAttached) return

        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable.add(
            apiSource.getData(urlUsername, onboardUsernameRequest, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(Schedulers.single())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        // do nothing
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        val onboardUser =
                            response.getData(OnboardingUserResponse::class.java)
                        getView().onSuccessCheckUser(onboardUser)
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView().onExceptionUserExist()
                    }
                })
        )
    }

    override fun setUrlUserPass(url: String) {
        urlUserPass = url
    }

    override fun sendUserPass(onboardUserPassRequest: OnboardingUserPasRequest) {
        if (urlUserPass.isEmpty() || !isViewAttached) return

        view.showProgress()
        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable.add(
            apiSource.getData(urlUserPass, onboardUserPassRequest, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView().hideProgress()
                        getView().onExceptionNoBackAction(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView().hideProgress()
                        getView().onSuccessUserPass()
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView().hideProgress()
                        if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_STATUS_NOT_MATCH.value))
                            getView().onExceptionStatusNotMatch()
                        else getView().onExceptionRevamp(restResponse.desc)
                    }
                })
        )
    }

    override fun getDeviceId(): String {
        return brImoPrefRepository.deviceId
    }

}