package id.co.bri.brimo.presenters.base

import com.google.gson.annotations.Expose
import com.google.gson.annotations.SerializedName
import id.co.bri.brimo.R
import id.co.bri.brimo.contract.IPresenter.base.IBaseTransactionPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.base.IBaseTransactionView
import id.co.bri.brimo.contract.IView.pulsarevamp.reskin.AccountModelNs
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.api.observer.ApiReskinObserver
import id.co.bri.brimo.data.api.observer.ResExceptionErr
import id.co.bri.brimo.data.api.observer.mapping
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.AccountModel
import id.co.bri.brimo.models.apimodel.response.EmptyStateResponse
import id.co.bri.brimo.models.apimodel.response.ExceptionResponse
import id.co.bri.brimo.models.apimodel.response.GeneralResponse
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.SaldoReponse
import id.co.bri.brimo.models.apimodel.response.pulsarevamp.FormPulsaDataResponse
import id.co.bri.brimo.presenters.MvpPresenter
import id.co.bri.brimo.util.DecodeResult
import id.co.bri.brimo.util.ResponseDecoder
import id.co.bri.brimo.util.subscribeWithObserver
import io.reactivex.Observable
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable

abstract class BaseTransactionPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBRImoPrefRepository: BRImoPrefSource,
    apiSource: ApiSource,
    transaksiPfmSource: TransaksiPfmSource
) : MvpPresenter<V>(
    schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource
), IBaseTransactionPresenter<V> where V : IMvpView {
    private var account: AccountModel?= null
    private var accountList: MutableList<AccountModel> = mutableListOf()
    private var isFetching = false
    private var isFm = false

    override fun getAccountList() {
        val mUrl = if(!isFm) GeneralHelper.getString(R.string.url_v5_account_list) else GeneralHelper.getString(R.string.url_v5_account_list_fm) ?: return
        val seqNum = brImoPrefRepository.seqNumber

        apiSource.getDataForm(mUrl, seqNum)
            .subscribeOn(schedulerProvider.io())
            .concatMap { response1 ->
                when (val result = ResponseDecoder.decodeStringResponse(response1, seqNum)) {
                    is DecodeResult.Success -> {
                        val list = result.restResponse
                            .getData(AccountModelNs::class.java)
                            .account.toMutableList()

                        accountList = list
                        account = list.find { it.isDefault == 1 } ?: accountList.firstOrNull()

                        val numberAccount = account?.acoount
                            ?: list.firstOrNull()?.acoount
                            ?: ""

                        apiSource.getData(
                            GeneralHelper.getString(R.string.url_v2_saldo_normal) ?: return@concatMap Observable.empty(),
                            SaldoRequestNS(account = numberAccount),
                            seqNum
                        )
                    }
                    is DecodeResult.Error -> {
                        when (result.restResponse.code) {
                            Constant.RE_SESSION_END -> view.onSessionEnd(result.restResponse.desc)
                            else -> {
                            }
                        }
                        (getView() as IBaseTransactionView).onErrorFetch()
                        Observable.error(Throwable("Decode error"))
                    }
                    is DecodeResult.Exception -> {
                        (getView() as IBaseTransactionView).onErrorFetch()
                        Observable.error(result.throwable)
                    }
                }
            }
            .observeOn(schedulerProvider.mainThread())
            .doFinally {
                isFetching = false
            }
            .subscribeWithObserver(
                compositeDisposable = compositeDisposable,
                schedulerProvider = schedulerProvider,
                createObserver = {
                    object: ApiReskinObserver(view, seqNum) {
                        override fun onApiCallSuccess(response: RestResponse) {
                            val result = response.getData(SaldoReponse::class.java)
                            account?.let{
                                it.saldoReponse = result
                                (getView() as IBaseTransactionView).onSuccessAccountList(accountList, it)
                            }
                        }

                        override fun onApiCallError(errRes: ResExceptionErr) {
                            println("onApiCallError")
                            account?.let{
                                it.saldoReponse = SaldoReponse().apply {
                                    balanceString = "TO"
                                }
                                (getView() as IBaseTransactionView).onSuccessAccountList(accountList, it)
                            }
                        }
                    }
                }
            )
    }

    override fun getSaldo() {
        val mUrl = GeneralHelper.getString(R.string.url_v2_saldo_normal)
        val seqNum = brImoPrefRepository.seqNumber

        apiSource.getData(
            mUrl,
            SaldoRequestNS(account = account?.acoount?: ""),
            seqNum
        ).subscribeWithObserver(
            compositeDisposable = compositeDisposable,
            schedulerProvider = schedulerProvider,
            createObserver = {
                object : ApiReskinObserver(view, seqNum) {
                    override fun onApiCallSuccess(response: RestResponse) {
                        val result = response.getData(SaldoReponse::class.java)
                        account?.let{
                            it.saldoReponse = result
                            (getView() as IBaseTransactionView).onSuccessAccountList(accountList, it)
                        }
                    }
                    override fun onApiCallError(errRes: ResExceptionErr) {
                    }
                }
            }
        )
    }

    override fun isFromFastMenu(isFm: Boolean) {
        this.isFm = isFm
    }
}

class SaldoRequestNS (
    @SerializedName("account") @Expose
    var account: String = "",
)