package id.co.bri.brimo.presenters.rdn;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

import id.co.bri.brimo.contract.IPresenter.rdn.IResultSelfieRdnPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.rdn.IResultSelfieRdnView;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.config.AppConfig;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.apimodel.request.rdn.RdnSelfieRequest;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.models.apimodel.response.rdn.RdnCheckpointResponse;
import id.co.bri.brimo.models.apimodel.response.rdn.RdnDataFormResponse;
import id.co.bri.brimo.models.apimodel.response.rdn.RdnProvinceResponse;
import id.co.bri.brimo.presenters.MvpPresenter;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.schedulers.Schedulers;

public class ResultSelfieRdnPresenter <V extends IMvpView & IResultSelfieRdnView> extends MvpPresenter<V> implements IResultSelfieRdnPresenter<V> {

    private String url;
    private String urlForm;
    private String urlProvince;
    RdnDataFormResponse rdnDataFormResponse;

    List<RdnProvinceResponse> provinceResponseList = new ArrayList<>();

    public ResultSelfieRdnPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void setUrl(String url) {
        this.url = url;
    }

    @Override
    public void setUrlForm(String urlForm) {
        this.urlForm = urlForm;
    }

    @Override
    public void setProvince(String url) { this.urlProvince = url;}

    @Override
    public void onSendSelfie(String image, String checkpoint) {
        if (isViewAttached()){

            RdnSelfieRequest rdnSelfieRequest = new RdnSelfieRequest(image,checkpoint);
            getView().showProgress();
            String seq = getBRImoPrefRepository().getSeqNumber();

            getCompositeDisposable()
                    .add(getApiSource().getData(url,rdnSelfieRequest,seq)
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .subscribeOn(Schedulers.io())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribeWith(new ApiObserver(getView(),seq){

                                @Override
                                protected void onFailureHttp(String type) {
                                    getView().hideProgress();
                                    getView().onException(type);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    RdnCheckpointResponse rdnCheckpointResponse = response.getData(RdnCheckpointResponse.class);
                                    onGetDataForm(rdnCheckpointResponse);
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().hideProgress();
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue())){
                                        getView().onSessionEnd(restResponse.getDesc());
                                    }
                                    else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.getValue())){
                                        getView().onException93(restResponse.getDesc());
                                    }
                                    else{
                                        getView().onException(restResponse.getDesc());
                                    }
                                }
                            }));
        }
    }

    @Override
    public void onGetDataForm(RdnCheckpointResponse rdnCheckpointResponse) {
        if (isViewAttached()){
            String seq = getBRImoPrefRepository().getSeqNumber();

            getCompositeDisposable()
                    .add(getApiSource().getData(urlForm,"",seq)
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .subscribeOn(Schedulers.io())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribeWith(new ApiObserver(getView(),seq){

                                @Override
                                protected void onFailureHttp(String type) {
                                    getView().hideProgress();
                                    getView().onException(type);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    rdnDataFormResponse = response.getData(RdnDataFormResponse.class);
                                    onGetProvince(rdnDataFormResponse, rdnCheckpointResponse);
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().hideProgress();
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                        getView().onSessionEnd(restResponse.getDesc());
                                    else
                                        getView().onException(restResponse.getDesc());
                                }
                            }));
        }
    }

    @Override
    public void onGetProvince(RdnDataFormResponse rdnDataFormResponse, RdnCheckpointResponse rdnCheckpointResponse) {
        String seq = getBRImoPrefRepository().getSeqNumber();

        getCompositeDisposable()
                .add(getApiSource().getData(urlProvince,"",seq)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(Schedulers.io())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(new ApiObserver(getView(),seq){

                            @Override
                            protected void onFailureHttp(String type) {
                                getView().hideProgress();
                                getView().onException(type);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                RdnProvinceResponse rdnProvinceResponse = response.getData(RdnProvinceResponse.class);
                                getView().hideProgress();
                                getView().onSuccessGetProvince(rdnProvinceResponse, rdnCheckpointResponse, rdnDataFormResponse);
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                    getView().onSessionEnd(restResponse.getDesc());
                                else
                                    getView().onException(restResponse.getDesc());
                            }
                        }));
    }
}