package id.co.bri.brimo.presenters.depositorevamp

import id.co.bri.brimo.contract.IPresenter.depositorevamp.IDetailDepositoRevampPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.depositorevamp.IDetailDepositoRevampView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimo.data.repository.category.CategoryPfmSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.config.AppConfig

import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.RefnumRequest
import id.co.bri.brimo.models.apimodel.request.RequestRenewalDeposito
import id.co.bri.brimo.models.apimodel.request.UpdatePerpanjanganDepositoRequest
import id.co.bri.brimo.models.apimodel.response.GeneralConfirmationResponse
import id.co.bri.brimo.models.apimodel.response.RenewalDepositoResponse
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.depositorevamp.InquiryPenutupanDepositoResponseRevamp
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.disposables.CompositeDisposable
import java.util.concurrent.TimeUnit

class DetailDepositoRevampPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBRImoPrefRepository: BRImoPrefSource,
    apiSource: ApiSource,
    categoryPfmSource: CategoryPfmSource,
    transaksiPfmSource: TransaksiPfmSource,
    anggaranPfmSource: AnggaranPfmSource
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    mBRImoPrefRepository,
    apiSource,
    transaksiPfmSource
), IDetailDepositoRevampPresenter<V> where V : IMvpView, V : IDetailDepositoRevampView {

    var url: String? = null

    private var urlInquiryPenutupan: String? = null

    private var urlUpdatePerpanjangan: String? = null

    private var urlConfirmationPenutupanDeposito: String? = null

    override fun setUrlRenewalDeposito(urlDeposito: String?) {
        this.url = urlDeposito
    }

    override fun getDataRenewalDeposito(account: String?) {
        if (isViewAttached) {
            //initiate param with getter from view
            view.showProgress()
            val seqNum = brImoPrefRepository.seqNumber
            val request = RequestRenewalDeposito(account)
            compositeDisposable
                .add(
                    apiSource.getData(url, request, seqNum)
                        .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                        .subscribeOn(schedulerProvider.io())
                        .observeOn(schedulerProvider.mainThread())
                        .subscribeWith(object : ApiObserver(view, seqNum) {
                            override fun onFailureHttp(errorMessage: String) {
                                getView().hideProgress()
                                getView().onException(errorMessage)
                            }

                            override fun onApiCallSuccess(response: RestResponse) {
                                getView().hideProgress()
                                val renewalDepositoResponse = response.getData(
                                    RenewalDepositoResponse::class.java
                                )
                                getView().onRenewalDeposito(renewalDepositoResponse)
                            }

                            override fun onApiCallError(restResponse: RestResponse) {
                                getView().hideProgress()
                                if (restResponse.code.equals(
                                        RestResponse.ResponseCodeEnum.RC_SESSION_END.value,
                                        ignoreCase = true
                                    )
                                ) getView().onSessionEnd(restResponse.desc)
                                else getView().onException(
                                    restResponse.desc
                                )
                            }
                        })
                )
        }
    }

    override fun setUrlInquiryPenutupanDeposito(urlInquiryPenutupanDeposito: String?) {
        urlInquiryPenutupan = urlInquiryPenutupanDeposito
    }

    override fun setUrlUpdatePerpanjangan(url: String?) {
        urlUpdatePerpanjangan = url
    }

    override fun getInquiryPenutupanDeposito(account: String?) {
        if (isViewAttached) {
            //initiate param with getter from view
            view.showProgress()
            val seqNum = brImoPrefRepository.seqNumber
            val request = RequestRenewalDeposito(account)
            compositeDisposable
                .add(
                    apiSource.getData(urlInquiryPenutupan, request, seqNum)
                        .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                        .subscribeOn(schedulerProvider.io())
                        .observeOn(schedulerProvider.mainThread())
                        .subscribeWith(object : ApiObserver(view, seqNum) {
                            override fun onFailureHttp(errorMessage: String) {
                                getView().hideProgress()
                                getView().onException(errorMessage)
                            }

                            override fun onApiCallSuccess(response: RestResponse) {
                                getView().hideProgress()
                                val inquiryPenutupanDepositoResponseRevamp = response.getData(
                                    InquiryPenutupanDepositoResponseRevamp::class.java
                                )
                                getView().getDataInquiryPenutupanDeposito(
                                    inquiryPenutupanDepositoResponseRevamp
                                )
                            }

                            override fun onApiCallError(restResponse: RestResponse) {
                                getView().hideProgress()
                                if (restResponse.code.equals(
                                        RestResponse.ResponseCodeEnum.RC_SESSION_END.value,
                                        ignoreCase = true
                                    )
                                ) getView().onSessionEnd(restResponse.desc) else getView().onException(
                                    restResponse.desc
                                )
                            }
                        })
                )
        }
    }

    override fun sendUpdatePerpanjangan(request: UpdatePerpanjanganDepositoRequest?) {

        if (view == null) {
            return
        }
        view.showProgress()
        val seqNum = brImoPrefRepository.seqNumber
        if (isViewAttached) {
            compositeDisposable.add(
                apiSource.getData(urlUpdatePerpanjangan, request, seqNum)
                    .subscribeOn(schedulerProvider.single())
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(type: String) {
                            getView().hideProgress()
                            getView().onException(type)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            getView().hideProgress()
                            getView().onSuccessUpdateDeposito(response.desc)
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView().hideProgress()
                            if (restResponse.code.equals(
                                    RestResponse.ResponseCodeEnum.RC_SESSION_END.value,
                                    ignoreCase = true
                                )
                            ) getView().onSessionEnd(restResponse.desc) else if (restResponse.code == RestResponse.ResponseCodeEnum.RC_99.value) {
                                getView().onException99(restResponse.desc)
                            } else getView().onException(restResponse.desc)
                        }
                    })
            )
        }
    }

    override fun setUrlConfirmationPenutupanDeposito(url: String) {
        this.urlConfirmationPenutupanDeposito = url
    }

    override fun getConfirmationPenutupanDeposito(refnum: String) {
        if (isViewAttached) {
            view.showProgress()
            //initiate param with getter from view
            val seqNum = brImoPrefRepository.seqNumber
            val request = RefnumRequest(refnum)
            compositeDisposable
                .add(
                    apiSource.getData(urlConfirmationPenutupanDeposito, request, seqNum)
                        .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                        .subscribeOn(schedulerProvider.io())
                        .observeOn(schedulerProvider.mainThread())
                        .subscribeWith(object : ApiObserver(view, seqNum) {
                            override fun onFailureHttp(errorMessage: String) {
                                getView().hideProgress()
                                getView().onException(errorMessage)
                            }

                            override fun onApiCallSuccess(response: RestResponse) {
                                getView().hideProgress()
                                val confirmationResponse = response.getData(
                                    GeneralConfirmationResponse::class.java
                                )
                                //detaildataview tuker dengan amountDataView dam masukin transactiondataview
                                //untuk di general confirmation
                                val bodyUp = confirmationResponse.detailDataView
                                val bodyDown = confirmationResponse.amountDataView
                                confirmationResponse.detailDataView = bodyDown
                                confirmationResponse.transactionDataView = bodyUp

                                getView().getDataConfirmationPenutupanDeposito(
                                    confirmationResponse
                                )
                            }

                            override fun onApiCallError(restResponse: RestResponse) {
                                getView().hideProgress()
                                if (restResponse.code.equals(
                                        RestResponse.ResponseCodeEnum.RC_SESSION_END.value,
                                        ignoreCase = true
                                    )
                                ) getView().onSessionEnd(restResponse.desc) else getView().onException(
                                    restResponse.desc
                                )
                            }
                        })
                )
        }

    }

}