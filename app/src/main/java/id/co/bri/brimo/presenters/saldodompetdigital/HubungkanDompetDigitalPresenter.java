package id.co.bri.brimo.presenters.saldodompetdigital;

import static com.amazonaws.util.json.JsonUtils.JsonEngine.Gson;

import android.util.Log;

import com.google.gson.Gson;

import id.co.bri.brimo.contract.IPresenter.saldodompetdigital.IHubungkanDompetDigitalPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.saldodompetdigital.IHubungkanDompetDigitalView;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.config.AppConfig;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.apimodel.request.EwalletBindingRequest;
import id.co.bri.brimo.models.apimodel.request.EwalletBindingTypeRequest;
import id.co.bri.brimo.models.apimodel.response.EwalletBindingResponse;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.models.apimodel.response.SyaratKetentuanResponse;
import id.co.bri.brimo.presenters.MvpPresenter;

import java.util.concurrent.TimeUnit;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.schedulers.Schedulers;

public class HubungkanDompetDigitalPresenter<V extends IMvpView & IHubungkanDompetDigitalView> extends MvpPresenter<V> implements IHubungkanDompetDigitalPresenter<V> {

    private static final String TAG = "HubungkanDompetDigitalPresenter";
    protected String formUrl;
    protected String fiturUrl;
    protected String tncEwalletUrl;
    protected String bindingUrl;


    boolean isLoading = false;
    protected Object bindingRequest;

    public HubungkanDompetDigitalPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }


    @Override
    public void getEwalletTnc(String type) {
        getView().showProgress();
        if (tncEwalletUrl == null || !isViewAttached()) {
            return;
        }
        String seqNum = getBRImoPrefRepository().getSeqNumber();

        bindingRequest = new EwalletBindingTypeRequest(type);

        getCompositeDisposable().add(
                getApiSource().getData(tncEwalletUrl, bindingRequest, seqNum)//function(param)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(Schedulers.single())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(new ApiObserver(getView(),seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().hideProgress();
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                SyaratKetentuanResponse syaratKetentuanResponse = response.getData(SyaratKetentuanResponse.class);
                                getView().onSuccessGetTnc(syaratKetentuanResponse);

                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                if (restResponse.getCode().equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                    getView().onSessionEnd(restResponse.getDesc());
                                if (restResponse.getCode().equals(RestResponse.ResponseCodeEnum.RC_12.getValue()))
                                    getView().onException12(restResponse.getDesc());
                                else
                                    getView().onException(restResponse.getDesc());
                            }
                        })
        );


    }

    @Override
    public void bindingEwallet(String code, String countryCode) {
        getView().showProgress();
        if (bindingUrl == null || !isViewAttached()) {
            return;
        }
        String seqNum = getBRImoPrefRepository().getSeqNumber();

        bindingRequest = new EwalletBindingRequest(code, countryCode);


        getCompositeDisposable().add(
                getApiSource().getData(bindingUrl, bindingRequest, seqNum)//function(param)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(Schedulers.single())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(new ApiObserver(getView(),seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().hideProgress();
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                try {
                                if (response.getCode().equals(RestResponse.ResponseCodeEnum.RC_SUCCESS.getValue())){
                                    EwalletBindingResponse ewalletBindingResponse = response.getData(EwalletBindingResponse.class);
                                    getView().onSuccessBinding(ewalletBindingResponse);
                                }else{
                                    EwalletBindingResponse ewalletBindingResponse = response.getData(EwalletBindingResponse.class);
                                    getView().onException01(ewalletBindingResponse);
                                }

                                } catch (Exception e) {
                                    Log.d("LOG Hubungkan", "ini error: " + new Gson().toJson(e));
                                }
                                getView().hideProgress();

                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                if (restResponse.getCode().equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                    getView().onSessionEnd(restResponse.getDesc());
                                if (restResponse.getCode().equals(RestResponse.ResponseCodeEnum.RC_12.getValue()))
                                    getView().onException12(restResponse.getDesc());
                                else
                                    getView().onException(restResponse.getDesc());
                            }
                        })
        );


    }

    @Override
    public void setUrlEwalletTnc(String url) {
        this.tncEwalletUrl = url;
    }

    @Override
    public void setUrlEwalletBinding(String url) {
        this.bindingUrl = url;
    }


    @Override
    public void start() {
        super.start();
    }


}