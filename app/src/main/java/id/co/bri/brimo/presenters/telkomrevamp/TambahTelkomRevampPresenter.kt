package id.co.bri.brimo.presenters.telkomrevamp

import id.co.bri.brimo.contract.IPresenter.telkomrevamp.ITambahTelkomRevampPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.telkomrevamp.ITambahTelkomRevampView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.config.AppConfig
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.InquiryTelkomRequest
import id.co.bri.brimo.models.apimodel.response.InquiryBrivaRevampResponse
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.disposables.CompositeDisposable
import java.util.concurrent.TimeUnit

class TambahTelkomRevampPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBrimoPrefRepository: BRImoPrefSource,
    apiSource: ApiSource,
    transaksiPfmSource: TransaksiPfmSource,
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    mBrimoPrefRepository,
    apiSource,
    transaksiPfmSource
), ITambahTelkomRevampPresenter<V> where V : IMvpView, V : ITambahTelkomRevampView {
    private var inquiryUrl: String = ""
    override fun getDataInquiry(telkomNum: String) {
        if (inquiryUrl.isEmpty() || !isViewAttached) return
        view.showProgress()
        val seqNum = brImoPrefRepository.seqNumber
        val inquiryRequest = InquiryTelkomRequest(telkomNum)
        compositeDisposable.add(
            apiSource.getData(inquiryUrl, inquiryRequest, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        view.hideProgress()
                        view.onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        view.hideProgress()
                        val responseData = response.getData(InquiryBrivaRevampResponse::class.java)
                        getView().onSuccessGetInquiry(responseData)
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        view.hideProgress()
                        when (restResponse.code) {
                            RestResponse.ResponseCodeEnum.RC_88_BILL_ALREADY_PAID.value -> getView().onBillingAlreadyPaid(restResponse.desc)
                            else -> getView().onException(restResponse.desc)
                        }
                    }
                })
        )
    }

    override fun setInquiryUrl(url: String) {
        inquiryUrl = url
    }
}