package id.co.bri.brimo.presenters.ssc

import android.util.Log
import id.co.bri.brimo.R
import id.co.bri.brimo.contract.IPresenter.ssc.IComplaintCekBrizziPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.ssc.IComplaintCekBrizziView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimo.data.repository.category.CategoryPfmSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.calendar.CalendarHelper
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.BrizziAktivasiDepositRequest
import id.co.bri.brimo.models.apimodel.request.FastValidateBrizziRequest
import id.co.bri.brimo.models.apimodel.request.InquiryBrizziTopUpRequest
import id.co.bri.brimo.models.apimodel.request.ValidateRequest
import id.co.bri.brimo.models.apimodel.request.ValidateTersimpanRequest
import id.co.bri.brimo.models.apimodel.response.AktivasiBrizziResponse
import id.co.bri.brimo.models.apimodel.response.InquiryBrizziResponse
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.presenters.MvpPresenter
import id.co.bri.brizzi.Brizzi
import id.co.bri.brizzi.CardData
import id.co.bri.brizzi.RCOptions
import id.co.bri.brizzi.callbacks.BrizziCallback
import id.co.bri.brizzi.callbacks.BrizziTrxCallback
import id.co.bri.brizzi.exception.BrizziException
import id.co.bri.brizzi.exception.ExceptionList
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.Schedulers

class ComplaintCekBrizziPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBRImoPrefRepository: BRImoPrefSource,
    apiSource: ApiSource,
    categoryPfmSource: CategoryPfmSource,
    transaksiPfmSource: TransaksiPfmSource,
    anggaranPfmSource: AnggaranPfmSource
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    mBRImoPrefRepository,
    apiSource,
    transaksiPfmSource
), IComplaintCekBrizziPresenter<V> where V : IMvpView, V : IComplaintCekBrizziView {

    private var urlInquiry: String = ""
    private var urlPayment: String = ""
    private var urlValidate: String = ""
    private var descPaymentResp = ""
    private var codePaymentResp = ""

    override fun setInquiryUrl(url: String) {
        urlInquiry = url
    }

    override fun setPaymentUrl(url: String) {
        urlPayment = url
    }

    override fun setValidateUrl(url: String) {
        urlValidate = url
    }

    override fun checkBalancePres(brizzi: Brizzi) {
        view.hideProgress()
        view.showProgress()
        if (isViewAttached) {
            brizzi.initCheckbalance(object : BrizziCallback {
                override fun onSuccess(cardData: CardData) {
                    getRandomHost(
                        InquiryBrizziTopUpRequest(
                            cardData.cardNumber,
                            cardData.randomSAM
                        ), brizzi
                    )
                }

                override fun onFailure(e: BrizziException) {
                    view.hideProgress()
                    view.showErrorMessage(GeneralHelper.getString(R.string.brizzi_kartu_tidak_terbaca))
                }
            })
        }
    }

    override fun updateBalancePres(brizzi: Brizzi) {
        view.hideProgress()
        view.showProgress()
        if (isViewAttached) {
            brizzi.initUpdateBalance(object : BrizziCallback {
                override fun onSuccess(cardData: CardData) {
                    getActivationBrizzi(
                        brizzi, InquiryBrizziTopUpRequest(
                            cardData.cardNumber,
                            cardData.randomSAM
                        )
                    )
                }

                override fun onFailure(e: BrizziException) {
                    view.hideProgress()
                    view.showErrorMessage(GeneralHelper.getString(R.string.brizzi_kartu_tidak_terbaca))
                }
            })
        }
    }

    override fun getActivationBrizzi(brizzi: Brizzi, request: InquiryBrizziTopUpRequest) {
        if (isViewAttached) {
            view.hideProgress()
            view.showProgress()
            val seqNum = brImoPrefRepository.seqNumber
            compositeDisposable.add(
                apiSource.getData(urlInquiry, request, seqNum)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView().hideProgress()
                            getView().onException(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            val aktivasiBrizziResponse = response.getData(
                                InquiryBrizziResponse::class.java
                            )
                            continueUpdateBalancePres(brizzi, aktivasiBrizziResponse)
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView().hideProgress()
                            if (restResponse.code.equals(Constant.RE93))
                                getView().onExceptionTrxExpired(restResponse.desc)
                            else if (restResponse.code.equals(Constant.RE01))
                                getView().onException01(restResponse.desc)
                            else if (restResponse.code.equals(Constant.RE12))
                                getView().onException12(restResponse.desc)
                            else getView().onException(restResponse.desc)
                        }
                    })
            )
        }
    }

    override fun continueUpdateBalancePres(
        brizzi: Brizzi,
        inquiryBrizziResponse: InquiryBrizziResponse
    ) {
        view.hideProgress()
        view.showProgress()
        brizzi.updateBalance(
            inquiryBrizziResponse.key + inquiryBrizziResponse.rcHost,
            object : BrizziCallback {
                override fun onSuccess(cardData: CardData) {
                    getCommitBrizzi(
                        BrizziAktivasiDepositRequest(
                            cardData.cardBalance,
                            cardData.cardNumber,
                            cardData.randomSAM
                        )
                    )
                }

                override fun onFailure(e: BrizziException) {
                    view.hideProgress()
                    view.showErrorMessage(GeneralHelper.getString(R.string.brizzi_kartu_tidak_terbaca))
                }
            })
    }

    override fun getCommitBrizzi(request: BrizziAktivasiDepositRequest) {
        if (isViewAttached) {
            view.hideProgress()
            view.showProgress()

            val seqNum = brImoPrefRepository.seqNumber
            compositeDisposable.add(
                apiSource.getData(urlPayment, request, seqNum)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView().hideProgress()
                            getView().onException(errorMessage)

                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            descPaymentResp = response.desc
                            codePaymentResp = response.code
                            view.hideProgress()

                            val inquiryBrizziResponse = response.getData(AktivasiBrizziResponse::class.java)
                            if (response.code.equals("00") || response.code.equals("01")){
                                getView().onSuccessGetCommitAktivasi(inquiryBrizziResponse)
                            }
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            descPaymentResp = restResponse.desc
                            codePaymentResp = restResponse.code
                            getView().hideProgress()
                            getView().onExceptionGagalDefault(descPaymentResp)
                        }
                    })
            )
        }
    }

    override fun commitContinuePres(
        aktivasiBrizziResponse: AktivasiBrizziResponse,
        brizzi: Brizzi
    ) {
        view.hideProgress()
        view.showProgress()


        try {
            val keyParam = aktivasiBrizziResponse.key ?: aktivasiBrizziResponse.rcHost
            val hostParam = if (aktivasiBrizziResponse.rcHost.equals("Q4", ignoreCase = true) ||
                aktivasiBrizziResponse.rcHost.equals("44", ignoreCase = true) ||
                aktivasiBrizziResponse.rcHost.equals("Q1", ignoreCase = true) ||
                aktivasiBrizziResponse.rcHost.equals("404", ignoreCase = true)) {
                RCOptions.RC_ERROR_TIMEOUT
            } else {
                keyParam + aktivasiBrizziResponse.rcHost
            }

            if (aktivasiBrizziResponse.rcHost in Constant.LIST_RC_NON_VALIDATE_GAGAL) {
                view?.hideProgress()
                if (aktivasiBrizziResponse.rcHost.equals("OV", ignoreCase = true))
                    view?.onExceptionNonValidateBalanceUpdate(descPaymentResp)
                else
                    view?.onExceptionGagalDefault(descPaymentResp)
            }
            else {
                brizzi.commitUpdateBalance(hostParam, object : BrizziTrxCallback {
                    override fun onSuccess(cardData: CardData) {
                        view.onSuccessGetValidateAktivasi()
                        view?.hideProgress()
                    }

                    override fun onValidate(e: BrizziException, cardNumber: String, cardBalance: String, validateRandom: String) {
                        val reffP = if (!aktivasiBrizziResponse.reff.isNullOrBlank()) {
                            aktivasiBrizziResponse.reff
                        } else {
                            aktivasiBrizziResponse.referenceNumber
                        }
                        getValidateBrizzi(
                            ValidateRequest(
                                brizzi.cardData.cardNumber,
                                brizzi.cardData.validateRandom,
                                brizzi.cardData.cardBalance,
                                reffP,
                                aktivasiBrizziResponse.referenceNumber
                            )
                        )
                    }

                    override fun onFailure(e: BrizziException) {
                        view?.hideProgress()
                        when (e.errorCode) {
                            ExceptionList.cardReadFailed -> view?.onExceptionTrxExpired(GeneralHelper.getString(R.string.brizzi_kartu_tidak_terbaca))
                            ExceptionList.generalError -> view?.onExceptionTrxExpired(GeneralHelper.getString(R.string.brizzi_gagal_koneksi))
                            else -> view?.showErrorMessage(GeneralHelper.getString(R.string.brizzi_gagal_koneksi))
                        }
                    }
                })
            }
        } catch (e: Exception){
            view.hideProgress()
            view.showErrorMessage(GeneralHelper.getString(R.string.brizzi_gagal_update_saldo))
        }
    }

    override fun getValidateBrizzi(request: ValidateRequest) {
        if (isViewAttached) {
            view.hideProgress()
            view.showProgress()

            val seqNum = brImoPrefRepository.seqNumber
            compositeDisposable.add(
                apiSource.getData(urlValidate, request, seqNum)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            view?.hideProgress()
                            view?.onException(errorMessage)
                            try {
                                var validateRequestSimpan: ValidateRequest? = null
                                if (request is FastValidateBrizziRequest) {
                                    val requestFast = request as FastValidateBrizziRequest
                                    validateRequestSimpan = ValidateRequest(
                                        requestFast.cardNumber,
                                        requestFast.randomString,
                                        requestFast.balance,
                                        requestFast.reff,
                                        requestFast.referenceNumber
                                    )
                                } else {
                                    validateRequestSimpan = request
                                }

                                validateRequestSimpan?.let {
                                    val validateTersimpanRequest = ValidateTersimpanRequest(it, CalendarHelper.getCurrentDateString())
                                    getBRImoPrefRepository().saveBrizziValidate(validateTersimpanRequest)
                                }
                            } catch (e: Exception) {
                                if (!GeneralHelper.isProd()) {
                                    Log.e(TAG, "onFailureHttp: ", e)
                                }
                            }


                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            view?.hideProgress()
                            if (codePaymentResp.equals(Constant.RE_SUCCESS, ignoreCase = true) && !response.code.equals(Constant.RE_SUCCESS, ignoreCase = true)) {
                                getView().onExceptionGagalDefault(response.desc)
                            } else {
                                if (response.code.equals(Constant.RE01, ignoreCase = true)) {
                                    getView().onExceptionValidate(descPaymentResp, response.desc)
                                }
                            }

                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            view?.hideProgress()
                            if (codePaymentResp.equals("00", ignoreCase = true)) {
                                getView().onExceptionGagalDefault(restResponse.desc)
                            } else {
                                getView().onExceptionGagalDefault(descPaymentResp)
                            }

                        }
                    })
            )
        }
    }

    override fun commitCheckBalancePres(
        brizzi: Brizzi,
        inquiryBrizziResponse: InquiryBrizziResponse
    ) {
        //step 3
        view.hideProgress()
        view.showProgress()
        brizzi.commitCheckBalance(
            inquiryBrizziResponse.key + inquiryBrizziResponse.rcHost,
            object : BrizziCallback {
                override fun onSuccess(cardData: CardData) {
                    view.hideProgress()
                    view.continueInfoSaldo(brizzi, inquiryBrizziResponse)
                }

                override fun onFailure(e: BrizziException) {
                    view.hideProgress()
                    view.showErrorMessage(GeneralHelper.getString(R.string.brizzi_kartu_tidak_terbaca))
                }
            })


    }

    override fun getRandomHost(
        inquiryBrizziRequest: InquiryBrizziTopUpRequest,
        brizzi: Brizzi
    ) {
        view.hideProgress()
        view.showProgress()
        if (isViewAttached) {

            val seqNum = brImoPrefRepository.seqNumber
            compositeDisposable.add(
                apiSource.getData(urlInquiry, inquiryBrizziRequest, seqNum)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView().hideProgress()
                            getView().onException(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            val inquiryBrizziResponse = response.getData(
                                InquiryBrizziResponse::class.java
                            )
                            commitCheckBalancePres(brizzi, inquiryBrizziResponse)
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value))
                                getView().onSessionEnd(restResponse.desc)
                            else if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_01.value))
                                getView().onException01(restResponse.desc)
                            else getView().onException(restResponse.desc)
                            getView().hideProgress()
                        }
                    })
            )
        }
    }

    companion object {
        private const val TAG = "TapAktivasiBrizziPresen"
    }
}