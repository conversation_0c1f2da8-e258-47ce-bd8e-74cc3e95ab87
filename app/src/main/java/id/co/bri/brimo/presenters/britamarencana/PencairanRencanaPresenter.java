package id.co.bri.brimo.presenters.britamarencana;

import java.util.concurrent.TimeUnit;

import id.co.bri.brimo.contract.IPresenter.britamarencana.IPencairanRencanaPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.britamarencana.IPencairanRencanaView;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.config.AppConfig;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.apimodel.request.KonfirmasiPencairanRequest;
import id.co.bri.brimo.models.apimodel.response.GeneralConfirmationResponse;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.presenters.MvpPresenter;
import io.reactivex.disposables.CompositeDisposable;

public class PencairanRencanaPresenter <V extends IMvpView & IPencairanRencanaView> extends MvpPresenter<V>
        implements IPencairanRencanaPresenter<V> {

    String url;

    public PencairanRencanaPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void setUrl(String url) {
        this.url = url;
    }

    @Override
    public void getInquiryPencairan(KonfirmasiPencairanRequest konfirmasiPencairanRequest) {
        getView().showProgress();
        if (url == null || !isViewAttached()) {
            return;
        }

        String seqNum = getBRImoPrefRepository().getSeqNumber();

        getCompositeDisposable().add(
                getApiSource().getData(url, konfirmasiPencairanRequest,seqNum)//function(param)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(getSchedulerProvider().single())
                        .observeOn(getSchedulerProvider().mainThread())
                        .subscribeWith(new ApiObserver(getView(),seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().hideProgress();
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                getView().hideProgress();
                                GeneralConfirmationResponse generalConfirmationResponse = response.getData(GeneralConfirmationResponse.class);
                                getView().onSuccessGetInquiryPencairan(generalConfirmationResponse);
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                if (restResponse.getCode().equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                    getView().onSessionEnd(restResponse.getDesc());
                                else
                                    getView().onException(restResponse.getDesc());
                            }
                        })
        );
    }
}