package id.co.bri.brimo.presenters.ubahdetailrencana

import id.co.bri.brimo.contract.IPresenter.ubahrencana.IChangeDetailPlanPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.ubahdetailrencana.IChangeDetailPlanView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimo.data.repository.category.CategoryPfmSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.config.AppConfig

import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.dashboardrencanarevamp.InquiryPencairanRencanaRequest
import id.co.bri.brimo.models.apimodel.request.ubahdetailrencana.ChangeDetailPlanRequest
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.dashboardrecana.InquiryPencairanResponse
import id.co.bri.brimo.models.apimodel.response.topuprencana.TopupPlanDataResponse
import id.co.bri.brimo.models.apimodel.response.ubahdetailrencana.ChangeDetailPlanDataResponse
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.Schedulers
import java.util.concurrent.TimeUnit

class ChangeDetailPlanPresenter <V>(
    schedulerProvider: SchedulerProvider?,
    compositeDisposable: CompositeDisposable?,
    mBRImoPrefRepository: BRImoPrefSource?,
    apiSource: ApiSource?,
    categoryPfmSource: CategoryPfmSource?,
    transaksiPfmSource: TransaksiPfmSource?,
    anggaranPfmSource: AnggaranPfmSource?
) : MvpPresenter<V>(
    schedulerProvider, compositeDisposable,
    mBRImoPrefRepository, apiSource,
    transaksiPfmSource
), IChangeDetailPlanPresenter<V> where V: IMvpView, V: IChangeDetailPlanView {

    @JvmField
    var urlChangeDetailPlan = ""
    private var url = ""
    private var urlPencairanRencana = ""

    override fun setUrlChangeDetailPlan(url: String) {
        this.urlChangeDetailPlan = url
    }

    override fun setUrlTopUpPlan(url: String) {
        this.url = url
    }

    override fun setUrlPencairanRencana(url: String) {
        this.urlPencairanRencana = url
    }

    override fun postChangeDetailPlanRequest(changeDetailPlanRequest: ChangeDetailPlanRequest) {
        if (urlChangeDetailPlan.isEmpty() || !isViewAttached) return

        view?.showProgress()
        val seqNum = brImoPrefRepository.seqNumber
        val disposable = apiSource.getData(
            urlChangeDetailPlan,
            changeDetailPlanRequest,
            seqNum,
        ).subscribeOn(schedulerProvider.io())
            .observeOn(schedulerProvider.mainThread())
            .subscribeWith(object : ApiObserver(view, seqNum) {
                override fun onFailureHttp(message: String) {
                    getView().apply {
                        hideProgress()
                        onException(message)
                    }
                }

                override fun onApiCallSuccess(response: RestResponse?) {
                    getView().apply {
                        hideProgress()
                        val responseData = response?.getData(
                            ChangeDetailPlanDataResponse::class.java
                        )
                        responseData?.let { getView().onSuccessResponse(it) }
                    }
                }

                override fun onApiCallError(restResponse: RestResponse?) {
                    restResponse?.let { response ->
                        getView().apply {
                            hideProgress()
                            when (response.code) {
                                RestResponse.ResponseCodeEnum.RC_SESSION_END.value -> this.onSessionEnd(response.desc.orEmpty())
                                RestResponse.ResponseCodeEnum.RC_12.value -> this.onExceptionCode12(response.restResponse.desc)
                                else -> this.onException(response.desc.orEmpty())
                            }
                        }
                    }
                }
            })
        compositeDisposable.add(disposable)
    }

    override fun postTopUpPlanRequest(changeDetailPlanRequest: ChangeDetailPlanRequest) {
        if (url.isEmpty() || !isViewAttached) return

        view?.showProgress()
        val seqNum = brImoPrefRepository.seqNumber
        val disposable = apiSource.getData(
            url,
            changeDetailPlanRequest,
            seqNum,
        ).subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribeWith(object : ApiObserver(view, seqNum) {
                override fun onFailureHttp(message: String) {
                    getView().apply {
                        hideProgress()
                        onException(message)
                    }
                }

                override fun onApiCallSuccess(response: RestResponse?) {
                    getView().apply {
                        hideProgress()
                        val responseData = response?.getData(
                            TopupPlanDataResponse::class.java
                        )
                        responseData?.let { getView().onSuccessResponse(it) }
                    }
                }

                override fun onApiCallError(restResponse: RestResponse?) {
                    restResponse?.let { response ->
                        getView().apply {
                            hideProgress()
                            when (response.code) {
                                RestResponse.ResponseCodeEnum.RC_SESSION_END.value -> this.onSessionEnd(response.desc.orEmpty())
                                RestResponse.ResponseCodeEnum.RC_12.value -> this.onExceptionCode12(response.restResponse.desc)
                                else -> this.onException(response.desc.orEmpty())
                            }
                        }
                    }
                }
            })
        compositeDisposable.add(disposable)
    }

    override fun getInquiryPencairanRencana(accountNumberRequest: InquiryPencairanRencanaRequest) {
        if (urlPencairanRencana.isEmpty() || !isViewAttached) return

        view?.showProgress()
        val seqNum = brImoPrefRepository.seqNumber
        compositeDisposable
            .add(
                apiSource.getData(urlPencairanRencana, accountNumberRequest, seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(type: String?) {
                            view?.hideProgress()
                            getView()!!.onException(type)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            view?.hideProgress()
                            getView()!!.onSuccessInquiry(response.getData(InquiryPencairanResponse::class.java))
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            view?.hideProgress()
                            if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value, ignoreCase = true)) {
                                getView()!!.onSessionEnd(restResponse.desc)
                            } else {
                                getView()!!.onException(restResponse.desc)
                            }
                        }

                    })
            )
    }
}