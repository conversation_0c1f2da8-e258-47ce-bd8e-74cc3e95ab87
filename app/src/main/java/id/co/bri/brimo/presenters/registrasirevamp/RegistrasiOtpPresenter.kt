package id.co.bri.brimo.presenters.registrasirevamp

import id.co.bri.brimo.contract.IPresenter.registrasirevamp.IRegistrasiOtpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.registrasirevamp.IRegistrasiOtpView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimo.data.repository.category.CategoryPfmSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.config.AppConfig
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.RegisResendReq
import id.co.bri.brimo.models.apimodel.request.RegisSendOtpReq
import id.co.bri.brimo.models.apimodel.response.registrasi.RegisOtpResponse
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.registrasi.RegisEmailResponse
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.Schedulers
import java.util.concurrent.TimeUnit

class RegistrasiOtpPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBRImoPrefRepository: BRImoPrefSource,
    apiSource: ApiSource,
    categoryPfmSource: CategoryPfmSource,
    transaksiPfmSource: TransaksiPfmSource,
    anggaranPfmSource: AnggaranPfmSource
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    mBRImoPrefRepository,
    apiSource,
    transaksiPfmSource
), IRegistrasiOtpPresenter<V> where V : IMvpView, V : IRegistrasiOtpView? {

    private lateinit var urlResend: String
    private lateinit var urlSend: String

    override fun setUrlResend(url: String) {
        urlResend = url
    }

    override fun setUrlSend(url: String) {
        urlSend = url
    }

    override fun sendResendOtp(regisResendReq: RegisResendReq) {
        if (isViewAttached) {
            val seqNum = brImoPrefRepository.seqNumber
            view.showProgress()

            compositeDisposable.add(
                apiSource.getData(urlResend, regisResendReq, seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView().hideProgress()
                            getView().onExceptionNoBackAction(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            getView().hideProgress()
                            val regisOtpResponse =
                                response.getData(RegisOtpResponse::class.java)
                            getView().onSuccessGetResend(regisOtpResponse)
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView().deletePin()
                            getView().hideProgress()
                            if(restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_STATUS_NOT_MATCH.value))
                                getView().onExceptionStatusNotMatch()
                            else getView().onExceptionRevamp(restResponse.desc)
                        }
                    })
            )
        }
    }

    override fun sendSendOtp(regisSendOtpReq: RegisSendOtpReq) {
        if (isViewAttached) {
            val seqNum = brImoPrefRepository.seqNumber
            view.showProgress()

            compositeDisposable.add(
                apiSource.getData(urlSend, regisSendOtpReq, seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView().hideProgress()
                            getView().onException(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            getView()?.hideProgress()
                            val regisEmailResponse =
                                response.getData(RegisEmailResponse::class.java)
                            getView().onSuccessSend(regisEmailResponse)
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView().deletePin()
                            getView().hideProgress()
                            if(restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_STATUS_NOT_MATCH.value))
                                getView().onExceptionStatusNotMatch()
                            else getView().onExceptionRevamp(restResponse.desc)
                        }
                    })
            )
        }
    }

}