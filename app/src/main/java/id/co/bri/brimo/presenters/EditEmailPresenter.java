package id.co.bri.brimo.presenters;

import java.util.concurrent.TimeUnit;

import id.co.bri.brimo.contract.IPresenter.IEditEmailPresenter;
import id.co.bri.brimo.contract.IView.IEditEmailView;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.config.AppConfig;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.apimodel.request.EmailRequest;
import id.co.bri.brimo.models.apimodel.response.GeneralOtpResponse;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.schedulers.Schedulers;

public class EditEmailPresenter<V extends IMvpView & IEditEmailView> extends MvpPresenter<V> implements IEditEmailPresenter<V> {

    private String url;

    public EditEmailPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void setUrl(String url) {
        this.url = url;
    }

    @Override
    public void sendDataEmail() {
        if (isViewAttached()) {

            getView().showProgress();

            EmailRequest emailRequest = new EmailRequest(getView().getEmail());
            String seqNum = getBRImoPrefRepository().getSeqNumber();

            getCompositeDisposable().add(
                    getApiSource().getData(url, emailRequest, seqNum)
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .subscribeOn(Schedulers.io())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribeWith(new ApiObserver(getView(),seqNum) {

                                @Override
                                protected void onFailureHttp(String errorMessage) {
                                    getView().hideProgress();
                                    getView().onException(errorMessage);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    getView().hideProgress();
                                    GeneralOtpResponse otpResponse = response.getData(GeneralOtpResponse.class);
                                    getView().onSuccessGetData(otpResponse);
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().hideProgress();
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                        getView().onSessionEnd(restResponse.getDesc());
                                    else
                                        getView().onException(restResponse.getDesc());
                                }
                            })
            );
        }
    }
}