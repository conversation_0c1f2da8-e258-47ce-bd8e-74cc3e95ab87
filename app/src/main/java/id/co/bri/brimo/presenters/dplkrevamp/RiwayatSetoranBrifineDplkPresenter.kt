package id.co.bri.brimo.presenters.dplkrevamp

import id.co.bri.brimo.contract.IPresenter.dplkrevamp.IRiwayatSetoranBrifineDplkPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.dplkrevamp.IRiwayatSetoranBrifineDplkView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimo.data.repository.category.CategoryPfmSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.config.AppConfig

import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.dplkrevamp.FilterRiwayatSetoranDplkRequest
import id.co.bri.brimo.models.apimodel.request.dplkrevamp.RiwayatMutasiDplkRequest
import id.co.bri.brimo.models.apimodel.request.dplkrevamp.RiwayatSetoranDplkRequest
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.dplkrevamp.RiwayatMutasiDPLKResponse
import id.co.bri.brimo.models.apimodel.response.dplkrevamp.RiwayatSetoranResponse
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.disposables.CompositeDisposable
import java.util.concurrent.TimeUnit

class RiwayatSetoranBrifineDplkPresenter <V>(schedulerProvider: SchedulerProvider?,
                                             compositeDisposable: CompositeDisposable?,
                                             mBRImoPrefRepository: BRImoPrefSource?, apiSource: ApiSource?,
                                             categoryPfmSource: CategoryPfmSource?,
                                             transaksiPfmSource: TransaksiPfmSource?,
                                             anggaranPfmSource: AnggaranPfmSource?) : MvpPresenter<V>(
    schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource),
    IRiwayatSetoranBrifineDplkPresenter<V> where V : IMvpView?, V : IRiwayatSetoranBrifineDplkView?  {

    private var mUrlRiwayatBrifine: String = ""
    private var mUrlFilterRiwayatBrifine: String = ""
    private var mUrlRiwayatMutasiBrifine: String = ""

    override fun setUrlRiwayatSetoranBrifine(urlRiwayatSetoran: String) {
        mUrlRiwayatBrifine = urlRiwayatSetoran
    }

    override fun setUrlRiwayatSetoranBrifineFilter(urlFilterRiwayatSeotran: String) {
        mUrlFilterRiwayatBrifine = urlFilterRiwayatSeotran
    }

    override fun setUrlRiwayatMutasiBrifine(urlRiwayatMutasiBrifine: String) {
        mUrlRiwayatMutasiBrifine = urlRiwayatMutasiBrifine
    }

    override fun getRiwayatSetoranBrifine(request: RiwayatSetoranDplkRequest) {
        if (!isViewAttached) {
            return
        }

        view?.showProgress()
        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable
            .add(
                apiSource.getData(mUrlRiwayatBrifine, request, seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(schedulerProvider.io())
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView()?.hideProgress()
                            getView()?.onException(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            //TO-DO onSuccess
                            getView()?.hideProgress()
                            val data = response.getData(
                                RiwayatSetoranResponse::class.java
                            )
                            if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SUCCESS.value, ignoreCase = true)) {
                                getView()!!.onSuccessRiwayatSetoran(data)
                            }

                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView()!!.hideProgress()
                            when(restResponse.code){
                                RestResponse.ResponseCodeEnum.RC_SESSION_END.value -> getView()!!.onSessionEnd(restResponse.desc)
                                else -> getView()!!.onException(restResponse.desc)
                            }
                        }
                    })
            )
    }

    override fun getRiwayatSetoranBrifineFilter(request: FilterRiwayatSetoranDplkRequest) {
        if (!isViewAttached) {
            return
        }

        val seqNum = brImoPrefRepository.seqNumber
        compositeDisposable
            .add(
                apiSource.getData(mUrlFilterRiwayatBrifine, request, seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(schedulerProvider.io())
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView()?.onException(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            //TO-DO onSuccess
                            val data = response.getData(
                                RiwayatSetoranResponse::class.java
                            )
                            if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SUCCESS.value, ignoreCase = true)) {
                                getView()!!.onSuccessFilterRiwayatSetoran(data)
                            }
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView()?.onException(restResponse.desc)
                        }
                    })
            )
    }

    override fun getRiwayatMutasiBrifine(request: RiwayatMutasiDplkRequest) {
        if (!isViewAttached) {
            return
        }

        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable
            .add(
                apiSource.getData(mUrlRiwayatMutasiBrifine, request, seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(schedulerProvider.io())
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView()?.onException(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            //TO-DO onSuccess
                            val data = response.getData(
                                RiwayatMutasiDPLKResponse::class.java
                            )
                            if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SUCCESS.value, ignoreCase = true)) {
                                getView()?.onSuccessRiwayatMutasi(data)
                            }
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView()?.onException(restResponse.desc)
                        }
                    })
            )

    }
}