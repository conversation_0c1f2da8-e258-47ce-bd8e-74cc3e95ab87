package id.co.bri.brimo.presenters.voucher

import android.util.Log
import id.co.bri.brimo.contract.IPresenter.voucher.IInquiryKonfirmasiVoucherPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.voucher.IInquiryKonfirmasiVoucherView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.api.observer.ApiObserverKonfirmasi
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimo.data.repository.category.CategoryPfmSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.config.DbConfig

import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.calendar.CalendarHelper
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.cashback.CashbackRevFilterRequest
import id.co.bri.brimo.models.apimodel.request.cashback.PilihCashbackRequest
import id.co.bri.brimo.models.apimodel.request.voucher.PaymentVoucherRequest
import id.co.bri.brimo.models.apimodel.response.GeneralConfirmationResponse
import id.co.bri.brimo.models.apimodel.response.GeneralResponse
import id.co.bri.brimo.models.apimodel.response.ListAllCashbackFilterResponse
import id.co.bri.brimo.models.apimodel.response.ReceiptRevampResponse
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.SelectCashbackResponse
import id.co.bri.brimo.models.daomodel.Transaksi
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.disposables.Disposable
import io.reactivex.observers.DisposableSingleObserver
import io.reactivex.schedulers.Schedulers

class InquiryKonfirmasiVoucherPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBRImoPrefRepository: BRImoPrefSource,
    apiSource: ApiSource,
    categoryPfmSource: CategoryPfmSource,
    transaksiPfmSource: TransaksiPfmSource,
    anggaranPfmSource: AnggaranPfmSource
) : MvpPresenter<V>(
    schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource
),
    IInquiryKonfirmasiVoucherPresenter<V> where V : IMvpView, V : IInquiryKonfirmasiVoucherView {

    private lateinit var mUrlPayment: String
    private lateinit var paymentRequest: Any
    private lateinit var urlGetListCashback: String
    private lateinit var selectCashbackUrl: String
    private lateinit var redeemCashbackRequest: Any
    private lateinit var cashbackRequest: Any

    override fun setUrlPayment(url: String) {
        mUrlPayment = url
    }

    override fun getDataPaymentRevamp(
        mInquiryKonfirmasiVoucherResponse: GeneralConfirmationResponse,
        account: String, pin: String, pfmCategory: String
    ) {

        if (mUrlPayment.isEmpty() || !isViewAttached)
            return

        view.showProgress()
        val seqNum = brImoPrefRepository.seqNumber

        paymentRequest = PaymentVoucherRequest(
            mInquiryKonfirmasiVoucherResponse.referenceNumber,
            account, pin, mInquiryKonfirmasiVoucherResponse.pfmCategory.toString()
        )

        val disposable: Disposable =
            apiSource.getData(mUrlPayment, paymentRequest, seqNum) //function(param)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(object : ApiObserverKonfirmasi(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String?) {
                        getView().hideProgress()
                        getView().onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView().hideProgress()

                        val receiptRevampResponse =
                            response.getData(ReceiptRevampResponse::class.java)

                        if (receiptRevampResponse.immediatelyFlag) onSaveTransaksiPfm(
                            generateTransaksiModel(
                                mInquiryKonfirmasiVoucherResponse.pfmCategory,
                                mInquiryKonfirmasiVoucherResponse.payAmount,
                                mInquiryKonfirmasiVoucherResponse.referenceNumber,
                                mInquiryKonfirmasiVoucherResponse.pfmDescription
                            )
                        )
                        getView().onSuccessGetPaymentRevamp(receiptRevampResponse)
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView().hideProgress()
                        when (restResponse.code.toLowerCase()) {
                            RestResponse.ResponseCodeEnum.RC_12.value -> getView().onException12(restResponse.desc)
                            RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.value -> getView().onException93(restResponse.desc)
                            RestResponse.ResponseCodeEnum.RC_01.value -> getView().onException01(restResponse.desc)
                            RestResponse.ResponseCodeEnum.RC_LIMIT_EXCEEDED.value -> getView().onExceptionLimitExceed(restResponse.getData(GeneralResponse::class.java))
                            else -> getView().onException(restResponse.desc)
                        }
                    }
                })

        compositeDisposable.add(disposable)
    }

    fun onSaveTransaksiPfm(transaksi: Transaksi?) {
        if (transaksi != null) {
            compositeDisposable.add(
                transaksiPfmSource
                    .saveTransaksiPfm(transaksi)
                    .subscribeOn(schedulerProvider.io())
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : DisposableSingleObserver<Long?>() {
                        override fun onSuccess(aLong: Long) {}
                        override fun onError(e: Throwable) {
                            if (!GeneralHelper.isProd())
                                Log.d("saveTransaksiPfm", "onError: $e")
                        }
                    })
            )
        }
    }

    fun generateTransaksiModel(
        kategoriId: Int,
        amount: Long,
        referenceNumber: String?,
        billingName: String?
    ): Transaksi? {
        var transaksi: Transaksi? = null
        try {
            transaksi = Transaksi(
                kategoriId.toLong(),
                1,
                billingName,
                "",
                DbConfig.TRX_OUT,
                brImoPrefRepository.user,
                amount,
                CalendarHelper.getCurrentDate(),
                CalendarHelper.getCurrentTime(),
                java.lang.Long.valueOf(referenceNumber.toString()),
                0
            )
        } catch (e: Exception) {
            /*
            if(BuildConfig.DEBUG)
            e.printStackTrace();
             */
        }
        return transaksi
    }

    override fun start() {
        super.start()
        getDefaultSaldo()
    }

    override fun getAccountDefault(): String {
        return brImoPrefRepository.accountDefault
    }

    override fun getSaldoRekeningUtama(): String {
        return brImoPrefRepository.saldoRekeningUtama
    }

    override fun setUrlGetCashback(url: String) {
        this.urlGetListCashback = url
    }

    override fun getCashbackAll(
        accountNumber: String,
        referenceNumber: String
    ) {
        if (urlGetListCashback == null || !isViewAttached) {
            return
        }

        cashbackRequest = CashbackRevFilterRequest(
            accountNumber = accountNumber,
            referenceNumber = referenceNumber
        )

        view.showProgress()
        val seqNum = brImoPrefRepository.seqNumber
        compositeDisposable.add(
            apiSource.getData(urlGetListCashback, cashbackRequest, seqNum)
                .subscribeOn(schedulerProvider.single())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView().onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView().hideProgress()
                        if (response.code.equals(RestResponse.ResponseCodeEnum.RC_SUCCESS.value, ignoreCase = true)) {
                            val responseData = response.getData(
                                ListAllCashbackFilterResponse::class.java
                            )
                            getView().onSuccessGetCashback(responseData)
                        } else if (response.code.equals(RestResponse.ResponseCodeEnum.RC_01.value, ignoreCase = true)) {
                            getView().onCashbackBlank(response.desc)
                        }
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView().hideProgress()
                        onApiError(restResponse)
                    }
                })
        )
    }

    override fun setRedeemCashbackUrl(url: String) {
        this.selectCashbackUrl = url
    }

    override fun getRedeemCashback(refNum: String, code: String) {
        if (selectCashbackUrl == null || !isViewAttached) {
            return
        }

        redeemCashbackRequest = PilihCashbackRequest(refNum, code)

        view.showProgress()
        val seqNum = brImoPrefRepository.seqNumber
        compositeDisposable.add(
            apiSource.getData(selectCashbackUrl, redeemCashbackRequest, seqNum)
                .subscribeOn(schedulerProvider.single())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView().onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView().hideProgress()
                        val selectCashbackResponse = response.getData(
                            SelectCashbackResponse::class.java
                        )
                        getView().onSuccessClearSelectedCashback(selectCashbackResponse)
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView().hideProgress()
                        when {
                            restResponse.code.equals(
                                RestResponse.ResponseCodeEnum.RC_12.value,
                                ignoreCase = true
                            ) -> getView().onException12(restResponse.desc)

                            else -> getView().onException(restResponse.desc)
                        }
                    }
                })
        )
    }

    private fun getDefaultSaldo() {
        var saldo = 0.0
        val saldoText = brImoPrefRepository.saldoRekeningUtamaString
        if (saldoText != "") {
            val cleanString = saldoText.replace("[,.]".toRegex(), "")
            try {
                saldo = cleanString.toDouble() / 100
            } catch (e: Exception) {
                // do nothing
            }
        }
        val saldoHold = brImoPrefRepository.saldoHold

        val defaultAcc = brImoPrefRepository.accountDefault

        val saldoString = brImoPrefRepository.saldoRekeningUtamaString
        view.setDefaultSaldo(saldo, saldoString, defaultAcc, saldoHold)
    }

}