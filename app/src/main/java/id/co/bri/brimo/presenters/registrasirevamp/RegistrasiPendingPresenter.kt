package id.co.bri.brimo.presenters.registrasirevamp

import android.util.Log
import com.google.gson.Gson
import id.co.bri.brimo.contract.IPresenter.registrasirevamp.IRegistrasiPendingPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.registrasirevamp.IRegistrasiPendingView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimo.data.repository.category.CategoryPfmSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.config.AppConfig
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.RegisIdRequest
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.registrasi.RegisProgressResponse
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.Schedulers
import java.util.concurrent.TimeUnit

class RegistrasiPendingPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBRImoPrefRepository: BRImoPrefSource,
    apiSource: ApiSource,
    categoryPfmSource: CategoryPfmSource,
    transaksiPfmSource: TransaksiPfmSource,
    anggaranPfmSource: AnggaranPfmSource
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    mBRImoPrefRepository,
    apiSource,
    transaksiPfmSource
), IRegistrasiPendingPresenter<V> where V : IMvpView, V : IRegistrasiPendingView? {

    private lateinit var urlProgress: String

    override fun setUrlProgress(url: String) {
        urlProgress = url
    }

    override fun getProgressRegis() {
        if (urlProgress.isNotEmpty() && isViewAttached) {
            view.showProgress()
            val seqNum = brImoPrefRepository.seqNumber

            val regisIdRequest = RegisIdRequest(brImoPrefRepository.deviceId)

            compositeDisposable.add(
                apiSource.getData(urlProgress, regisIdRequest, seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView().hideProgress()
                            getView().onExceptionNoBackAction(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            getView().hideProgress()
                            val regisProgressResponse =
                                response.getData(RegisProgressResponse::class.java)

                            getView().progress30s()

                            when (regisProgressResponse.status) {
                                6 -> {
                                    getView().onSuccess06(
                                        Gson().toJson(response.data),
                                        regisProgressResponse
                                    )
                                }

                                41 -> {
                                    getView().onSuccess41(
                                        Gson().toJson(response.data),
                                        regisProgressResponse
                                    )
                                }

                                42 -> {
                                    getView().onSuccess42(
                                        Gson().toJson(response.data),
                                        regisProgressResponse
                                    )
                                }

                                43 -> {
                                    getView().onSuccess43(
                                        Gson().toJson(response.data),
                                        regisProgressResponse
                                    )
                                }
                            }
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView().hideProgress()
                            if (!restResponse.desc.contains("SIR1"))
                                getView().onExceptionRevamp(restResponse.desc)
                        }
                    })
            )
        }
    }
}