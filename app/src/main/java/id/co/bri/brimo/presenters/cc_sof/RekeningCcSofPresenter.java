package id.co.bri.brimo.presenters.cc_sof;


import android.util.Log;

import androidx.annotation.NonNull;

import com.google.gson.Gson;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

import id.co.bri.brimo.R;
import id.co.bri.brimo.contract.IPresenter.cc_sof.IRekeningCcSofPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.cc_sof.IRekeningCcSofView;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.config.AppConfig;
import id.co.bri.brimo.domain.converter.MapperHelper;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.apimodel.request.DetailCcSofRequest;
import id.co.bri.brimo.models.apimodel.response.cc.DetailCcSofResponse;
import id.co.bri.brimo.models.apimodel.response.cc.LimitCcSofResponse;
import id.co.bri.brimo.models.apimodel.response.ListCcSofResponse;
import id.co.bri.brimo.models.apimodel.response.ListRekeningResponse;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.models.apimodel.response.SnkResponse;
import id.co.bri.brimo.presenters.MvpPresenter;
import io.reactivex.Observable;
import io.reactivex.ObservableSource;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.functions.Function;
import io.reactivex.observers.DisposableObserver;
import io.reactivex.schedulers.Schedulers;

public class RekeningCcSofPresenter<V extends IMvpView & IRekeningCcSofView> extends
        MvpPresenter<V> implements IRekeningCcSofPresenter<V> {

    List<ListRekeningResponse.Account> accountList;
    List<ListCcSofResponse.Account> ccSofAccount = new ArrayList<>();

    private String urlListCcSof;
    private String urlDetailCcSof;
    private String urlCheckLimitCcSof;
    private String urlTerm;
    private boolean isLoading = false;
    private int count = 0;

    public RekeningCcSofPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable,
                                  BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource,
                                  TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void getAccountWithSaldo() {
        if (isLoading)
            return;

        if (isViewAttached()) {
            isLoading = true;
            accountList = new ArrayList<>();
            String seqNum = getBRImoPrefRepository().getSeqNumber();
            getCompositeDisposable().add(getApiSource().getDataForm(urlListCcSof, seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                    .subscribeOn(Schedulers.single())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(new ApiObserver(getView(), seqNum) {

                        @Override
                        protected void onFailureHttp(String errorMessage) {
                            getView().onException(errorMessage);
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SUCCESS.getValue())) {
                                ListCcSofResponse listCcSofResponse = restResponse.getData(ListCcSofResponse.class);
                                if (ccSofAccount.isEmpty()) {
                                    ccSofAccount.addAll(listCcSofResponse.getAccount());
                                } else {
                                    ccSofAccount.clear();
                                    ccSofAccount.addAll(listCcSofResponse.getAccount());
                                }
                                getView().onListCcSof(listCcSofResponse);
                            } else if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_01.getValue())) {
                                getView().onSuccess01(response.getDesc());
                            }
                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue())) {
                                ++count;
                                checkSession(restResponse.getDesc());
                            } else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_12.getValue())) {
                                getView().onException12(restResponse.getDesc());
                            } else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_99.getValue()))
                                getView().onException99(restResponse.getDesc());
                        }

                        @Override
                        public void onComplete() {
                            getLimit(ccSofAccount, false);
                            getView().enableButton(false);
                        }
                    }));


        }
    }

    private void checkSession(String msg) {
        if (count == 1) {
            getView().onSessionEnd(msg);
        } else {
            stop();
        }
    }

    @Override
    public void getLimit(List<ListCcSofResponse.Account> list, boolean isRefreshed) {
        getView().showProgress();
        getCompositeDisposable().add(
                Observable.fromIterable(list)
                        .flatMap((Function<ListCcSofResponse.Account, ObservableSource<ListCcSofResponse.Account>>) this::getLimitObservable)
                        .subscribeWith(new DisposableObserver<ListCcSofResponse.Account>() {

                                           @Override
                                           public void onNext(@NonNull ListCcSofResponse.Account account) {
                                               int postion = list.indexOf(account);
                                               if (postion == -1)
                                                   return;
                                               list.set(postion, account);
                                               getView().hideProgress();
                                               getView().onGetLimit(list, isRefreshed);
                                               getView().enableButton(false);
                                           }

                                           @Override
                                           public void onError(Throwable e) {
                                               getView().hideProgress();
                                               getView().enableButton(true);
                                           }

                                           @Override
                                           public void onComplete() {
                                               getView().onGetLimitComplete();
                                               isLoading = false;
                                               getView().enableButton(true);
                                           }
                                       }
                        ));
    }

    @Override
    public void getLimitCcSof() {
        if (isViewAttached()) {
            getView().showProgress();

            String seqNum = getBRImoPrefRepository().getSeqNumber();

            getCompositeDisposable()
                    .add(getApiSource().getData(urlCheckLimitCcSof, "", seqNum)
                            .subscribeOn(Schedulers.single())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribeWith(new ApiObserver(getView(), seqNum) {

                                @Override
                                protected void onFailureHttp(String errorMessage) {
                                    getView().hideProgress();
                                    getView().onException(errorMessage);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    getView().hideProgress();
                                    LimitCcSofResponse limitCcSofResponse = response.getData(LimitCcSofResponse.class);
                                    getView().onLimitCheckComplete(limitCcSofResponse);
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().hideProgress();
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue())) {
                                        ++count;
                                        checkSession(restResponse.getDesc());
                                    }
                                }
                            }));
        }
    }

    @Override
    public Observable<ListCcSofResponse.Account> getLimitObservable(ListCcSofResponse.Account account) {
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        DetailCcSofRequest detailCcSofRequest = new DetailCcSofRequest(account.getCardNumberToken());

        return getApiSource().getData(urlDetailCcSof, detailCcSofRequest, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                .subscribeOn(getSchedulerProvider().newThread())
                .observeOn(getSchedulerProvider().mainThread())
                .map(stringResponse -> {
                    DetailCcSofResponse detailCcSofResponse;
                    RestResponse restResponse;
                    detailCcSofResponse = new DetailCcSofResponse();
                    String responseCheck = MapperHelper.getIdResponse(stringResponse);

                    if (responseCheck.isEmpty()) {
                        detailCcSofResponse.setBalanceString("-");
                    }
                    restResponse = MapperHelper.stringToRestResponse(stringResponse, seqNum);

                    if (restResponse != null) {
                        if (GeneralHelper.isContains(R.array.response_code_success, restResponse.getCode())) {
                            detailCcSofResponse = restResponse.getData(DetailCcSofResponse.class);
                            if (restResponse.getDesc().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_01.getValue())) {
                                detailCcSofResponse.setBalanceString(restResponse.getDesc());
                            }
                        } else if (restResponse.getCode().equals(RestResponse.ResponseCodeEnum.RC_12.getValue())) {
                            detailCcSofResponse.setBalanceString(restResponse.getCode());
                            detailCcSofResponse.setName(restResponse.getDesc());
                            getView().onExceptionTotalSaldo();

                        } else if (restResponse.getCode().equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue())) {
                            detailCcSofResponse.setBalanceString(restResponse.getCode());
                            ++count;
                            checkSession(restResponse.getDesc());
                        } else {
                            detailCcSofResponse.setBalanceString("-");
                        }

                    } else {
                        detailCcSofResponse.setBalanceString("-");
                    }
                    account.setDetailCcSofResponse(detailCcSofResponse);
                    return account;
                });
    }

    @Override
    public void getTerm() {
        if (!urlTerm.isEmpty() && isViewAttached()) {
            getView().showProgress();
            getView().showProgressTerm();

            String seqNum = getBRImoPrefRepository().getSeqNumber();

            getCompositeDisposable()
                    .add(getApiSource().getDataTanpaRequest(urlTerm, seqNum)
                            .subscribeOn(Schedulers.io())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribeWith(new ApiObserver(getView(), seqNum) {
                                @Override
                                protected void onFailureHttp(String errorMessage) {
                                    getView().hideProgress();
                                    getView().hideProgressTerm();
                                    getView().onException(errorMessage);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    getView().hideProgress();
                                    getView().hideProgressTerm();
                                    SnkResponse snkResponse = response.getData(SnkResponse.class);
                                    getView().onSuccessTerm(snkResponse);
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().hideProgress();
                                    getView().hideProgressTerm();
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue())) {
                                        ++count;
                                        checkSession(restResponse.getDesc());
                                    } else {
                                        getView().onException(restResponse.getDesc());
                                    }
                                }
                            }));
        }
    }

    @Override
    public void setUrlListCcSof(String url) {
        urlListCcSof = url;
    }

    @Override
    public void setUrlDetailCcSof(String url) {
        urlDetailCcSof = url;
    }

    @Override
    public void setUrlCheckLimitCcSof(String url) {
        urlCheckLimitCcSof = url;
    }

    @Override
    public void setUrlTerm(String url) {
        urlTerm = url;
    }

    @Override
    public void start() {
        super.start();
        getLimitCcSof();
        getAccountWithSaldo();
    }

    @Override
    public void stop() {
        if (isViewAttached())
            getView().onGetLimitComplete();
        super.stop();
    }
}