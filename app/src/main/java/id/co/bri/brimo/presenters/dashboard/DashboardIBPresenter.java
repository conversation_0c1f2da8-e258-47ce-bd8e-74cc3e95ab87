package id.co.bri.brimo.presenters.dashboard;

import id.co.bri.brimo.contract.IPresenter.dashboard.IDasboardIBPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.dashboard.IDashboardIBView;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.menudashboard.MenuDashboardSource;
import id.co.bri.brimo.data.repository.menudashfav.MenuDashFavSource;
import id.co.bri.brimo.data.repository.menukategori.MenuKategoriSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.config.AppConfig;
import id.co.bri.brimo.domain.config.MenuConfig;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.MenuModel;
import id.co.bri.brimo.models.PFMTotalAmountResponse;
import id.co.bri.brimo.models.apimodel.request.BaseSubscribeTopicRequest;
import id.co.bri.brimo.models.apimodel.request.DetailPromoRequest;
import id.co.bri.brimo.models.apimodel.request.EwalletBindingTypeRequest;
import id.co.bri.brimo.models.apimodel.request.IdRequest;
import id.co.bri.brimo.models.apimodel.request.KategoriPromoRequest;
import id.co.bri.brimo.models.apimodel.request.LatestBlastNotifRequest;
import id.co.bri.brimo.models.apimodel.request.NotifReadFastMenuRequest;
import id.co.bri.brimo.models.apimodel.request.NotifUnreadsRequest;
import id.co.bri.brimo.models.apimodel.request.SafetyPusatBantuanRequest;
import id.co.bri.brimo.models.apimodel.request.dompetdigitalrevamp.InquiryDompetDigitalRequest;
import id.co.bri.brimo.models.apimodel.response.EwalletBalanceListResponse;
import id.co.bri.brimo.models.apimodel.response.EwalletProductListResponse;
import id.co.bri.brimo.models.apimodel.response.EwalletUnbindingResponse;
import id.co.bri.brimo.models.apimodel.request.ResendSmsRequest;
import id.co.bri.brimo.models.apimodel.response.NotifUnreadsResponse;
import id.co.bri.brimo.models.apimodel.response.NotifikasiBlastResponse;
import id.co.bri.brimo.models.apimodel.response.ProductIbbizResponse;
import id.co.bri.brimo.models.apimodel.response.PromoResponse;
import id.co.bri.brimo.models.apimodel.response.QuestionResponse;
import id.co.bri.brimo.models.apimodel.response.RegisKontakResponse;
import id.co.bri.brimo.models.apimodel.response.RegisMagicLinkResponse;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.models.apimodel.response.SafetyModeResponse;
import id.co.bri.brimo.models.apimodel.response.SaldoReponse;
import id.co.bri.brimo.models.apimodel.response.dompetdigitalrevamp.InquiryDompetDigitalResponse;
import id.co.bri.brimo.models.daomodel.DashboardMenu.MenuDashFav;
import id.co.bri.brimo.presenters.MvpPresenter;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.annotations.NonNull;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.disposables.Disposable;
import io.reactivex.observers.DisposableCompletableObserver;
import io.reactivex.schedulers.Schedulers;

public class DashboardIBPresenter<V extends IMvpView & IDashboardIBView> extends MvpPresenter<V>
        implements IDasboardIBPresenter<V> {

    private static final String TAG = "DashboardIBPresenter";
    private static final String TAG_INFO_TYPE = "0";
    private String urlSaldo;
    private String formUrl;
    private String kategoriItemUrl;
    private String detailUrl;
    private String subscribeUrl;
    private String inquiryUrl = "";
    private String latestBlastUrl;
    private String imagePopup;
    private String promoId;
    private String notifUnreadUrl;
    private String urlRead;
    private String urlIbbiz;
    private String urlSafetyMode, urlPFMSummary;
    private String additionalData;
    private String urlEwalletBindingList;
    private String urlEwalletBalance;
    private String urlEwalletUnbinding;
    private boolean isAnyEwalletBinded;
    private String urlMagicLinkRegis;
    private String urlOtpEmailRegis;
    private String urlInfoSaldoHold;
    private String urlPusatBantuanSafety;
    private String urlBiometric;

    private int blastId;

    protected Object confirmationRequest;
    protected Object detailPromoRequest;
    protected Object eWalletRequest;

    protected MenuDashFavSource menuDashFavSource;

    private MenuDashboardSource mMenuDashboardSource;

    private MenuKategoriSource mMenuKategoriSource;

    public DashboardIBPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository,
                                ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource,
                                AnggaranPfmSource anggaranPfmSource, MenuDashFavSource menuDashFavSource, MenuDashboardSource menuDashboardSource,
                                MenuKategoriSource menuKategoriSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
        this.menuDashFavSource = menuDashFavSource;
        this.mMenuDashboardSource = menuDashboardSource;
        this.mMenuKategoriSource = menuKategoriSource;
    }

    @Override
    public void onCloseButtonClicked() {
        // do nothing
    }

    @Override
    public void getOnboarding() {
        // do nothing
    }

    @Override
    public void start() {
        super.start();

        //pop up notif enable
        setDisablePopup(false);

        //set user type exist
        getBRImoPrefRepository().saveUserExist(true);

    }

    @Override
    public void setFormUrl(String formUrl) {
        this.formUrl = formUrl;
    }

    @Override
    public void setSubscribeUrl(String subscribeUrl) {
        this.subscribeUrl = subscribeUrl;
    }

    @Override
    public void setKategoriItemUrl(String kategoriItemUrl) {
        this.kategoriItemUrl = kategoriItemUrl;
    }

    @Override
    public void setDetailItemUrl(String detailItemUrl) {
        this.detailUrl = detailItemUrl;
    }

    @Override
    public void setLatestBlastNotifUrl(String latestBlastUrl) {
        this.latestBlastUrl = latestBlastUrl;
    }


    /**
     * Get SALDO default
     */
    @Override
    public void getSaldo() {
        if (getView() != null) {
            //initiate param with getter from view
            String seqNum = getBRImoPrefRepository().getSeqNumber();
            Disposable disposable = getApiSource().getDataTanpaRequest(urlSaldo, seqNum)//function(param)
                    .subscribeOn(getSchedulerProvider().io())
                    .observeOn(getSchedulerProvider().mainThread())
                    .subscribeWith(new ApiObserver(getView(), seqNum) {

                        @Override
                        protected void onFailureHttp(String errorMessage) {
                            getView().onExceptionIgnore(errorMessage);
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            SaldoReponse saldoReponse = response.getData(SaldoReponse.class);
                            getView().onGetSaldo(saldoReponse);

                            if (saldoReponse != null) {
                                getBRImoPrefRepository().saveSaldoHold(saldoReponse.isOnHold());
                                getBRImoPrefRepository().saveSaldoRekeningUtama(String.valueOf(saldoReponse.getBalance()));
                                getBRImoPrefRepository().saveSaldoRekeningUtamaString(saldoReponse.getBalanceString());
                                getBRImoPrefRepository().saveCurrency(saldoReponse.getCurrency());
                                getBRImoPrefRepository().saveNameRekeningUtama(saldoReponse.getName());
                                getBRImoPrefRepository().saveAccountDefault(saldoReponse.getAccount());
                            }
                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {

                            if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue())) {
                                getView().onSessionEnd(restResponse.getDesc());
                            } else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_99.getValue()))
                                getView().onException99(restResponse.getDesc());
                            else {
                                getView().onExceptionIgnore(restResponse.getDesc());
                                getView().onSaldoNoResp();
                                getBRImoPrefRepository().saveSaldoRekeningUtama(String.valueOf(0));
                                getBRImoPrefRepository().saveSaldoRekeningUtamaString("-");
                            }
                        }
                    });

            getCompositeDisposable().add(disposable);
        }
    }

    @Override
    public void getBubbleShowNewOnboarding() {
        if (Boolean.TRUE.equals(getBRImoPrefRepository().getBubbleNewOnboarding())) {
            getView().showBubbleNewOnboarding();
            getBRImoPrefRepository().saveBubbleNewOnboarding(false);
        }
    }

    @Override
    public void updateIsNewFalse(int menuId, int menuStatus) {
        if (isViewAttached()) {
            getCompositeDisposable().add(mMenuDashboardSource.updateIsNew(menuId, menuStatus)
                    .subscribeOn(getSchedulerProvider().io())
                    .observeOn(getSchedulerProvider().mainThread())
                    .subscribeWith(new DisposableCompletableObserver() {
                        @Override
                        public void onComplete() {
                            getView().onSuccessUpdate(menuId);
                        }

                        @Override
                        public void onError(@androidx.annotation.NonNull Throwable e) {
                            // do nothing
                        }
                    })
            );
        }
    }

    @Override
    public void updateKategori(int kategoriId) {
        if (isViewAttached()) {
            getCompositeDisposable().add(mMenuKategoriSource.updateKategori(kategoriId, MenuConfig.NewStatus.OLD)
                    .subscribeOn(getSchedulerProvider().io())
                    .observeOn(getSchedulerProvider().mainThread())
                    .subscribeWith(new DisposableCompletableObserver() {
                        @Override
                        public void onComplete() {
                            //update object
                        }

                        @Override
                        public void onError(@androidx.annotation.NonNull Throwable e) {
                            // do nothing
                        }
                    })
            );
        }
    }

    /**
     * Update Token FIREBASE
     */
    @Override
    public void updateTokenFirebase() {

        BaseSubscribeTopicRequest request = new BaseSubscribeTopicRequest(
                getBRImoPrefRepository().getUsername(),
                getBRImoPrefRepository().getTokenKey(),
                getBRImoPrefRepository().getFirebaseToken()
        );

        String seqNum = getBRImoPrefRepository().getSeqNumber();

        getCompositeDisposable().add(
                getApiSource().getUpdateTokenFirebase(request, seqNum)
                        .subscribeOn(getSchedulerProvider().io())
                        .observeOn(getSchedulerProvider().mainThread())
                        .subscribeWith(new ApiObserver(getView(), seqNum) {

                            @Override
                            protected void onFailureHttp(String type) {
                                getBRImoPrefRepository().saveUpdateTokenFirebase(false);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                // do nothing
                                getBRImoPrefRepository().saveUpdateTokenFirebase(true);
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getBRImoPrefRepository().saveUpdateTokenFirebase(false);
                            }
                        })
        );
    }

    /**
     * Load Menu DEFAULT
     */
    @Override
    public void getMenuDefault() {
        getCompositeDisposable().add(menuDashFavSource.getDashMenuFav()
                .subscribeOn(getSchedulerProvider().io())
                .observeOn(getSchedulerProvider().mainThread())
                .subscribe(dashMenuFavs -> {
                    if (dashMenuFavs != null) {
                        List<MenuModel> menuModelList = new ArrayList<>();
                        for (int i = 0; i < dashMenuFavs.size(); i++) {
                            // TODO: 03/03/2023 perubahan menu setelah ltmpt ke snpmb
                            if (dashMenuFavs.get(i).getId() != MenuConfig.MenuId.MENU_LTMPT) {
                                menuModelList.add(new MenuModel(dashMenuFavs.get(i).getId(), dashMenuFavs.get(i).getMenuId(), dashMenuFavs.get(i).getMenuName(), dashMenuFavs.get(i).getMenuIcon(),
                                        dashMenuFavs.get(i).getMenu(), dashMenuFavs.get(i).isNew(), dashMenuFavs.get(i).getGroupName()));
                            } else {
                            }
                        }

                        getView().onInitiateMenu(menuModelList);
                    } else {
                        saveMenuFavDefault();
                    }
                }, throwable -> getView().onException(throwable.getMessage())));
    }

    protected void saveMenuFavDefault() {
        List<MenuDashFav> defaultMenuDashFavs = new ArrayList<>();
        defaultMenuDashFavs.addAll(MenuConfig.fetchMenuDashFav());

        if (!defaultMenuDashFavs.isEmpty() && menuDashFavSource != null) {
            getCompositeDisposable().add(menuDashFavSource.insertMenuDashAll(defaultMenuDashFavs)
                    .subscribeOn(getSchedulerProvider().io())
                    .observeOn(getSchedulerProvider().mainThread())
                    .subscribeWith(new DisposableCompletableObserver() {
                        @Override
                        public void onComplete() {
                            // do nothing
                            getMenuDefault();
                        }

                        @Override
                        public void onError(@NonNull Throwable e) {
                            // do nothing
                        }
                    }));
        }
    }

    /**
     * Get Daftar PROMO terbaru
     */
    @Override
    public void getPromoFeatured() {
        if (!isViewAttached() || formUrl.isEmpty())
            return;
        getView().isHideSkeletonPromo(false);
        String seqNum = getBRImoPrefRepository().getSeqNumber();

        getCompositeDisposable().add(
                getApiSource().getDataForm(formUrl, seqNum)
                        .subscribeOn(getSchedulerProvider().io())
                        .observeOn(getSchedulerProvider().mainThread())
                        .subscribeWith(new ApiObserver(getView(), seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().isHideSkeletonPromo(true);
                                getView().onFailedPromoFeatured();
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                getView().isHideSkeletonPromo(true);
                                PromoResponse promoResponse = response.getData(PromoResponse.class);

                                getView().onSuccessGetPromoFeatured(promoResponse);
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().isHideSkeletonPromo(true);
                                getView().onFailedPromoFeatured();
                            }
                        })
        );
    }

    /**
     * Get Saldo dompet digital
     */
    @Override
    public void getEwalletBindingList() {

        getView().isHideSkeletonEwallet(false);
        if (!isViewAttached() || urlEwalletBindingList.isEmpty()) {
            return;
        }

        String seqNum = getBRImoPrefRepository().getSeqNumber();
        getCompositeDisposable().add(
                getApiSource().getDataTanpaRequest(urlEwalletBindingList, seqNum)
                        .subscribeOn(getSchedulerProvider().io())
                        .observeOn(getSchedulerProvider().mainThread())
                        .subscribeWith(new ApiObserver(getView(), seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().isHideSkeletonEwallet(true);
                                getView().onFailedGetEwallet();
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                isAnyEwalletBinded = false;
                                EwalletProductListResponse ewalletProductListResponse = response.getData(EwalletProductListResponse.class);
                                if (ewalletProductListResponse.getProducts() != null) {
                                    for (int i = 0; i < ewalletProductListResponse.getProducts().size(); i++) {
                                        if (ewalletProductListResponse.getProducts().get(i).getIsBind() == 1) {
                                            isAnyEwalletBinded = true;
                                        }
                                    }
                                }
                                if (isAnyEwalletBinded) {
                                    getView().isHideSkeletonEwallet(false);
                                    getEwalletBalance(ewalletProductListResponse);
                                } else {
                                    getView().isHideSkeletonEwallet(true);
                                    getView().onSuccessGetEwalletList(ewalletProductListResponse);
                                    return;
                                }
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().isHideSkeletonEwallet(true);
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                    getView().onSessionEnd(restResponse.getDesc());
                                else
                                    getView().onFailedGetEwallet();
                            }
                        })
        );
    }

    @Override
    public void getEwalletBalance(EwalletProductListResponse ewalletProductListResponse) {
        getView().isHideSkeletonEwallet(false);

        if (!isViewAttached() || urlEwalletBalance.isEmpty())
            return;

        String seqNum = getBRImoPrefRepository().getSeqNumber();

        getCompositeDisposable().add(
                getApiSource().getDataTanpaRequest(urlEwalletBalance, seqNum)
                        .subscribeOn(getSchedulerProvider().io())
                        .observeOn(getSchedulerProvider().mainThread())
                        .subscribeWith(new ApiObserver(getView(), seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().isHideSkeletonEwallet(true);
                                getView().onSuccessGetEwalletList(ewalletProductListResponse);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                getView().isHideSkeletonEwallet(true);
                                EwalletBalanceListResponse ewalletBalanceListResponse = response.getData(EwalletBalanceListResponse.class);
                                getView().onSuccessGetEwalletList(ewalletProductListResponse, ewalletBalanceListResponse);
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().onSuccessGetEwalletList(ewalletProductListResponse);
                                getView().isHideSkeletonEwallet(true);
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                    getView().onSessionEnd(restResponse.getDesc());
                                else
                                    getView().onException(restResponse.getDesc());
                            }
                        })
        );
    }

    @Override
    public void getTimerSafetyMode() {

        if (!isViewAttached() || urlSafetyMode.isEmpty())
            return;

        String seqNum = getBRImoPrefRepository().getSeqNumber();

        getCompositeDisposable().add(
                getApiSource().getDataTanpaRequest(urlSafetyMode, seqNum)
                        .subscribeOn(getSchedulerProvider().io())
                        .observeOn(getSchedulerProvider().mainThread())
                        .subscribeWith(new ApiObserver(getView(), seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SUCCESS.getValue())) {
                                    SafetyModeResponse safetyModeResponse = response.getData(SafetyModeResponse.class);
                                    getView().onSuccessTimerSafetyMode(safetyModeResponse);
                                }
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue())) {
                                    getView().onSessionEnd(restResponse.getDesc());
                                } else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_12.getValue())) {
                                    getView().onSafetyMode01();
                                } else {
                                    getView().onException(restResponse.getDesc());
                                }
                            }
                        })
        );
    }

    @Override
    public void setUnbindingEwallet(String type) {
        if (!isViewAttached() || urlEwalletUnbinding.isEmpty())
            return;

        String seqNum = getBRImoPrefRepository().getSeqNumber();
        getView().showProgress();
        eWalletRequest = new EwalletBindingTypeRequest(type);
        getCompositeDisposable().add(
                getApiSource().getData(urlEwalletUnbinding, eWalletRequest, seqNum)
                        .subscribeOn(getSchedulerProvider().io())
                        .observeOn(getSchedulerProvider().mainThread())
                        .subscribeWith(new ApiObserver(getView(), seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().hideProgress();
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                EwalletUnbindingResponse ewalletUnbindingResponse = response.getData(EwalletUnbindingResponse.class);
                                getView().hideProgress();
                                getView().onSuccessUnbindingeWallet(ewalletUnbindingResponse);

                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue())) {
                                    getView().onSessionEnd(restResponse.getDesc());
                                } else {
                                    getView().onException(restResponse.getDesc());
                                }
                                getView().hideProgress();
                            }
                        })
        );
    }


    /**
     * Get PROMO by Kategory Promo
     */
    @Override
    public void setNotificationUnreads(String notifUnreadUrl) {
        this.notifUnreadUrl = notifUnreadUrl;
    }

    @Override
    public void setUrlReadNotif(String urlRead) {
        this.urlRead = urlRead;
    }

    @Override
    public void setUrlIbbiz(String urlIbbiz) {
        this.urlIbbiz = urlIbbiz;
    }

    @Override
    public void setUrlInfoSaldoHold(String urlInfoSaldoHold) {
        this.urlInfoSaldoHold = urlInfoSaldoHold;
    }

    @Override
    public void setSafetyMode(String urlSafety) {
        this.urlSafetyMode = urlSafety;
    }

    @Override
    public void setUrlPFMSummary(String url) {
        this.urlPFMSummary = url;
    }

    @Override
    public void setUrlPusatBantuanSafety(String urlPusatBantuanSafety) {
        this.urlPusatBantuanSafety = urlPusatBantuanSafety;
    }

    @Override
    public void setUrlMagicLinkRegis(String urlMagicLinkRegis) {
        this.urlMagicLinkRegis = urlMagicLinkRegis;
    }

    @Override
    public void setUrlOtpEmailRegis(String urlOtpEmailRegis) {
        this.urlOtpEmailRegis = urlOtpEmailRegis;
    }

    @Override
    public void getInfoSaldoHold() {
        if (urlInfoSaldoHold != null || isViewAttached()) {
            getView().showProgress();

            IdRequest idRequest = new IdRequest("105");
            String seqNum = getBRImoPrefRepository().getSeqNumber();

            getCompositeDisposable()
                    .add(getApiSource().getData(urlInfoSaldoHold, idRequest, seqNum)
                            .subscribeOn(getSchedulerProvider().io())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribeWith(new ApiObserver(getView(), seqNum) {

                                @Override
                                protected void onFailureHttp(String errorMessage) {
                                    //do nothing
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    //TO-DO onSuccess
                                    getView().hideProgress();
                                    QuestionResponse questionResponse = response.getData(QuestionResponse.class);
                                    getView().onSuccessInfoSaldoHold(questionResponse);
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().hideProgress();
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                        getView().onSessionEnd(restResponse.getDesc());
                                    else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_99.getValue()))
                                        getView().onException99(restResponse.getDesc());
                                    else
                                        getView().onException(restResponse.getDesc());
                                }
                            }));
        }
    }

    @Override
    public void getBrimoPrefSaldoHold() {
        getView().getSaldoHoldPref(getBRImoPrefRepository().getBubbleAlertSaldo(), getBRImoPrefRepository().getSaldoHold());
    }

    @Override
    public void saveBrimoPrefSaldoHold() {
        getBRImoPrefRepository().savebubbleAlertSaldo(true);
    }

    @Override
    public void getTotalAmount() {
        if (getView() != null) {
            getView().isHideSkeletonPFM(false);
            String seqNum = getBRImoPrefRepository().getSeqNumber();
            getCompositeDisposable()
                    .add(getApiSource().getDataTanpaRequest(urlPFMSummary, seqNum)
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .subscribeOn(getSchedulerProvider().single())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribeWith(new ApiObserver(getView(), seqNum) {

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    getView().isHideSkeletonPFM(true);
                                    PFMTotalAmountResponse data = response.getData(PFMTotalAmountResponse.class);
                                    getView().successGetTotalAmountPFM(data);
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().isHideSkeletonPFM(true);
                                    getView().failedToLoadAmount();
                                }

                                @Override
                                protected void onFailureHttp(String type) {
                                    getView().isHideSkeletonPFM(true);
                                    getView().failedToLoadAmount();
                                }
                            }));
        }
    }

    @Override
    public void getTermCondition() {
        getView().successTermConditionNewOnboarding(getBRImoPrefRepository().getTermCondition());
        getBRImoPrefRepository().saveBubbleNewOnboarding(false);
    }

    @Override
    public void getKategoriItem(String namaPromo) {
        if (kategoriItemUrl == null || !isViewAttached()) {
            return;
        }

        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();

        confirmationRequest = new KategoriPromoRequest(namaPromo);
        getCompositeDisposable()
                .add(getApiSource().getData(kategoriItemUrl, confirmationRequest, seqNum)
                        .subscribeOn(getSchedulerProvider().io())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(new ApiObserver(getView(), seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                //do nothing
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                //TO-DO onSuccess
                                getView().hideProgress();
                                PromoResponse responsebriva = response.getData(PromoResponse.class);

                                getView().onSuccessKategoriItem(responsebriva);
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                    getView().onSessionEnd(restResponse.getDesc());
                                else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_99.getValue()))
                                    getView().onException99(restResponse.getDesc());
                                //else
                                //    getView().onException(restResponse.getDesc());

                            }
                        }));

    }

    @Override
    public void getDetailItem(String id) {
        if (detailUrl == null || !isViewAttached()) {
            return;
        }

        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();

        detailPromoRequest = new DetailPromoRequest(id);
        getCompositeDisposable()
                .add(getApiSource().getData(detailUrl, detailPromoRequest, seqNum)
                        .subscribeOn(Schedulers.io())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(new ApiObserver(getView(), seqNum) {


                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().hideProgress();
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                //TO-DO onSuccess
                                getView().hideProgress();
                                PromoResponse responsebriva = response.getData(PromoResponse.class);

                                getView().onSuccessGetDetailItem(responsebriva);
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                    getView().onSessionEnd(restResponse.getDesc());
                                else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_99.getValue()))
                                    getView().onException99(restResponse.getDesc());
                                else
                                    getView().onException(restResponse.getDesc());

                            }
                        }));

    }

    /**
     * Method digunakan untuk mendapatkan blast notifikasi terbaru
     */
    @Override
    public void getLatestBlastNotif() {
        if (getView() != null) {
            LatestBlastNotifRequest request = new LatestBlastNotifRequest(
                    getBRImoPrefRepository().getUsername(),
                    getBRImoPrefRepository().getFirebaseToken()
            );

            String seqNum = getBRImoPrefRepository().getSeqNumber();

            getCompositeDisposable()
                    .add(getApiSource().getData(latestBlastUrl, request, seqNum)
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .subscribeOn(getSchedulerProvider().io())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribeWith(new ApiObserver(getView(), seqNum) {

                                @Override
                                protected void onFailureHttp(String errorMessage) {
                                    getView().hideProgress();
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    //TO-DO onSuccess
                                    getView().hideProgress();


                                    if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SUCCESS.getValue())) {
                                        NotifikasiBlastResponse notifikasiBlastResponse = response.getData(NotifikasiBlastResponse.class);

                                        if (getBRImoPrefRepository().getBlastId() != notifikasiBlastResponse.getNotifikasiModel().getBlastId()) {

                                            imagePopup = notifikasiBlastResponse.getNotifikasiModel().getImagePopup();
                                            promoId = String.valueOf(notifikasiBlastResponse.getNotifikasiModel().getPromoId());
                                            blastId = notifikasiBlastResponse.getNotifikasiModel().getBlastId();
                                            additionalData = notifikasiBlastResponse.getNotifikasiModel().getAdditionalPayload();

                                            //menyimpan Blast ID Terakhir
                                            getBRImoPrefRepository().saveBlastId(blastId);

                                            //jika tipe nya INFO
                                            if (promoId.equals(TAG_INFO_TYPE)) {
                                                getView().onGetLatestInfoNotif(imagePopup, String.valueOf(blastId));
                                            } else {
                                                getView().onGetLatestBlastNotif(imagePopup, promoId, String.valueOf(blastId), additionalData);
                                            }

                                        }
                                    }
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().hideProgress();
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                        getView().onSessionEnd(restResponse.getDesc());
                                    else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_99.getValue()))
                                        getView().onException99(restResponse.getDesc());

                                }
                            }));
        }
    }

    @Override
    public void getNotificationUnreads() {
        if (getView() != null) {
            NotifUnreadsRequest request = new NotifUnreadsRequest(
                    "android"
            );

            String seqNum = getBRImoPrefRepository().getSeqNumber();

            getCompositeDisposable()
                    .add(getApiSource().getData(notifUnreadUrl, request, seqNum)
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .subscribeOn(getSchedulerProvider().io())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribeWith(new ApiObserver(getView(), seqNum) {

                                @Override
                                protected void onFailureHttp(String errorMessage) {
                                    getView().hideProgress();
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    getView().hideProgress();
                                    NotifUnreadsResponse notifUnreadsResponse = response.getData(NotifUnreadsResponse.class);
                                    getView().onSuccessNotifUnread(notifUnreadsResponse);
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().hideProgress();
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                        getView().onSessionEnd(restResponse.getDesc());
                                }
                            }));
        }
    }

    @Override
    public void getReadNotifFastMenu(String blastId) {
        NotifReadFastMenuRequest notifRequest = new NotifReadFastMenuRequest("android",
                blastId,
                getBRImoPrefRepository().getTokenKey(),
                getBRImoPrefRepository().getUsername());

        String seqNum = getBRImoPrefRepository().getSeqNumber();

        getCompositeDisposable()
                .add(getApiSource().getData(urlRead, notifRequest, seqNum)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(getSchedulerProvider().io())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(new ApiObserver(getView(), seqNum) {
                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                // do nothing
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                    getView().onSessionEnd(restResponse.getDesc());
                            }
                        }));
    }

    @Override
    public void getDataIbbiz() {
        if (urlIbbiz != null || isViewAttached()) {

            getView().showProgress();
            String seqNum = getBRImoPrefRepository().getSeqNumber();

            getCompositeDisposable()
                    .add(getApiSource().getDataTanpaRequest(urlIbbiz, seqNum)
                            .subscribeOn(Schedulers.io())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribeWith(new ApiObserver(getView(), seqNum) {
                                @Override
                                protected void onFailureHttp(String errorMessage) {
                                    getView().hideProgress();
                                    getView().onException(errorMessage);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    //TO-DO onSuccess
                                    getView().hideProgress();
                                    ProductIbbizResponse productResponse = response.getData(ProductIbbizResponse.class);
                                    if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SUCCESS.getValue())) {
                                        getView().onProductIbbiz(productResponse);
                                    } else if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_01.getValue()))
                                        getView().onSuccessIbbiz(productResponse);
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().hideProgress();
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                        getView().onSessionEnd(restResponse.getDesc());
                                    else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_99.getValue()))
                                        getView().onException99(restResponse.getDesc());
                                    else
                                        getView().onException(restResponse.getDesc());
                                }
                            }));
        }
    }

    @Override
    public void getDataInquiryDompetDigital(InquiryDompetDigitalRequest request) {
        if (inquiryUrl == null || !isViewAttached()) {
            return;
        }

        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        getCompositeDisposable()
                .add(getApiSource().getData(inquiryUrl, request, seqNum)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(Schedulers.io())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(new ApiObserver(getView(), seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().hideProgress();
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                //TO-DO onSuccess
                                getView().hideProgress();
                                InquiryDompetDigitalResponse responWallet = response.getData(InquiryDompetDigitalResponse.class);
                                getView().onSuccessGetInquiry(responWallet);
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                    getView().onSessionEnd(restResponse.getDesc());
                                else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_99.getValue()))
                                    getView().onException99(restResponse.getDesc());
                                else
                                    getView().onException(restResponse.getDesc());

                            }
                        }));
    }

    @Override
    public void setInquiryUrlDompetDigital(String url) {
        inquiryUrl = url;
    }

    @Override
    public void setUrlEwalletBindingList(String url) {
        urlEwalletBindingList = url;
    }

    @Override
    public void setUrlEwalletBalance(String url) {
        urlEwalletBalance = url;
    }

    @Override
    public void setUrlEwalletUnBinding(String url) {
        urlEwalletUnbinding = url;
    }

    @Override
    public void getDataMagicLinkRegis() {
        if (isViewAttached()) {

            getView().showProgress();
            String seqNum = getBRImoPrefRepository().getSeqNumber();

            getCompositeDisposable().add(getApiSource().getData(urlMagicLinkRegis, sendRequestCheckPoinRegis(), seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(new ApiObserver(getView(), seqNum) {
                        @Override
                        protected void onFailureHttp(String errorMessage) {
                            getView().hideProgress();
                            getView().onException(errorMessage);
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            getView().hideProgress();
                            RegisKontakResponse regisMagicLinkResponse = response.getData(RegisKontakResponse.class);
                            getBRImoPrefRepository().saveTokenKey(regisMagicLinkResponse.getTokenKey());
                            getBRImoPrefRepository().saveVerifikasiId(regisMagicLinkResponse.getResponseId());
                            getView().getMagicLinkRegisSuccess(regisMagicLinkResponse);
                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            getView().hideProgress();
                            getView().onException(restResponse.getDesc());
                        }
                    }));
        }
    }

    @Override
    public void getDataOtpEmailRegis() {
        if (isViewAttached()) {

            getView().showProgress();
            String seqNum = getBRImoPrefRepository().getSeqNumber();

            getCompositeDisposable().add(getApiSource().getData(urlOtpEmailRegis, sendRequestCheckPoinRegis(), seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(new ApiObserver(getView(), seqNum) {
                        @Override
                        protected void onFailureHttp(String errorMessage) {
                            getView().hideProgress();
                            getView().onException(errorMessage);
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            getView().hideProgress();
                            int tsLong = (int) (System.currentTimeMillis() / 1000);

                            RegisMagicLinkResponse regisMagicLinkResponse = response.getData(RegisMagicLinkResponse.class);
                            getBRImoPrefRepository().saveVerifikasiId(regisMagicLinkResponse.getResponseId());
                            getBRImoPrefRepository().saveTimeStamp(tsLong);
                            getBRImoPrefRepository().saveExpiredTime(regisMagicLinkResponse.getOtpExpiredInSecond());
                            getView().getOtpEmailRegisSuccess(regisMagicLinkResponse);
                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            getView().hideProgress();
                            getView().onException(restResponse.getDesc());
                        }
                    }));
        }
    }

    @Override
    public void getPusatBantuanSafety(String id) {
        if (urlPusatBantuanSafety == null || !isViewAttached()) {
//            Log.d(TAG, "getDataInquiry: view atau inquiry urlInformasi null");
            return;
        }

        getView().showProgress();
        SafetyPusatBantuanRequest safetyPusatBantuanRequest = new SafetyPusatBantuanRequest(id);
        String seqNum = getBRImoPrefRepository().getSeqNumber();

        getCompositeDisposable()
                .add(getApiSource().getData(urlPusatBantuanSafety, safetyPusatBantuanRequest, seqNum)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(Schedulers.io())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(new ApiObserver(getView(), seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().hideProgress();
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                //TO-DO onSuccess
                                getView().hideProgress();
                                QuestionResponse topicQuestionResponse = response.getData(QuestionResponse.class);

                                if (urlPusatBantuanSafety != null)
                                    getView().onSuccessPusatBantuanSafety(topicQuestionResponse);
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                    getView().onSessionEnd(restResponse.getDesc());
                                else
                                    getView().onException(restResponse.getDesc());

                            }
                        }));

    }

    @Override
    public ResendSmsRequest sendRequestCheckPoinRegis() {
        return new ResendSmsRequest(getBRImoPrefRepository().getVerifikasiId());
    }

    @Override
    public void setUrlSaldo(String urlSaldo) {
        this.urlSaldo = urlSaldo;
    }

}