package id.co.bri.brimo.presenters.qrmpm;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.concurrent.TimeUnit;

import id.co.bri.brimo.R;
import id.co.bri.brimo.contract.IPresenter.qrmpm.IInquiryQrMPMPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.base.IBaseInquiryView;
import id.co.bri.brimo.contract.IView.qrmpm.IInquiryQrMPMView;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.config.AppConfig;
import id.co.bri.brimo.domain.converter.MapperHelper;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.apimodel.request.ConfirmationMPMRequest;
import id.co.bri.brimo.models.apimodel.request.FastConfirmationMPMRequest;
import id.co.bri.brimo.models.apimodel.request.ccqrismpm.SetDefaultQrPaymentRequest;
import id.co.bri.brimo.models.apimodel.response.cc.DetailCcSofResponse;
import id.co.bri.brimo.models.apimodel.response.GeneralConfirmationResponse;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.models.apimodel.response.SaldoReponse;
import id.co.bri.brimo.presenters.base.BaseInquiryPresenter;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.disposables.Disposable;
import io.reactivex.schedulers.Schedulers;

public class InquiryQrMPMPresenter<V extends IMvpView & IBaseInquiryView & IInquiryQrMPMView>
        extends BaseInquiryPresenter<V> implements IInquiryQrMPMPresenter<V> {

    public InquiryQrMPMPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable,
                                 BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource,
                                 TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, categoryPfmSource, transaksiPfmSource, anggaranPfmSource);
    }

    @Override
    public void setUrlConfirmation(String urlConfirmation) {
        this.urlConfirmation = urlConfirmation;
    }

    private String urlChangeSof ="";
    @Override
    public void setUrlChangeSof(String urlChangeSof) {
        this.urlChangeSof = urlChangeSof;
    }

    @Override
    public void changeSofCc(SetDefaultQrPaymentRequest request) {
        if (!isViewAttached()) return;
        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();

        Disposable disposable = getApiSource().getData(urlChangeSof, request, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(new ApiObserver(getView(),seqNum) {

                    @Override
                    protected void onFailureHttp(String errorMessage) {
                        getView().hideProgress();
                        getView().onException(errorMessage);
                    }

                    @Override
                    protected void onApiCallSuccess(RestResponse response) {
                        getView().hideProgress();
                        getView().onSuccessChangeSof(
                                restResponse.getDesc()
                        );
                    }

                    @Override
                    protected void onApiCallError(RestResponse restResponse) {
                        getView().hideProgress();
                        onApiError(restResponse);

                    }
                });

        getCompositeDisposable().add(disposable);
    }

    /**
     * @param refNum
     * @param accountNum
     * @param amount
     * @param save
     * @param tip
     */
    @Override
    public void getDataConfirmationMPM(String refNum, String accountNum, String amount, String save, String tip, boolean isFromFastMenu) {
        if (isViewAttached()) {
            //flag on Load true

            getView().showProgress();
            if (isFromFastMenu) {
                confirmationRequest = new FastConfirmationMPMRequest(getBRImoPrefRepository().getUsername(), getBRImoPrefRepository().getTokenKey(), refNum, accountNum, amount, save, tip);
            } else {
                confirmationRequest = new ConfirmationMPMRequest(refNum, accountNum, amount, save, tip);
            }

            String seqNum = getBRImoPrefRepository().getSeqNumber();

            Disposable disposable = getApiSource().getData(urlConfirmation, confirmationRequest, seqNum)//function(param)
                    .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(new ApiObserver(getView(),seqNum) {

                        @Override
                        protected void onFailureHttp(String errorMessage) {
                            getView().onException(errorMessage);
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            getView().hideProgress();
                            GeneralConfirmationResponse brivaResponse = response.getData(GeneralConfirmationResponse.class);
                            getView().onSuccessGetConfirmation(brivaResponse);
                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            getView().hideProgress();
                            if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                getView().onSessionEnd(restResponse.getDesc());
                            else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.getValue()))
                                getView().onException93(restResponse.getDesc());
                            else
                                getView().onException(restResponse.getDesc());
                        }
                    });

            getCompositeDisposable().add(disposable);
        }
    }

    @Override
    public void getBalanceSavings(String account) {
        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        Disposable disposable = getApiSource().getSaldoNormal(account, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                .subscribeOn(getSchedulerProvider().newThread())
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(new ApiObserver(getView(), seqNum) {
                    @Override
                    protected void onFailureHttp(String errorMessage) {
                        getView().hideProgress();
                        getView().onException(errorMessage);
                    }

                    @Override
                    protected void onApiCallSuccess(RestResponse response) {
                        getView().hideProgress();
                        SaldoReponse saldoReponse;
                        if (restResponse.getDesc().equalsIgnoreCase("01")) {
                            saldoReponse = new SaldoReponse();
                            saldoReponse.setBalanceString(restResponse.getDesc());
                        } else {
                            saldoReponse = restResponse.getData(SaldoReponse.class);
                        }
                        getView().onSuccessSofSavingBalance(saldoReponse);
                    }

                    @Override
                    protected void onApiCallError(RestResponse restResponse) {
                        getView().hideProgress();
                        SaldoReponse saldoReponse = new SaldoReponse();
                        if (restResponse.getCode().equals("12")) {
                            saldoReponse.setBalanceString(restResponse.getCode());
                            saldoReponse.setName(restResponse.getDesc());
                            getView().onSuccessSofSavingBalance(saldoReponse);
                        } else if (restResponse.getCode().equalsIgnoreCase("05")) {
                            saldoReponse.setBalanceString(restResponse.getCode());
                            getView().onSessionEnd(restResponse.getDesc());
                        } else {
                            saldoReponse.setBalanceString("TO");
                            getView().onSuccessSofSavingBalance(saldoReponse);
                        }
                    }
                });
        getCompositeDisposable().add(disposable);
    }

    @Override
    public void getLimitSaldoCc(String token) {
        saveAlreadySelectCc(token);
        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        Disposable disposable = getApiSource()
                .getSaldoCc(token, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                .subscribeOn(getSchedulerProvider().newThread())
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(new ApiObserver(getView(),seqNum) {

                    @Override
                    protected void onFailureHttp(String errorMessage) {
                        getView().onException(errorMessage);
                    }

                    @Override
                    protected void onApiCallSuccess(RestResponse restResponse) {
                        DetailCcSofResponse detailCcSofResponse = restResponse.getData(DetailCcSofResponse.class);

                        getView().hideProgress();
                        SaldoReponse saldoReponse = null;

                        String responseCheck = MapperHelper.getIdResponse(stringResponse);

                        if (responseCheck.isEmpty()) {
                            detailCcSofResponse = new DetailCcSofResponse();
                            detailCcSofResponse.setBalanceString("TO");
                        }

                        restResponse = MapperHelper.stringToRestResponse(stringResponse, seqNum);


                        if (restResponse != null) {

                            if (GeneralHelper.isContains(R.array.response_code_success, restResponse.getCode())) {

                                if (restResponse.getDesc().equalsIgnoreCase("01")) {
                                    detailCcSofResponse = new DetailCcSofResponse();
                                    detailCcSofResponse.setBalanceString(restResponse.getDesc());
                                } else {
                                    detailCcSofResponse = restResponse.getData(DetailCcSofResponse.class);
                                    saldoReponse = restResponse.getData(SaldoReponse.class);
                                }

                            } else if (restResponse.getCode().equals("12")) {
                                detailCcSofResponse = new DetailCcSofResponse();
                                detailCcSofResponse.setBalanceString(restResponse.getCode());
                                detailCcSofResponse.setName(restResponse.getDesc());
                                saldoReponse = new SaldoReponse();
                                saldoReponse.setBalanceString(restResponse.getCode());
                                saldoReponse.setName(restResponse.getDesc());
                            } else if (restResponse.getCode().equalsIgnoreCase("05")) {
                                detailCcSofResponse = new DetailCcSofResponse();
                                detailCcSofResponse.setBalanceString(restResponse.getCode());
                                saldoReponse = new SaldoReponse();
                                saldoReponse.setBalanceString(restResponse.getCode());
                                getView().onSessionEnd(restResponse.getDesc());
                            } else {
                                detailCcSofResponse = new DetailCcSofResponse();
                                detailCcSofResponse.setBalanceString("TO");
                                saldoReponse = new SaldoReponse();
                                saldoReponse.setBalanceString("TO");
                            }
                        } else {
                            detailCcSofResponse = new DetailCcSofResponse();
                            detailCcSofResponse.setBalanceString("TO");
                        }
                       getView().onSucessSofCcSaldo(detailCcSofResponse, saldoReponse);

                    }

                    @Override
                    protected void onApiCallError(RestResponse restResponse) {
                        getView().hideProgress();
                        getView().onException(restResponse.getDesc());
                    }
                });

        getCompositeDisposable().add(disposable);
    }

    @Override
    public void checkCcAlreadySelect(String token) {
       if (getBRImoPrefRepository().getListCcAlreadySelectOnQris().isEmpty() ||
            getBRImoPrefRepository().getListCcAlreadySelectOnQris().equalsIgnoreCase("null")){
            saveAlreadySelectCc(token);
            getView().onShowDialogCcAsSof();
        }else {
            if (!isAlreadySelectCc(token)){
                saveAlreadySelectCc(token);
                getView().onShowDialogCcAsSof();
            }

        }

    }

    private void saveAlreadySelectCc(String token){
        ArrayList<String> list = new ArrayList<>();
        String json = getBRImoPrefRepository().getListCcAlreadySelectOnQris();
        Type type = new TypeToken<ArrayList<String>>() { }.getType();
        if (!json.isEmpty()){
            list = new Gson().fromJson(json, type);
        }

        list.add(token);

        String saveJson = new Gson().toJson(list);
        getBRImoPrefRepository().saveListCcAlreadySelectOnQris(saveJson);
     }

    private Boolean isAlreadySelectCc(String token){
        String json = getBRImoPrefRepository().getListCcAlreadySelectOnQris();
        Type type = new TypeToken<ArrayList<String>>() { }.getType();
        ArrayList<String> list = new Gson().fromJson(json, type);
        return list.contains(token);
    }
}