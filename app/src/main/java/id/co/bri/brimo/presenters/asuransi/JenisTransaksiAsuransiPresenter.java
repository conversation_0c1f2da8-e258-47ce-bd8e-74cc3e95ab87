package id.co.bri.brimo.presenters.asuransi;

import java.util.concurrent.TimeUnit;

import id.co.bri.brimo.contract.IPresenter.asuransi.IJenisTransaksiAsuransiPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.asuransi.IJenisTransaksiAsuransiView;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.config.AppConfig;

import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.apimodel.response.JenisTransaksiAsuransiResponse;
import id.co.bri.brimo.models.apimodel.response.ProdukAsuransiResponse;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.presenters.MvpPresenter;
import io.reactivex.disposables.CompositeDisposable;

public class JenisTransaksiAsuransiPresenter<V extends IMvpView & IJenisTransaksiAsuransiView> extends MvpPresenter<V> implements IJenisTransaksiAsuransiPresenter<V> {

    private String url;
    private String urlProduk;

    public JenisTransaksiAsuransiPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void getJenisTransaksi() {
        if (isViewAttached()){
            String seqNum = getBRImoPrefRepository().getSeqNumber();
            getCompositeDisposable().add(getApiSource().getData(url,"",seqNum)
            .subscribeOn(getSchedulerProvider().single())
            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
            .observeOn(getSchedulerProvider().mainThread())
            .subscribeWith(new ApiObserver(getView(),seqNum) {
                @Override
                protected void onFailureHttp(String type) {
                    getView().onException(type);
                }

                @Override
                protected void onApiCallSuccess(RestResponse response) {
                    JenisTransaksiAsuransiResponse jenisTransaksiAsuransiResponse = response.getData(JenisTransaksiAsuransiResponse.class);
                    getView().onSuccess(jenisTransaksiAsuransiResponse);
                }

                @Override
                protected void onApiCallError(RestResponse restResponse) {
                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue())) {
                        getView().onSessionEnd(restResponse.getDesc());
                    }
                    else
                        getView().onException(restResponse.getDesc());
                }
            }));
        }
    }

    @Override
    public void getProduk() {
        if (isViewAttached()){
            String seqNum = getBRImoPrefRepository().getSeqNumber();
            getCompositeDisposable().add(getApiSource().getData(urlProduk,"",seqNum)
                    .subscribeOn(getSchedulerProvider().single())
                    .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                    .observeOn(getSchedulerProvider().mainThread())
                    .subscribeWith(new ApiObserver(getView(),seqNum) {
                        @Override
                        protected void onFailureHttp(String type) {
                            getView().hideProgress();
                            getView().onException(type);
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            getView().hideProgress();
                            ProdukAsuransiResponse produkAsuransiResponse = response.getData(ProdukAsuransiResponse.class);
                            getView().onSuccessGetProduk(produkAsuransiResponse);
                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            getView().hideProgress();
                            if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue())) {
                                getView().onSessionEnd(restResponse.getDesc());
                            }
                            else
                                getView().onException(restResponse.getDesc());
                        }
                    }));
        }
    }

    @Override
    public void start() {
        super.start();
        getJenisTransaksi();
    }

    @Override
    public void setUrl(String url) {
        this.url = url;
    }

    @Override
    public void setUrlProduk(String urlProduk) {
        this.urlProduk = urlProduk;
    }
}