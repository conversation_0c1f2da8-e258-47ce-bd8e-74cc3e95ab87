package id.co.bri.brimo.presenters.pusatBantuan;

import id.co.bri.brimo.contract.IPresenter.pusatBantuan.IPusatBantuanPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.base.IBaseFormView;
import id.co.bri.brimo.contract.IView.pusatBantuan.IPusatBantuanView;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.config.AppConfig;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.apimodel.request.SearchRequest;
import id.co.bri.brimo.models.apimodel.request.TopicRequest;
import id.co.bri.brimo.models.apimodel.response.PusatBantuanResponse;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.models.apimodel.response.TopicQuestionResponse;
import id.co.bri.brimo.presenters.base.BaseFormPresenter;

import java.util.concurrent.TimeUnit;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.disposables.Disposable;
import io.reactivex.schedulers.Schedulers;

public class PusatBantuanPresenter<V extends IMvpView & IBaseFormView & IPusatBantuanView> extends BaseFormPresenter<V> implements IPusatBantuanPresenter<V> {

    protected String formServisUrl;
    protected String detailUrl;
    protected String searchUrl;

    public PusatBantuanPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, categoryPfmSource, transaksiPfmSource, anggaranPfmSource);
    }

    @Override
    public void getPusatBantuan() {
        if (formServisUrl == null || !isViewAttached()) {
//            Log.d(TAG, "getDataInquiry: view atau inquiry urlInformasi null");
            return;
        }

        String seqNum = getBRImoPrefRepository().getSeqNumber();

            Disposable disposable = getApiSource().getPusatBantuan(seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                    .subscribeOn(getSchedulerProvider().io())
                    .observeOn(getSchedulerProvider().mainThread())
                    .subscribeWith(new ApiObserver(getView(),seqNum) {

                        @Override
                        protected void onFailureHttp(String errorMessage) {
                            getView().hideProgress();
                            getView().onException(errorMessage);
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            getView().hideProgress();
                            PusatBantuanResponse pusatBantuanResponse = response.getData(PusatBantuanResponse.class);
                            if (formServisUrl != null) {
                                getView().onSuccessGetPusatBantuan(pusatBantuanResponse);
                                getView().onSuccessGetTopic(pusatBantuanResponse.getTopic());
                            }
                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            getView().hideProgress();
                            if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                getView().onSessionEnd(restResponse.getDesc());
                            else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_12.getValue()))
                                getView().onExceptionForm(restResponse.getDesc());
                            else
                                getView().onException(restResponse.getDesc());
                        }
                    });
            getCompositeDisposable().add(disposable);

    }

    @Override
    public void getTopicQuestion(String id) {
        if (detailUrl == null || !isViewAttached()) {
//            Log.d(TAG, "getDataInquiry: view atau inquiry urlInformasi null");
            return;
        }

        getView().showProgress();
        TopicRequest topicRequest = new TopicRequest(id);
        String seqNum = getBRImoPrefRepository().getSeqNumber();

        getCompositeDisposable()
                .add(getApiSource().getData(detailUrl, topicRequest, seqNum)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(Schedulers.io())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(new ApiObserver(getView(),seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().hideProgress();
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                //TO-DO onSuccess
                                getView().hideProgress();
                                TopicQuestionResponse topicQuestionResponse = response.getData(TopicQuestionResponse.class);

                                if (detailUrl != null)
                                    getView().onSuccesGetTopicQuestion(topicQuestionResponse);
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                    getView().onSessionEnd(restResponse.getDesc());
                                else
                                    getView().onException(restResponse.getDesc());

                            }
                        }));

    }

    @Override
    public void getSearch(String keyeword) {
        if (searchUrl == null || !isViewAttached()) {
//            Log.d(TAG, "getDataInquiry: view atau inquiry urlInformasi null");
            return;
        }

        getView().showProgress();
        SearchRequest searchRequest = new SearchRequest(keyeword);
        String seqNum = getBRImoPrefRepository().getSeqNumber();

        getCompositeDisposable()
                .add(getApiSource().getData(searchUrl, searchRequest, seqNum)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(Schedulers.io())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(new ApiObserver(getView(),seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                //TO-DO onSuccess
                                getView().hideProgress();
                                getView().hideProgress();
                                TopicQuestionResponse topicQuestionResponse = response.getData(TopicQuestionResponse.class);

                                if (searchUrl != null)
                                    getView().onSuccesGetSearchQuestion(topicQuestionResponse);
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                    getView().onSessionEnd(restResponse.getDesc());
                                else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_12.getValue()))
                                    getView().onExceptionSearch(restResponse.getDesc());
                                else
                                    getView().onException(restResponse.getDesc());

                            }
                        }));

    }

    @Override
    public void setFormUrl(String formUrl) {
        this.formServisUrl = formUrl;
    }

    @Override
    public void setDetailUrl(String detailUrl) {
        this.detailUrl = detailUrl;
    }

    @Override
    public void setSearchUrl(String searchUrl) {
        this.searchUrl = searchUrl;
    }
}