package id.co.bri.brimo.presenters.autograbfund

import id.co.bri.brimo.contract.IPresenter.autograbfund.IDashboardAutoGrabFundPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.autograbfund.IDashboardAutoGrabFundView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimo.data.repository.category.CategoryPfmSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.autograbfund.IdAgfRequest
import id.co.bri.brimo.models.apimodel.request.autograbfund.UpdateStatusAgfRequest
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.autograbfund.DashboardAutoGrabFundResponse
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.disposables.CompositeDisposable

class DashboardAutoGrabFundPresenter<V>(schedulerProvider: SchedulerProvider?, compositeDisposable: CompositeDisposable?, mBRImoPrefRepository: BRImoPrefSource?, apiSource: ApiSource?, categoryPfmSource: CategoryPfmSource?, transaksiPfmSource: TransaksiPfmSource?, anggaranPfmSource: AnggaranPfmSource?) :
    MvpPresenter<V>(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource),

    IDashboardAutoGrabFundPresenter<V> where V : IMvpView, V : IDashboardAutoGrabFundView {
    private var listUrl: String? = null
    private var mUpdateStatusUrl: String? = null
    private var mRemoveUrl: String? = null

    override fun getDataList() {
        if (listUrl == null || !isViewAttached) {
            return
        }
        view?.setSkeletonVisibility(true)
        val seqNum = brImoPrefRepository.seqNumber
        compositeDisposable
            .add(
                apiSource.getDataTanpaRequest(listUrl, seqNum)
                    .subscribeOn(schedulerProvider.io())
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView().setSkeletonVisibility(false)
                            getView()?.onException(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            getView().setSkeletonVisibility(false)
                            val responseList = response.getData(DashboardAutoGrabFundResponse::class.java)
                            getView()?.onSuccessGetList(responseList)
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView().setSkeletonVisibility(false)
                            onApiError(restResponse)
                        }
                    })
            )
    }

    override fun setListUrl(url: String?) {
        this.listUrl = url
    }

    override fun setDeleteUrl(url: String?) {
        this.mRemoveUrl = url
    }

    override fun getDelete(idAgf: String, pin: String) {
        if (mRemoveUrl == null || !isViewAttached) {
            return
        }
        view?.setSkeletonVisibility(true)
        val seqNum = brImoPrefRepository.seqNumber
        val requestIdAgf = IdAgfRequest(idAgf, pin)
        compositeDisposable
            .add(
                apiSource.getData(mRemoveUrl, requestIdAgf, seqNum)
                    .subscribeOn(schedulerProvider.io())
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView()?.setSkeletonVisibility(false)
                            getView()?.onException(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            getView()?.setSkeletonVisibility(false)
                            getView()?.onSuccessRemove(response.desc)
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView()?.setSkeletonVisibility(false)
                            if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_99.value, ignoreCase = true))
                                getView().onException99(restResponse.desc)
                            else
                                getView().onFailedUpdateAndDelete(restResponse.desc)
                        }
                    })
            )
    }

    override fun setUpdateStatusUrl(url: String?) {
        this.mUpdateStatusUrl = url
    }

    override fun getUpdateStatus(idAgf: String, status: String, pin: String) {
        if (mUpdateStatusUrl == null || !isViewAttached) {
            return
        }
        view?.setSkeletonVisibility(true)
        val seqNum = brImoPrefRepository.seqNumber
        val requestUpdateStatus = UpdateStatusAgfRequest(idAgf, status, pin)
        compositeDisposable
            .add(
                apiSource.getData(mUpdateStatusUrl, requestUpdateStatus, seqNum)
                    .subscribeOn(schedulerProvider.io())
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView()?.setSkeletonVisibility(false)
                            getView()?.onException(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            getView()?.setSkeletonVisibility(false)
                            getView()?.onSuccessUpdateStatus(response.desc)
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView()?.setSkeletonVisibility(false)
                            if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_99.value, ignoreCase = true))
                                getView().onException99(restResponse.desc)
                            else
                                getView().onFailedUpdateAndDelete(restResponse.desc)
                        }
                    })
            )
    }

}