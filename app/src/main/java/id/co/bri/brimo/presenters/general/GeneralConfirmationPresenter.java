package id.co.bri.brimo.presenters.general;

import id.co.bri.brimo.contract.IPresenter.general.IGeneralConfirmationPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.general.IGeneralConfirmationView;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.api.observer.ApiObserverKonfirmasi;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.config.DbConfig;
import id.co.bri.brimo.domain.helpers.calendar.CalendarHelper;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.apimodel.request.FastPaymentRequest;
import id.co.bri.brimo.models.apimodel.request.FastPaymentRevampRequest;
import id.co.bri.brimo.models.apimodel.request.PaymentAltRequest;
import id.co.bri.brimo.models.apimodel.request.PaymentRequest;
import id.co.bri.brimo.models.apimodel.request.PaymentRevampRequest;
import id.co.bri.brimo.models.apimodel.request.RencanaPaymentRequest;
import id.co.bri.brimo.models.apimodel.request.cashback.CashbackRequest;
import id.co.bri.brimo.models.apimodel.request.cashback.CashbackRevFilterFMRequest;
import id.co.bri.brimo.models.apimodel.request.cashback.CashbackRevFilterRequest;
import id.co.bri.brimo.models.apimodel.request.dompetdigitalrevamp.FastPaymentDompetDigitalRevRequest;
import id.co.bri.brimo.models.apimodel.request.dompetdigitalrevamp.PaymentDompetDigitalRevRequest;
import id.co.bri.brimo.models.apimodel.request.revampbriva.FastPayBrivaRevampRequest;
import id.co.bri.brimo.models.apimodel.request.revampbriva.PayBrivaRevampRequest;
import id.co.bri.brimo.models.apimodel.response.GeneralConfirmationResponse;
import id.co.bri.brimo.models.apimodel.response.GeneralResponse;
import id.co.bri.brimo.models.apimodel.response.ListAllCashbackFilterResponse;
import id.co.bri.brimo.models.apimodel.response.PaymentTarikResponse;
import id.co.bri.brimo.models.apimodel.response.PendingResponse;
import id.co.bri.brimo.models.apimodel.response.ReceiptRevampResponse;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.models.apimodel.response.onExceptionWH;
import id.co.bri.brimo.models.daomodel.Transaksi;
import id.co.bri.brimo.presenters.MvpPresenter;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.disposables.Disposable;
import io.reactivex.observers.DisposableSingleObserver;

public class GeneralConfirmationPresenter<V extends IMvpView & IGeneralConfirmationView> extends MvpPresenter<V> implements IGeneralConfirmationPresenter<V> {
    protected String urlPayment, urlGetListCashback;
    protected Object paymentRequest;
    protected boolean isGeneral;
    protected boolean isBriva;
    protected boolean isDompetDigital;
    protected boolean isCashback;
    protected boolean isBindingCcKki = false;
    protected long idPayment = 0;

    private Object cashbackRequest = null;
    private Disposable disposablePayment;

    public GeneralConfirmationPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, TransaksiPfmSource transaksiPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void getDataPayment(String pin, String note, GeneralConfirmationResponse generalConfirmationResponse, boolean fromfastMenu, boolean isUsingC2) {
        if (urlPayment == null) {
            return;
        }

        if (!isViewAttached()) {
            return;
        }

        if (isViewAttached()) {
            //set flag Loading
            getView().showProgress();
            String seqNum = getBRImoPrefRepository().getSeqNumber();

            if (fromfastMenu) {
                paymentRequest = new FastPaymentRequest(
                        getFastMenuRequest(),
                        generalConfirmationResponse.getReferenceNumber(),
                        pin,
                        generalConfirmationResponse.getPfmCategory().toString(),
                        note
                );
            } else {
                paymentRequest = isGeneral
                        ? new PaymentRequest(generalConfirmationResponse.getReferenceNumber(), pin, generalConfirmationResponse.getPfmCategory().toString(), note)
                        : new RencanaPaymentRequest(pin, generalConfirmationResponse.getReferenceNumber());
            }

            disposablePayment = (isUsingC2 && fromfastMenu)
                    ? generatePaymentDisposableC2(pin, note, generalConfirmationResponse, fromfastMenu, seqNum, isUsingC2)
                    : generatePaymentDisposable(generalConfirmationResponse, urlPayment, paymentRequest, seqNum);

            getCompositeDisposable().add(disposablePayment);
        }
    }


    public Disposable generatePaymentDisposable(GeneralConfirmationResponse generalConfirmationResponse, String urlPayment, Object paymentRequest, String seqNum) {
        Disposable disposable = getApiSource().getData(urlPayment, paymentRequest, seqNum)//function(param)
                .subscribeOn(getSchedulerProvider().io())
                .observeOn(getSchedulerProvider().mainThread())
                .subscribeWith(new ApiObserverKonfirmasi(getView(), seqNum) {

                    @Override
                    protected void onFailureHttp(String errorMessage) {
                        getView().onException(errorMessage);
                    }

                    @Override
                    protected void onApiCallSuccess(RestResponse response) {
                        getView().hideProgress();

                        //jika response data adalah response tarik tunai
                        try {
                            PendingResponse brivaResponse = response.getData(PendingResponse.class);
                            if (isGeneral) {
                                if (brivaResponse.getImmediatelyFlag())
                                    onSaveTransaksiPfm(generateTransaksiModel(
                                            generalConfirmationResponse.getPfmCategory(),
                                            generalConfirmationResponse.getPayAmount(),
                                            generalConfirmationResponse.getReferenceNumber(),
                                            generalConfirmationResponse.getPfmDescription())
                                    );
                            }
                            getView().onSuccessGetPayment(brivaResponse);
                        } catch (Exception e) {
                            onSaveTransaksiPfm(generateTransaksiModel(
                                    generalConfirmationResponse.getPfmCategory(),
                                    generalConfirmationResponse.getPayAmount(),
                                    generalConfirmationResponse.getReferenceNumber(),
                                    "Tarik Tunai")
                            );

                            PaymentTarikResponse responseTarik = response.getData(PaymentTarikResponse.class);
                            getView().onSuccesGetTarikTunai(responseTarik);
                        }

                    }

                    @Override
                    protected void onApiCallError(RestResponse restResponse) {
                        getView().hideProgress();
                        if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                            getView().onSessionEnd(restResponse.getDesc());
                        else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.getValue()))
                            getView().onExceptionTrxExpired(restResponse.getDesc());
                        else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_01.getValue()))
                            getView().onException01(restResponse.getDesc());
                        else if (restResponse.getCode().equals(RestResponse.ResponseCodeEnum.RC_99.getValue())) {
                            getView().onException99(restResponse.getDesc());
                        } else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_02.getValue())) {
                            onExceptionWH exception02 = restResponse.getData(onExceptionWH.class);
                            getView().onExceptionWH(exception02);
                        } else if (restResponse.getCode().equals(Constant.RE_LIMIT_EXCEED)) {
                            getView().onExceptionLimitExceed(restResponse.getData(GeneralResponse.class));
                        } else
                            getView().onException(restResponse.getDesc());
                    }
                });
        return disposable;
    }

    public Disposable generatePaymentDisposableC2(String pin, String note, GeneralConfirmationResponse generalConfirmationResponse, boolean fromfastMenu, String seqNum, boolean isUsingC2) {
        Disposable disposable = getApiSource().getDataC2(urlPayment, paymentRequest, seqNum)//function(param)
                .subscribeOn(getSchedulerProvider().io())
                .observeOn(getSchedulerProvider().mainThread())
                .subscribeWith(new ApiObserverKonfirmasi(getView(), seqNum) {

                    @Override
                    protected void onFailureHttp(String errorMessage) {
                        if (errorMessage.equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_DISABLE_SINGALARITY.getValue())) {
                            //update flag DKL C2
                            getBRImoPrefRepository().saveDklC2(false);
                            //retry hit form
                            getDataPayment(pin, note, generalConfirmationResponse, fromfastMenu, isUsingC2);
                        } else if (errorMessage.equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_ENABLE_SINGALARITY.getValue())) {
                            //update flag DKL C2
                            getBRImoPrefRepository().saveDklC2(true);
                            //retry hit form
                            getDataPayment(pin, note, generalConfirmationResponse, fromfastMenu, isUsingC2);
                        } else {
                            getView().onException(errorMessage);
                        }
                    }

                    @Override
                    protected void onApiCallSuccess(RestResponse response) {
                        getView().hideProgress();

                        //jika response data adalah response tarik tunai
                        try {
                            PendingResponse brivaResponse = response.getData(PendingResponse.class);
                            if (isGeneral) {
                                if (brivaResponse.getImmediatelyFlag())
                                    onSaveTransaksiPfm(generateTransaksiModel(
                                            generalConfirmationResponse.getPfmCategory(),
                                            generalConfirmationResponse.getPayAmount(),
                                            generalConfirmationResponse.getReferenceNumber(),
                                            generalConfirmationResponse.getPfmDescription())
                                    );
                            }
                            getView().onSuccessGetPayment(brivaResponse);
                        } catch (Exception e) {
                            onSaveTransaksiPfm(generateTransaksiModel(
                                    generalConfirmationResponse.getPfmCategory(),
                                    generalConfirmationResponse.getPayAmount(),
                                    generalConfirmationResponse.getReferenceNumber(),
                                    "Tarik Tunai")
                            );

                            PaymentTarikResponse responseTarik = response.getData(PaymentTarikResponse.class);
                            getView().onSuccesGetTarikTunai(responseTarik);
                        }

                    }

                    @Override
                    protected void onApiCallError(RestResponse restResponse) {
                        getView().hideProgress();
                        if (restResponse.getCode().equalsIgnoreCase("93"))
                            getView().onExceptionTrxExpired(restResponse.getDesc());
                        else if (restResponse.getCode().equalsIgnoreCase("01"))
                            getView().onException01(restResponse.getDesc());
                        else
                            getView().onException(restResponse.getDesc());
                    }
                });
        return disposable;
    }

    @Override
    public void getDataPaymentRevamp(String pin, GeneralConfirmationResponse generalConfirmationResponse, boolean fromfastMenu, boolean isUsingC2) {
        if (urlPayment == null) {
            return;
        }

        if (!isViewAttached()) {
            return;
        }

        if (isViewAttached()) {
            getView().showProgress();
            String seqNum = getBRImoPrefRepository().getSeqNumber();

            if (isBriva) {
                if (fromfastMenu)
                    paymentRequest = new FastPayBrivaRevampRequest(getFastMenuRequest(),
                            generalConfirmationResponse.getReferenceNumber(),
                            generalConfirmationResponse.getSourceAccountDataView().getDescription().replace(" ", ""),
                            String.valueOf(generalConfirmationResponse.getAmount()),
                            generalConfirmationResponse.getSaveAs() != null ? generalConfirmationResponse.getSaveAs() : "",
                            generalConfirmationResponse.getNote() != null ? generalConfirmationResponse.getNote() : "",
                            pin,
                            generalConfirmationResponse.getPfmCategory() != null ? generalConfirmationResponse.getPfmCategory().toString() : "");
                else
                    paymentRequest = new PayBrivaRevampRequest(generalConfirmationResponse.getReferenceNumber(),
                            generalConfirmationResponse.getSourceAccountDataView().getDescription().replace(" ", ""),
                            String.valueOf(generalConfirmationResponse.getAmount()),
                            generalConfirmationResponse.getSaveAs() != null ? generalConfirmationResponse.getSaveAs() : "",
                            generalConfirmationResponse.getNote() != null ? generalConfirmationResponse.getNote() : "",
                            pin,
                            generalConfirmationResponse.getPfmCategory() != null ? generalConfirmationResponse.getPfmCategory().toString() : "");
            } else if (isDompetDigital) {
                if (fromfastMenu)
                    paymentRequest = new FastPaymentDompetDigitalRevRequest(getFastMenuRequest(),
                            generalConfirmationResponse.getReferenceNumber(),
                            generalConfirmationResponse.getPfmCategory().toString(), pin, "");
                else
                    paymentRequest = new PaymentDompetDigitalRevRequest(generalConfirmationResponse.getReferenceNumber(),
                            generalConfirmationResponse.getPfmCategory().toString(), pin, "");
            } else if (fromfastMenu)
                paymentRequest = new FastPaymentRevampRequest(getFastMenuRequest(), generalConfirmationResponse.getReferenceNumber(), pin, generalConfirmationResponse.getPfmCategory().toString());
            else if (!isGeneral)
                paymentRequest = new RencanaPaymentRequest(pin, generalConfirmationResponse.getReferenceNumber());
            else
                paymentRequest = new PaymentRevampRequest(generalConfirmationResponse.getReferenceNumber(), pin, generalConfirmationResponse.getPfmCategory().toString());


            if (fromfastMenu && isUsingC2)
                disposablePayment = generateRevampPaymentDisposableC2(generalConfirmationResponse, urlPayment, paymentRequest, seqNum);
            else
                disposablePayment = generateRevampPaymentDisposable(generalConfirmationResponse, urlPayment, paymentRequest, seqNum);

            getCompositeDisposable().add(disposablePayment);
        }
    }

    public Disposable generateRevampPaymentDisposableC2(GeneralConfirmationResponse generalConfirmationResponse, String urlPayment, Object paymentRequest, String seqNum) {
        Disposable disposable = getApiSource().getDataC2(urlPayment, paymentRequest, seqNum)//function(param)
                .subscribeOn(getSchedulerProvider().io())
                .observeOn(getSchedulerProvider().mainThread())
                .subscribeWith(new ApiObserverKonfirmasi(getView(), seqNum) {

                    @Override
                    protected void onFailureHttp(String errorMessage) {
                        getView().onException(errorMessage);
                    }

                    @Override
                    protected void onApiCallSuccess(RestResponse response) {
                        getView().hideProgress();

                        //jika response data adalah response tarik tunai
                        try {
                            ReceiptRevampResponse receiptRevampResponse = response.getData(ReceiptRevampResponse.class);
                            if (isGeneral) {
                                if (receiptRevampResponse.getImmediatelyFlag())
                                    onSaveTransaksiPfm(generateTransaksiModel(
                                            generalConfirmationResponse.getPfmCategory(),
                                            generalConfirmationResponse.getPayAmount(),
                                            generalConfirmationResponse.getReferenceNumber(),
                                            generalConfirmationResponse.getPfmDescription())
                                    );
                            }
                            getView().onSuccessGetPaymentRevamp(receiptRevampResponse);
                        } catch (Exception e) {
                            onSaveTransaksiPfm(generateTransaksiModel(
                                    generalConfirmationResponse.getPfmCategory(),
                                    generalConfirmationResponse.getPayAmount(),
                                    generalConfirmationResponse.getReferenceNumber(),
                                    "Tarik Tunai")
                            );

                            PaymentTarikResponse responseTarik = response.getData(PaymentTarikResponse.class);
                            getView().onSuccesGetTarikTunai(responseTarik);
                        }

                    }

                    @Override
                    protected void onApiCallError(RestResponse restResponse) {
                        getView().hideProgress();
                        if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.getValue()))
                            getView().onExceptionTrxExpired(restResponse.getDesc());
                        else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_01.getValue()))
                            getView().onException01(restResponse.getDesc());
                        else if (restResponse.getCode().equals(RestResponse.ResponseCodeEnum.RC_LIMIT_EXCEEDED.getValue()))
                            getView().onExceptionLimitExceed(restResponse.getData(GeneralResponse.class));
                        else
                            getView().onException(restResponse.getDesc());
                    }
                });
        return disposable;
    }

    public Disposable generateRevampPaymentDisposable(GeneralConfirmationResponse generalConfirmationResponse, String urlPayment, Object paymentRequest, String seqNum) {
        Disposable disposable = getApiSource().getData(urlPayment, paymentRequest, seqNum)//function(param)
                .subscribeOn(getSchedulerProvider().io())
                .observeOn(getSchedulerProvider().mainThread())
                .subscribeWith(new ApiObserverKonfirmasi(getView(), seqNum) {

                    @Override
                    protected void onFailureHttp(String errorMessage) {
                        getView().onException(errorMessage);
                    }

                    @Override
                    protected void onApiCallSuccess(RestResponse response) {
                        getView().hideProgress();

                        //jika response data adalah response tarik tunai
                        try {
                            ReceiptRevampResponse receiptRevampResponse = response.getData(ReceiptRevampResponse.class);
                            if (isGeneral) {
                                if (!isBindingCcKki)
                                    if (receiptRevampResponse.getImmediatelyFlag() && generalConfirmationResponse.getPfmCategory() != null)
                                        onSaveTransaksiPfm(generateTransaksiModel(
                                                generalConfirmationResponse.getPfmCategory(),
                                                generalConfirmationResponse.getPayAmount(),
                                                generalConfirmationResponse.getReferenceNumber(),
                                                generalConfirmationResponse.getPfmDescription())
                                        );
                            }
                            getView().onSuccessGetPaymentRevamp(receiptRevampResponse);
                        } catch (Exception e) {
                            if (generalConfirmationResponse.getPfmCategory() != null) {
                                onSaveTransaksiPfm(generateTransaksiModel(
                                        generalConfirmationResponse.getPfmCategory(),
                                        generalConfirmationResponse.getPayAmount(),
                                        generalConfirmationResponse.getReferenceNumber(),
                                        "Tarik Tunai")
                                );
                            }

                            PaymentTarikResponse responseTarik = response.getData(PaymentTarikResponse.class);
                            getView().onSuccesGetTarikTunai(responseTarik);
                        }

                    }

                    @Override
                    protected void onApiCallError(RestResponse restResponse) {
                        getView().hideProgress();
                        if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.getValue()))
                            getView().onExceptionTrxExpired(restResponse.getDesc());
                        else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_01.getValue()))
                            getView().onException01(restResponse.getDesc());
                        else if (restResponse.getCode().equals(RestResponse.ResponseCodeEnum.RC_LIMIT_EXCEEDED.getValue()))
                            getView().onExceptionLimitExceed(restResponse.getData(GeneralResponse.class));
                        else if (restResponse.getCode().equals("MR"))
                            getView().onExceptionRevamp("");
                        else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_02.getValue())) {
                            onExceptionWH exception02 = restResponse.getData(onExceptionWH.class);
                            getView().onExceptionWH(exception02);
                        } else
                            getView().onException(restResponse.getDesc());
                    }
                });
        return disposable;
    }

    @Override
    public void getDataPaymentRevampListrik(String pin, GeneralConfirmationResponse generalConfirmationResponse, boolean fromfastMenu) {
        if (urlPayment == null) {
            return;
        }

        if (!isViewAttached()) {
            return;
        }

        if (isViewAttached()) {
            //set flag Loading

            getView().showProgress();
            String seqNum = getBRImoPrefRepository().getSeqNumber();


            paymentRequest = new PaymentAltRequest(generalConfirmationResponse.getReferenceNumber(), "", pin, generalConfirmationResponse.getPfmCategory().toString(), "", "");

            Disposable disposable = getApiSource().getData(urlPayment, paymentRequest, seqNum)//function(param)
                    .subscribeOn(getSchedulerProvider().io())
                    .observeOn(getSchedulerProvider().mainThread())
                    .subscribeWith(new ApiObserverKonfirmasi(getView(), seqNum) {

                        @Override
                        protected void onFailureHttp(String errorMessage) {
                            getView().onException(errorMessage);
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            getView().hideProgress();

                            //jika response data adalah response tarik tunai
                            try {
                                ReceiptRevampResponse receiptRevampResponse = response.getData(ReceiptRevampResponse.class);
                                if (isGeneral) {
                                    if (receiptRevampResponse.getImmediatelyFlag())
                                        onSaveTransaksiPfm(generateTransaksiModel(
                                                generalConfirmationResponse.getPfmCategory(),
                                                generalConfirmationResponse.getPayAmount(),
                                                generalConfirmationResponse.getReferenceNumber(),
                                                generalConfirmationResponse.getPfmDescription())
                                        );
                                }
                                getView().onSuccessGetPaymentRevamp(receiptRevampResponse);
                            } catch (Exception e) {
                                onSaveTransaksiPfm(generateTransaksiModel(
                                        generalConfirmationResponse.getPfmCategory(),
                                        generalConfirmationResponse.getPayAmount(),
                                        generalConfirmationResponse.getReferenceNumber(),
                                        "Tarik Tunai")
                                );

                                PaymentTarikResponse responseTarik = response.getData(PaymentTarikResponse.class);
                                getView().onSuccesGetTarikTunai(responseTarik);
                            }

                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            getView().hideProgress();
                            if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                getView().onSessionEnd(restResponse.getDesc());
                            else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.getValue()))
                                getView().onExceptionTrxExpired(restResponse.getDesc());
                            else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_01.getValue()))
                                getView().onException01(restResponse.getDesc());
                            else if (restResponse.getCode().equals(RestResponse.ResponseCodeEnum.RC_99.getValue())) {
                                getView().onException99(restResponse.getDesc());
                            } else
                                getView().onException(restResponse.getDesc());
                        }
                    });

            getCompositeDisposable().add(disposable);
        }
    }

    @Override
    public void getDataPaymentRevampTracking(String pin, GeneralConfirmationResponse response, boolean fromFast) {
        if (urlPayment == null) {
            return;
        }

        if (!isViewAttached()) {
            return;
        }

        if (isViewAttached()) {
            getView().showProgress();
            String seqNum = getBRImoPrefRepository().getSeqNumber();
            paymentRequest = new PaymentRevampRequest(response.getReferenceNumber(), pin, "");

            Disposable disposable = getApiSource().getData(urlPayment, paymentRequest, seqNum)
                    .subscribeOn(getSchedulerProvider().io())
                    .observeOn(getSchedulerProvider().mainThread())
                    .subscribeWith(new ApiObserverKonfirmasi(getView(), seqNum) {

                        @Override
                        protected void onFailureHttp(String errorMessage) {
                            getView().hideProgress();
                            getView().onException(errorMessage);
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            getView().hideProgress();

                            try {
                                ReceiptRevampResponse receiptRevampResponse = response.getData(ReceiptRevampResponse.class);
                                getView().onSuccessGetPaymentRevampTracking(receiptRevampResponse);
                            } catch (Exception e) {
                            }

                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            getView().hideProgress();
                            if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.getValue()))
                                getView().onExceptionTrxExpired(restResponse.getDesc());
                            else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_01.getValue()))
                                getView().onException01(restResponse.getDesc());
                            else if (restResponse.getCode().equals(RestResponse.ResponseCodeEnum.RC_LIMIT_EXCEEDED.getValue()))
                                getView().onExceptionLimitExceed(restResponse.getData(GeneralResponse.class));
                            else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_02.getValue())) {
                                onExceptionWH exception02 = restResponse.getData(onExceptionWH.class);
                                getView().onExceptionWH(exception02);
                            } else
                                getView().onException(restResponse.getDesc());
                        }
                    });

            getCompositeDisposable().add(disposable);
        }
    }


    @Override
    public void setUrlPayment(String urlPayment) {
        this.urlPayment = urlPayment;
    }

    @Override
    public void isGeneral(boolean general) {
        isGeneral = general;
    }

    @Override
    public void isCashback(boolean cashback) {
        isCashback = cashback;
    }

    @Override
    public void setIdPayment(long idPayment) {
        this.idPayment = idPayment;
    }

    @Override
    public Transaksi generateTransaksiModel(int kategoriId, long amount, String referenceNumber, String billingName) {
        Transaksi transaksi = null;
        try {
            transaksi = new Transaksi(
                    (long) kategoriId,
                    1,
                    billingName,
                    "",
                    DbConfig.TRX_OUT,
                    getBRImoPrefRepository().getUser(),
                    (long) amount,
                    CalendarHelper.getCurrentDate(),
                    CalendarHelper.getCurrentTime(),
                    Long.valueOf(referenceNumber),
                    idPayment
            );
        } catch (Exception e) {
            /*
            if(BuildConfig.DEBUG)
            e.printStackTrace();
             */
        }

        return transaksi;
    }

    @Override
    public void onSaveTransaksiPfm(Transaksi transaksi) {
        if (transaksi != null) {
            getCompositeDisposable().add(getTransaksiPfmSource()
                    .saveTransaksiPfm(transaksi)
                    .subscribeOn(getSchedulerProvider().io())
                    .observeOn(getSchedulerProvider().mainThread())
                    .subscribeWith(new DisposableSingleObserver<Long>() {
                        @Override
                        public void onSuccess(Long aLong) {

                        }

                        @Override
                        public void onError(Throwable e) {
                            // do nothing
                        }
                    })
            );
        }
    }

    @Override
    public void isBriva(boolean mIsFromBriva) {
        isBriva = mIsFromBriva;
    }


    @Override
    public void setEnableBindingCcKKi(boolean mIsBindingCcKki) {
        isBindingCcKki = mIsBindingCcKki;
    }

    @Override
    public void setUrlGetCashback(String url) {
        this.urlGetListCashback = url;
    }

    @Override
    public void getCashbackAll(CashbackRequest cashbackRequest) {
        if (urlGetListCashback == null || !isViewAttached()) {
            return;
        }
        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        getCompositeDisposable().add(
                getApiSource().getData(urlGetListCashback, cashbackRequest, seqNum)
                        .subscribeOn(getSchedulerProvider().single())
                        .observeOn(getSchedulerProvider().mainThread())
                        .subscribeWith(new ApiObserver(getView(), seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                getView().hideProgress();
                                if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SUCCESS.getValue())) {
                                    ListAllCashbackFilterResponse responseData = response.getData(ListAllCashbackFilterResponse.class);
                                    getView().onSuccessGetCashback(responseData);
                                } else if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_01.getValue())) {
                                    getView().onCashbackBlank(response.getDesc());
                                }
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                    getView().onSessionEnd(restResponse.getDesc());
                                else
                                    getView().onException(restResponse.getDesc());
                            }
                        })
        );
    }

    @Override
    public void getCashbackAllRevamp(String accountNumber, String referenceNumber, boolean isFromFastMenu) {
        if (urlGetListCashback == null || !isViewAttached()) {
            return;
        }

        if (isFromFastMenu) {
            cashbackRequest = new CashbackRevFilterFMRequest(getFastMenuRequest(), referenceNumber, accountNumber);
        } else {
            cashbackRequest = new CashbackRevFilterRequest(accountNumber, referenceNumber);
        }

        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        getCompositeDisposable().add(
                getApiSource().getData(urlGetListCashback, cashbackRequest, seqNum)
                        .subscribeOn(getSchedulerProvider().single())
                        .observeOn(getSchedulerProvider().mainThread())
                        .subscribeWith(new ApiObserver(getView(), seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                getView().hideProgress();
                                if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SUCCESS.getValue())) {
                                    ListAllCashbackFilterResponse responseData = response.getData(ListAllCashbackFilterResponse.class);
                                    getView().onSuccessGetCashback(responseData);
                                } else if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_01.getValue())) {
                                    getView().onCashbackBlank(response.getDesc());
                                }
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                    getView().onSessionEnd(restResponse.getDesc());
                                else
                                    getView().onException(restResponse.getDesc());
                            }
                        })
        );
    }

    @Override
    public void getDefaultSaldo() {
        double saldo = 0.0;
        String saldoText = getBRImoPrefRepository().getSaldoRekeningUtama();

        if (!saldoText.equals("")) {
            saldo = Double.valueOf(saldoText);
        }

        String defaultAcc = getBRImoPrefRepository().getAccountDefault();
        String saldoString = getBRImoPrefRepository().getSaldoRekeningUtamaString();
        boolean saldoHold = getBRImoPrefRepository().getSaldoHold();

        getView().setDefaultSaldo(saldo, saldoString, defaultAcc, saldoHold);
    }


    @Override
    public void isDompetDigital(boolean mIsFromDompetDigital) {
        isDompetDigital = mIsFromDompetDigital;
    }

    @Override
    public void start() {
        super.start();
        setDisablePopup(true);
    }

    @Override
    public void stop() {
        super.stop();
    }
}