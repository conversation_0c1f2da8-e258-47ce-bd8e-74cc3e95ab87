package id.co.bri.brimo.presenters.splitbill

import id.co.bri.brimo.contract.IPresenter.splitbill.ISplitBillDetailPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.splitbill.ISplitBillDetailView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.config.AppConfig
import id.co.bri.brimo.domain.extension.isNumeric
import id.co.bri.brimo.domain.helpers.LifestyleHelper
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.splitbill.MarkPaymentStatusRequest
import id.co.bri.brimo.models.apimodel.request.splitbill.SplitBillItemRequest
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.splitbill.GenerateBillResponse
import id.co.bri.brimo.models.apimodel.response.splitbill.MarkPaymentStatusResponse
import id.co.bri.brimo.models.apimodel.response.splitbill.TempBillViewModel
import id.co.bri.brimo.models.splitbill.SplitBillAddMemberItemViewModel
import id.co.bri.brimo.models.splitbill.SplitBillEditFormItemViewModel
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.disposables.Disposable
import io.reactivex.schedulers.Schedulers
import java.util.concurrent.TimeUnit

class SplitBillDetailPresenter<V>(
    schedulerProvider: SchedulerProvider?,
    compositeDisposable: CompositeDisposable?,
    mBRImoPrefRepository: BRImoPrefSource?,
    apiSource: ApiSource?,
    transaksiPfmSource: TransaksiPfmSource?
) :
    MvpPresenter<V>(
        schedulerProvider,
        compositeDisposable,
        mBRImoPrefRepository,
        apiSource,
        transaksiPfmSource
    ), ISplitBillDetailPresenter<V> where V : IMvpView?, V : ISplitBillDetailView? {

    private var members = mutableListOf<SplitBillAddMemberItemViewModel>()
    private val viewMembers = mutableListOf<SplitBillAddMemberItemViewModel>()

    private var mUrlMarkPayment = ""

    private var mUrlGetDetailBills = ""

    private var mBillId: Int = 0

    private var requestMarkPayment: MarkPaymentStatusRequest? = null

    override fun setUrlMarkPayment(urlMarkPayment: String) {
        mUrlMarkPayment = urlMarkPayment
    }

    override fun setUrlGetDetailBills(urlDetail: String) {
        mUrlGetDetailBills = urlDetail
    }

    override fun fetchData(generateBillResponse: GenerateBillResponse) {
        mBillId = generateBillResponse.billId

        members = (generateBillResponse.billModel.generated?.members?.map { member ->
            SplitBillAddMemberItemViewModel(
                id = member.memberId.toInt(),
                name = member.name,
                accNumber = member.desc,
                alias = member.userAlias,
                identifier = member.identifier,
                isPaid = member.isPaid,
                isNonBrimo = member.isBrimo,
                isCreator = member.isCreator,
                qrId = member.qrId,
                totalAmountPay = member.amount.toInt().toString(),
                amountToPay = member.details.amounts,
                products = member.details.items.map { product ->
                    SplitBillEditFormItemViewModel(
                        id = product.id,
                        name = product.name,
                        quantity = product.shared,
                        price = LifestyleHelper.convertStringtoNominal(product.fmtAmount).toLong()
                    )
                }
            )
        } ?: listOf()).toMutableList()

        val splitBillModel = TempBillViewModel(
            billName = generateBillResponse.billModel.generated?.header?.name.orEmpty(),
            billDate = generateBillResponse.billModel.generated?.header?.date.orEmpty(),
            paidAmount = generateBillResponse.billModel.generated?.header?.fmtPaidAmt.orEmpty(),
            totalBill = LifestyleHelper.convertStringtoNominal(
                (generateBillResponse.billModel.generated?.header?.fmtTotalAmt?: 0L).toString()).toLong(),
            membersConfirmation = members,
        )
        view?.showSplitBillData(splitBillModel, generateBillResponse.billModel.generated?.header!!)
        viewMembers.clear()
        viewMembers.addAll(members)
        view?.showMembers(models = viewMembers)
        view?.showDestinationAccount(generateBillResponse.billModel.generated?.userData!!)
    }

    override fun updateMemberPaymentStatusOne(
        memberId: Int,
        isPaidOff: Boolean,
        qrId: Int,
        mAmount: Long,
        isFromDetailMember: Boolean
    ) {
        val idxOnMembers = members.indexOfFirst { it.id == memberId }
        var idxOnViewMembers = idxOnMembers
        if (members.size != viewMembers.size) {
            idxOnViewMembers = viewMembers.indexOfFirst { it.id == memberId }
        }
        members[idxOnMembers].apply { isPaid = isPaidOff }
        viewMembers[idxOnViewMembers].apply { isPaid = isPaidOff }

        setRequest(
            billId = mBillId,
            mAmount = mAmount,
            mQrId = qrId,
            isAllDone = false,
            isPaid = isPaidOff
        )

        if (!isFromDetailMember) {
            getDataMarkPaymentStatus(false)
        }

        view?.showMembers(models = viewMembers)
    }

    override fun updateMemberPaymentStatusAll() {
        setRequest(
            billId = mBillId,
            mAmount = 0L,
            mQrId = 0,
            isAllDone = true,
            isPaid = true
        )

        getDataMarkPaymentStatus(true)
    }

    private fun setRequest(billId: Int, mAmount: Long, mQrId: Int, isAllDone: Boolean, isPaid: Boolean) {
        requestMarkPayment = if (isAllDone) {
            MarkPaymentStatusRequest(
                billId = billId,
                status = if (isPaid) "paid" else "unpaid",
                target = "all"
            )
        } else {
            MarkPaymentStatusRequest(
                billId = mBillId,
                status = if (isPaid) "paid" else "unpaid",
                target = "one",
                amount = mAmount,
                qrId = mQrId
            )
        }
    }

    override fun getDataMarkPaymentStatus(isAllDone: Boolean) {
        if (mUrlMarkPayment.isEmpty() || !isViewAttached) {
            return
        }

        if (view != null) {

            view?.showProgress()
            val seqNum = brImoPrefRepository.seqNumber
            val disposable: Disposable = apiSource.getData(mUrlMarkPayment, requestMarkPayment, seqNum)
                .subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView()?.hideProgress()
                        getView()?.onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView()?.hideProgress()

                        val markPaymentResponse = response.getData(
                            MarkPaymentStatusResponse::class.java
                        )

                        getView()?.onSuccessGetMarkPaymentStatus(markPaymentResponse, isAllDone)
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        view?.hideProgress()
                        getView()?.onException(restResponse.desc)
                    }
                })
            compositeDisposable.add(disposable)
        }
    }

    override fun getDetailBills(billId: Int) {
        if (mUrlGetDetailBills.isEmpty() || !isViewAttached) {
            return
        }

        view?.showSkeleton(true)

        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable.add(
            apiSource.getData(mUrlGetDetailBills, SplitBillItemRequest(billId), seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(Schedulers.single())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView()?.showSkeleton(false)
                        getView()?.onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView()?.showSkeleton(false)

                        val generatedBillResponse = response.getData(
                            GenerateBillResponse::class.java
                        )

                       getView()?.onSuccessGetDetailBills(generatedBillResponse)
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView()?.showSkeleton(false)
                        getView()?.onException(restResponse.desc)
                    }
                })
        )
    }

    override fun doFilterRecentMembers(searchQuery: String) {
        val listViewMember = if (searchQuery.isEmpty()) {
            members
        } else {
            val keyWords = searchQuery.lowercase().trim()
            members.filter { data ->
                data.name.contains(keyWords, true)
                        || data.alias.contains(keyWords, true)
                        || (keyWords.isNumeric()
                        || data.accNumber.contains(keyWords))
            }
        }
        view?.showMembers(
            models = listViewMember,
            searchQuery = searchQuery
        )
    }

}
