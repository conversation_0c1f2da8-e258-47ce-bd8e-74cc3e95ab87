package id.co.bri.brimo.presenters.lifestyle

import id.co.bri.brimo.contract.IPresenter.lifestyle.IMenuLifestylePresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.lifestyle.IMenuLifestyleView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimo.data.repository.category.CategoryPfmSource
import id.co.bri.brimo.data.repository.lifestyle.MenuLifestyleSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.config.AppConfig
import id.co.bri.brimo.domain.config.LifestyleConfig
import id.co.bri.brimo.domain.config.MenuConfig

import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.PartnerIdRequest
import id.co.bri.brimo.models.apimodel.response.CityFormResponse
import id.co.bri.brimo.models.apimodel.response.GeneralWebviewResponse
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.UrlWebViewResponse
import id.co.bri.brimo.models.apimodel.response.lifestyle.EODLifestyleResponse
import id.co.bri.brimo.models.apimodel.response.lifestyle.FeatureDataView
import id.co.bri.brimo.models.daomodel.lifestyle.MenuLifestyle
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.observers.DisposableCompletableObserver
import io.reactivex.observers.DisposableMaybeObserver
import java.util.concurrent.TimeUnit

class MenuLifestylePresenter<V>(
    schedulerProvider: SchedulerProvider?,
    compositeDisposable: CompositeDisposable?,
    mBRImoPrefRepository: BRImoPrefSource?,
    apiSource: ApiSource?,
    categoryPfmSource: CategoryPfmSource?,
    transaksiPfmSource: TransaksiPfmSource?,
    anggaranPfmSource: AnggaranPfmSource?,
    private val menuLifestyleSource: MenuLifestyleSource
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    mBRImoPrefRepository,
    apiSource,
    transaksiPfmSource
),
    IMenuLifestylePresenter<V> where V : IMvpView?, V : IMenuLifestyleView? {

    lateinit var mUrlFormBus: String
    lateinit var mUrlKai: String
    lateinit var mUrlTugu: String

    override fun setUrlBusShuttle(urlFormBus: String) {
        mUrlFormBus = urlFormBus
    }

    override fun setUrlKai(urlFormKai: String) {
        mUrlKai = urlFormKai
    }

    override fun setUrlWebviewTugu(urlTugu: String) {
        mUrlTugu = urlTugu
    }

    override fun getMenuLifestyle() {
        if (isViewAttached) {
            compositeDisposable.add(menuLifestyleSource.getAllMenuLifestyle()
                .subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : DisposableMaybeObserver<List<MenuLifestyle>>() {
                    override fun onSuccess(menuLifestyleList: List<MenuLifestyle>) {
                        if (menuLifestyleList.isNotEmpty()) {
                            view?.onSuccessGetMenuLocals(menuLifestyleList)
                        }
                    }

                    override fun onError(e: Throwable) {}
                    override fun onComplete() {}
                })
            )
        }
    }

    override fun onUpdateFlagNewMenu(featureCode: String) {
        compositeDisposable.add(
            menuLifestyleSource.updateMenuLifestyle(featureCode, MenuConfig.NewStatus.OLD)
                .subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : DisposableCompletableObserver() {
                    override fun onComplete() {
                        getMenuLifestyle()
                    }

                    override fun onError(e: Throwable) {
                        // do nothing
                    }
                })
        )
    }

    override fun getFormBus() {
        if (!isViewAttached)
            return

        view!!.showProgress()
        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable.add(
            apiSource.getDataForm(mUrlFormBus, seqNum)
                .subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView()!!.hideProgress()
                        getView()!!.onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView()!!.hideProgress()
                        val cityFormResponse = response.getData(CityFormResponse::class.java)
                        getView()!!.onSuccessGetFormBus(cityFormResponse)
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView()!!.hideProgress()
                        if (restResponse.code.equals(
                                RestResponse.ResponseCodeEnum.RC_SESSION_END.value,
                                ignoreCase = true
                            )
                        ) getView()!!.onSessionEnd(restResponse.desc)
                        else if (restResponse.code.equals(
                                RestResponse.ResponseCodeEnum.RC_99.value,
                                ignoreCase = true
                            )
                        ) getView()!!.onException99(restResponse.desc) else getView()!!.onException(
                            restResponse.desc
                        )
                    }
                })
        )
    }

    override fun getFormKai(titleBar: String) {
        if (!isViewAttached)
            return

        view!!.showProgress()
        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable.add(
            apiSource.getDataTanpaRequest(mUrlKai, seqNum)
                .subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView()!!.hideProgress()
                        getView()!!.onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView()!!.hideProgress()
                        val urlWebViewResponse = response.getData(
                            UrlWebViewResponse::class.java
                        )
                        getView()!!.onSuccessGetFormKai(urlWebViewResponse, titleBar)
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView()!!.hideProgress()
                        if (restResponse.code.equals(
                                RestResponse.ResponseCodeEnum.RC_SESSION_END.value,
                                ignoreCase = true
                            )
                        ) getView()!!.onSessionEnd(restResponse.desc)
                        else if (restResponse.code.equals(
                                RestResponse.ResponseCodeEnum.RC_99.value,
                                ignoreCase = true
                            )
                        ) getView()!!.onException99(restResponse.desc) else getView()!!.onException(
                            restResponse.desc
                        )
                    }
                })
        )
    }

    override fun getWebViewTugu(
        partnerIdRequest: PartnerIdRequest?,
        titleBar: String,
        codeMenu: String
    ) {
        if (!isViewAttached) {
            return
        }

        view!!.showProgress()
        val seqNum = brImoPrefRepository.seqNumber
        compositeDisposable.add(
            apiSource.getData(mUrlTugu, partnerIdRequest, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView()!!.hideProgress()
                        getView()!!.onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView()!!.hideProgress()

                        if (response.code.equals(RestResponse.ResponseCodeEnum.RC_SUCCESS.value, ignoreCase = true)) {
                            val webviewResponse = response.getData(
                                GeneralWebviewResponse::class.java
                            )

                            getView()!!.onSuccessGetWebviewTugu(
                                webviewResponse,
                                titleBar,
                                LifestyleConfig.MenuLifestyleCode.findMenu(codeMenu)
                            )
                        } else if (response.code.equals(RestResponse.ResponseCodeEnum.RC_02.value, ignoreCase = true)) {
                            val eodLifestyleResponse: EODLifestyleResponse =
                                response.getData(
                                    EODLifestyleResponse::class.java
                                )
                            getView()!!.onMenuLifestyleEOD(eodLifestyleResponse)
                        }
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView()!!.hideProgress()
                        if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value, ignoreCase = true)) {
                            getView()!!.onSessionEnd(restResponse.desc)
                        } else {
                            getView()!!.onException(restResponse.desc)
                        }
                    }
                })
        )
    }

    override fun getIndihomeRegistrationData(selectedMenu: FeatureDataView) {
        if (brImoPrefRepository.isIndihomeFirstClick) {
            getWebViewTugu(
                partnerIdRequest = PartnerIdRequest(selectedMenu.partnerId),
                titleBar = selectedMenu.featureName,
                codeMenu = selectedMenu.featureCode
            )
        } else {
            view?.showIndihomeConfirmation(selectedMenu)
        }
    }

    override fun confirmIndihomeRegistration(selectedMenu: FeatureDataView) {
        brImoPrefRepository.saveIndihomeFirstClick(true)
        getWebViewTugu(
            partnerIdRequest = PartnerIdRequest(selectedMenu.partnerId),
            titleBar = selectedMenu.featureName,
            codeMenu = selectedMenu.featureCode
        )
    }

}