package id.co.bri.brimo.presenters.bukaValas;

import android.util.Log;

import id.co.bri.brimo.contract.IPresenter.bukaValas.IPendingBukaValasPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.bukaValas.IPendingBukaValasView;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.domain.helpers.calendar.CalendarHelper;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.apimodel.request.StatusFastRequest;
import id.co.bri.brimo.models.apimodel.request.StatusRequest;
import id.co.bri.brimo.models.apimodel.response.GeneralConfirmationResponse;
import id.co.bri.brimo.models.apimodel.response.PendingResponse;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.models.daomodel.Transaksi;
import id.co.bri.brimo.presenters.MvpPresenter;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.disposables.Disposable;
import io.reactivex.observers.DisposableSingleObserver;
import io.reactivex.schedulers.Schedulers;

public class PendingBukaValasPresenter<V extends IMvpView & IPendingBukaValasView> extends MvpPresenter<V> implements IPendingBukaValasPresenter<V> {

    private static final String TAG = "PendingBukaValasPresenter";

    Transaksi newTransaksi = null;

    boolean onLoad = false;

    public PendingBukaValasPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }


    @Override
    public void getDataPaid(PendingResponse pendingResponse, String mUrl, GeneralConfirmationResponse generalConfirmationResponse) {
        if (!isViewAttached()) {
            Log.d(TAG, "getDataInquiry: view atau inquiry url null");
            return;
        }
        //initiate param with getter from view

        getView().showProgress();
        StatusRequest request = new StatusRequest(pendingResponse.getReferenceNumber());
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        Disposable disposable = getApiSource().getData(mUrl, request, seqNum)//function(param)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(new ApiObserver(getView(),seqNum) {

                    @Override
                    protected void onFailureHttp(String errorMessage) {
                        getView().hideProgress();
                        getView().onException(errorMessage);
                    }

                    @Override
                    protected void onApiCallSuccess(RestResponse response) {
                        getView().hideProgress();
                        PendingResponse pendingResponse = response.getData(PendingResponse.class);


                        if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SUCCESS.getValue())) {
                            getView().onGetData(pendingResponse);
                            onLoad = false;
                        } else if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_01.getValue())) {
                            getView().onGetTransaksiGagal(response);
                        }


                    }

                    @Override
                    protected void onApiCallError(RestResponse restResponse) {
                        onLoad = false;
                        getView().hideProgress();
                        if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                            getView().onSessionEnd(restResponse.getDesc());
                        else
                            getView().onException(restResponse.getDesc());
                    }
                });

        getCompositeDisposable().add(disposable);
    }

    @Override
    public void getDataPaidFastMenu(PendingResponse pendingResponse, String mUrl, GeneralConfirmationResponse generalConfirmationResponse) {
        if (isViewAttached()) {
            Log.d(TAG, "getDataInquiry: view atau inquiry url null");
            return;
        }
        //initiate param with getter from view

        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        StatusFastRequest request = new StatusFastRequest(pendingResponse.getReferenceNumber(), getBRImoPrefRepository().getUsername(), getBRImoPrefRepository().getTokenKey());
        Disposable disposable = getApiSource().getData(mUrl, request, seqNum)//function(param)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(new ApiObserver(getView(),seqNum) {

                    @Override
                    protected void onFailureHttp(String errorMessage) {
                        onLoad = false;
                        getView().hideProgress();
                        getView().onException(errorMessage);
                    }

                    @Override
                    protected void onApiCallSuccess(RestResponse response) {
                        getView().hideProgress();
                        PendingResponse pendingResponse = response.getData(PendingResponse.class);
                        if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SUCCESS.getValue())) {
                            getView().onGetData(pendingResponse);
                            onLoad = false;
                        } else if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_01.getValue())) {
                            getView().onGetTransaksiGagal(response);
                        }
                    }

                    @Override
                    protected void onApiCallError(RestResponse restResponse) {
                        onLoad = false;
                        getView().hideProgress();
                        if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                            getView().onSessionEnd(restResponse.getDesc());
                        else
                            getView().onException(restResponse.getDesc());
                    }
                });

        getCompositeDisposable().add(disposable);
    }

    @Override
    public void onSaveTransaksiPfm(Transaksi transaksi) {
        //create disposible
        if (transaksi != null) {
            DisposableSingleObserver disposableSingleObserver = new DisposableSingleObserver<Long>() {
                @Override
                public void onSuccess(Long id) {
                    Log.d(TAG, "onSuccess Save : " + transaksi.getRefnum());
                }

                @Override
                public void onError(Throwable e) {
                    /*
                    if(BuildConfig.DEBUG)
                    e.printStackTrace();

                     */
                }
            };

            getTransaksiPfmSource().saveTransaksiPfm(transaksi)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribe(disposableSingleObserver);

            getCompositeDisposable().add(disposableSingleObserver);
        }
    }


    public void onSaveTransaksiMasukPfm(Transaksi transaksi) {
        if (transaksi != null) {
            getCompositeDisposable().add(getTransaksiPfmSource()
                    .saveTransaksiPfm(transaksi)
                    .subscribeOn(getSchedulerProvider().io())
                    .observeOn(getSchedulerProvider().mainThread())
                    .subscribeWith(new DisposableSingleObserver<Long>() {
                        @Override
                        public void onSuccess(Long aLong) {
                        }

                        @Override
                        public void onError(Throwable e) {
                            if (!GeneralHelper.isProd()){
                                Log.d(TAG, "onError: " + e.toString());
                            }

                        }
                    })
            );
        }
    }
    @Override
    public Transaksi generateTransaksiModel(int kategoriId, long amount, String referenceNumber, String billingName,String trxType) {
        Transaksi transaksi = null;
        try {
            transaksi = new Transaksi(
                    (long) kategoriId,
                    1,
                    billingName,
                    "",
                    trxType,
                    getBRImoPrefRepository().getUser(),
                    (long) amount,
                    CalendarHelper.getCurrentDate(),
                    CalendarHelper.getCurrentTime(),
                    Long.valueOf(referenceNumber),
                    0
            );
        } catch (Exception e) {
            if (!GeneralHelper.isProd()){
                Log.d(TAG, "masuk sini");
            }
            /*
            if(BuildConfig.DEBUG)
            e.printStackTrace();
             */
        }

        return transaksi;
    }
    @Override
    public boolean onLoadGetReceipt() {
        return onLoad;
    }

    @Override
    public void stop() {
        newTransaksi = null;
        super.stop();
    }
}