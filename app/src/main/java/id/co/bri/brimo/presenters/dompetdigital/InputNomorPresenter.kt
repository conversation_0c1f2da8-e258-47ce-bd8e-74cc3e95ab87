package id.co.bri.brimo.presenters.dompetdigital

import id.co.bri.brimo.contract.IPresenter.dompetdigitalrevamp.IInputNomorPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.dompetdigitalrevamp.IInputNomorView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimo.data.repository.category.CategoryPfmSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.config.AppConfig

import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.dompetdigitalrevamp.InquiryDompetDigitalRequest
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.dompetdigitalrevamp.InquiryDompetDigitalResponse
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.disposables.CompositeDisposable
import java.util.concurrent.TimeUnit

class InputNomorPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBRImoPrefRepository: BRImoPrefSource,
    apiSource: ApiSource,
    categoryPfmSource: CategoryPfmSource,
    transaksiPfmSource: TransaksiPfmSource,
    anggaranPfmSource: AnggaranPfmSource
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    mBRImoPrefRepository,
    apiSource,
    transaksiPfmSource
), IInputNomorPresenter<V> where V : IMvpView, V : IInputNomorView {

    lateinit var mUrlInquiry: String
    lateinit var mUrlConfirm: String
    lateinit var mUrlPayment: String

    override fun setUrlInquiry(url: String) {
        mUrlInquiry = url
    }

    override fun setUrlConfirm(url: String) {
        mUrlConfirm = url
    }

    override fun setUrlPayment(url: String) {
        mUrlPayment = url
    }

    override fun getDataInquiry(eWalletCode: String, corpCode: String, purchaseNumber: String) {
        if (mUrlInquiry.isEmpty() && !isViewAttached) {
            return
        }

        view?.showProgress()

        val inquiryRequest = InquiryDompetDigitalRequest(
            eWalletCode, corpCode, purchaseNumber
        )

        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable.add(
            apiSource.getData(mUrlInquiry, inquiryRequest, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(schedulerProvider.io()).observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView()?.hideProgress()
                        getView()?.onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView()?.hideProgress()
                        val responseWallet = response.getData(
                            InquiryDompetDigitalResponse::class.java
                        )
                        if (response.data != null) {
                            getView()?.onSuccessGetInquiry(
                                responseWallet, mUrlConfirm, mUrlPayment
                            )
                        }
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView()?.hideProgress()
                        if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value, true))
                            getView()?.onSessionEnd(
                            restResponse.desc)
                        else if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_ACCOUNT_NOT_FOUND.value, true))
                            getView().onExceptionNumberNotFound(restResponse)
                        else getView()?.onException(restResponse.desc)
                    }

                })
        )
    }

}