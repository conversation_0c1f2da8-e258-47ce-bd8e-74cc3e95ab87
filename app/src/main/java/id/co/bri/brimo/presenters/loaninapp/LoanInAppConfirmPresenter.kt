package id.co.bri.brimo.presenters.loaninapp

import id.co.bri.brimo.R
import id.co.bri.brimo.contract.IPresenter.loaninapp.ILoanInAppConfirmPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.loaninapp.ILoanInAppConfirmView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimo.data.repository.category.CategoryPfmSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.applyccrevamp.ApplyCcGetOtpRequest
import id.co.bri.brimo.models.apimodel.response.applyccrevamp.GetOtpGeneralResponse
import id.co.bri.brimo.models.applyccrevamp.toGetOtpGeneralModel
import id.co.bri.brimo.presenters.MvpPresenter
import id.co.bri.brimo.util.extension.getDataWithOrWithoutRequest
import io.reactivex.disposables.CompositeDisposable

class LoanInAppConfirmPresenter<V>(
    schedulerProvider: SchedulerProvider?,
    compositeDisposable: CompositeDisposable?,
    mBRImoPrefRepository: BRImoPrefSource?,
    apiSource: ApiSource?,
    categoryPfmSource: CategoryPfmSource?,
    transaksiPfmSource: TransaksiPfmSource?,
    anggaranPfmSource: AnggaranPfmSource?,
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    mBRImoPrefRepository,
    apiSource,
    transaksiPfmSource
), ILoanInAppConfirmPresenter<V> where V : IMvpView, V : ILoanInAppConfirmView {
    override fun getOtp(cardToken: String) {
        view.getDataWithOrWithoutRequest(
            GeneralHelper.getString(R.string.url_loan_in_send_otp),
            brImoPrefRepository,
            compositeDisposable,
            apiSource,
            schedulerProvider,
            true,
            ApplyCcGetOtpRequest("", cardToken, ""),
        ) {
            val data = it.getData(GetOtpGeneralResponse::class.java).toGetOtpGeneralModel()
            view.getOtpSuccess(data)
        }
    }
}