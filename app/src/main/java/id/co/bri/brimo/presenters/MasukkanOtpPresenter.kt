package id.co.bri.brimo.presenters

import id.co.bri.brimo.contract.MasukkanOtpContract

class MasukkanOtpPresenter(private var view: MasukkanOtpContract.View) : MasukkanOtpContract.Presenter {

    override fun start() {

    }

    override fun stop() {
        TODO("not implemented") //To change body of created functions use File | Settings | File Templates.
    }


    override fun checkOtp(otp: String) {

        //Dummy
        view.onCorrectOtp()
    }



}