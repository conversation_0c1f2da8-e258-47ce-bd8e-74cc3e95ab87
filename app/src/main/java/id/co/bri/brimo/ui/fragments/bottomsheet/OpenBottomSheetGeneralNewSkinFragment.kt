package id.co.bri.brimo.ui.fragments.bottomsheet

import android.graphics.drawable.Drawable
import android.text.SpannableString
import androidx.fragment.app.FragmentManager
import id.co.bri.brimo.models.ParameterPilihKantorModel
import id.co.bri.brimo.models.apimodel.response.ListKantorResponse
import id.co.bri.brimo.models.apimodel.response.smarttransfer.Account

object OpenBottomSheetGeneralNewSkinFragment {

    @JvmStatic
    fun showDialogConfirmationWithButtons(
        fragmentManager: FragmentManager,
        imgPath: String,
        imgName: String,
        titleTxt: String,
        subTitleTxt: String,
        firstBtnTxt: String = "Coba Lagi",
        secondBtnTxt: String = "Atur Ulang Password",
        isClickableOutside: Boolean = false,
        onFirstButtonClicked: () -> Unit = {},
        onSecondButtonClicked: () -> Unit = {}
    ) {
        val bottomSheet = BottomSheetGeneralNewSkinFragment().apply {
            setFieldType(BottomSheetGeneralNewSkinFragment.DialogType.INFORMATIONNOOUTLINEBTN) // atau buat DialogType baru jika perlu
            imagePath = imgPath
            imageName = imgName
            titleText = titleTxt
            subtitleText = subTitleTxt
            firstBtnText = firstBtnTxt
            secondBtnText = secondBtnTxt
//            withBackgroundSecondBtn = false // Agar jadi tombol outline
            setOnBtnFirst {
                onFirstButtonClicked()
                dismiss()
            }
            setOnBtnSecond {
                onSecondButtonClicked()
                dismiss()
            }
            show(fragmentManager, "")
            isClickable = isClickableOutside
        }
    }


    @JvmStatic
    fun showDialogInformation(
        fragmentManager: FragmentManager,
        imgPath: String,
        imgName: String,
        titleTxt: String,
        subTitleTxt: String,
        btnFirstFunction: () -> Unit = {},
        isClickableOutside: Boolean = true,
        firstBtnTxt: String = ""
    ) {
        val bottomSheet = BottomSheetGeneralNewSkinFragment()
        bottomSheet.apply {
            setFieldType(BottomSheetGeneralNewSkinFragment.DialogType.INFORMATION)
            imagePath = imgPath
            imageName = imgName
            titleText = titleTxt
            subtitleText = subTitleTxt
            firstBtnText = firstBtnTxt
            setOnBtnFirst {
                btnFirstFunction()
                dismiss()
            }
            show(fragmentManager, "")
            isClickable = isClickableOutside
        }
    }

    @JvmStatic
    fun showDialogInformation1Button(
        fragmentManager: FragmentManager,
        imgPath: String,
        imgName: String,
        titleTxt: String,
        subTitleTxt: String,
        btnFirstFunction: () -> Unit = {},
        isClickableOutside: Boolean = true,
        firstBtnTxt: String = "",
        showCloseButton: Boolean = false,
        showPill: Boolean = true
    ) {
        val bottomSheet = BottomSheetGeneralNewSkinFragment()
        bottomSheet.apply {
            setFieldType(BottomSheetGeneralNewSkinFragment.DialogType.INFORMATION)
            imagePath = imgPath
            imageName = imgName
            titleText = titleTxt
            subtitleText = subTitleTxt
            firstBtnText = firstBtnTxt
            setOnBtnFirst {
                bottomSheet.dismiss()
                btnFirstFunction()
            }
            setOnDismiss {
                if (!requireActivity().isFinishing && isAdded) {
                    bottomSheet.dismissAllowingStateLoss()
                }
            }
            isClickable = isClickableOutside
            isCloseButtonVisible = showCloseButton
            isShowPillDecoration = showPill
            showAllowingStateLoss(fragmentManager, "")
        }
    }

    @JvmStatic
    fun showDialogInformation(
        fragmentManager: FragmentManager,
        imgPath: String,
        imgName: String,
        titleTxt: String,
        subTitleTxt: String,
        btnFirstFunction: () -> Unit = {},
        isClickableOutside: Boolean = true,
        firstBtnTxt: String = "",
        showCloseButton: Boolean = false,
        showPill: Boolean = true,
        onDismiss: () -> Unit = {},
        tag: String = ""
    ) {
        val bottomSheet = BottomSheetGeneralNewSkinFragment()
        bottomSheet.apply {
            setFieldType(BottomSheetGeneralNewSkinFragment.DialogType.INFORMATION)
            imagePath = imgPath
            imageName = imgName
            titleText = titleTxt
            subtitleText = subTitleTxt
            firstBtnText = firstBtnTxt
            setOnBtnFirst {
                btnFirstFunction()
                dismiss()
            }
            show(fragmentManager, tag)
            isClickable = isClickableOutside
            isCloseButtonVisible = showCloseButton
            isShowPillDecoration = showPill
        }
    }

    @JvmStatic
    fun showDialogInformationDismiss(
        fragmentManager: FragmentManager,
        imgPath: String,
        imgName: String,
        titleTxt: String,
        subTitleTxt: String,
        btnFirstFunction: () -> Unit = {},
        isClickableOutside: Boolean = true,
        firstBtnTxt: String = "",
        showCloseButton: Boolean = false,
        showPill: Boolean = true,
    ) {
        val bottomSheet = BottomSheetGeneralNewSkinFragment()
        bottomSheet.apply {
            setFieldType(BottomSheetGeneralNewSkinFragment.DialogType.INFORMATION)
            imagePath = imgPath
            imageName = imgName
            titleText = titleTxt
            subtitleText = subTitleTxt
            firstBtnText = firstBtnTxt
            setOnBtnFirst {
                bottomSheet.dismiss()
                btnFirstFunction()
            }
            setOnDismiss {
                if (!requireActivity().isFinishing && isAdded) {
                    bottomSheet.dismissAllowingStateLoss()
                }
            }
            isClickable = isClickableOutside
            isCloseButtonVisible = showCloseButton
            isShowPillDecoration = showPill
            show(fragmentManager, "")
        }
    }

    @JvmStatic
    fun showDialogConnectivity(
        fragmentManager: FragmentManager,
        imgPath: String,
        imgName: String,
        titleTxt: String,
        subTitleTxt: String,
        btnFirstFunction: () -> Unit = {},
        isClickableOutside: Boolean = true,
        firstBtnTxt: String = "",
        showCloseButton: Boolean = false
    ): BottomSheetGeneralNewSkinFragment {
        val bottomSheet = BottomSheetGeneralNewSkinFragment().apply {
            setFieldType(BottomSheetGeneralNewSkinFragment.DialogType.INFORMATION)
            imagePath = imgPath
            imageName = imgName
            titleText = titleTxt
            subtitleText = subTitleTxt
            firstBtnText = firstBtnTxt
            setOnBtnFirst {
                btnFirstFunction()
                dismiss()
            }
            isClickable = isClickableOutside
            isCloseButtonVisible = showCloseButton
        }

        bottomSheet.show(fragmentManager, "InformationBottomSheet")
        return bottomSheet
    }

    @JvmStatic
    fun showDialogInformationNfcPayment(
        fragmentManager: FragmentManager,
        imgPath: String,
        imgName: String,
        titleTopTxt: String,
        titleTopDsc: String,
        btnFirstFunction: () -> Unit = {},
        onBtnSecondClicked: () -> Unit = {},
        isClickableOutside: Boolean = true,
        firstBtnTxt: String = "",
        secondBtnTxt: String = "",
        isSecondBtnBordered: Boolean = false,
    ) {
        if (fragmentManager.isDestroyed) return

        val bottomSheet = BottomSheetGeneralNewSkinFragment()
        bottomSheet.apply {
            setFieldType(BottomSheetGeneralNewSkinFragment.DialogType.ACTIVEINACTIVENFC)
            imagePath = imgPath
            imageName = imgName
            titleTopText = titleTopTxt
            titleTopDesc = titleTopDsc
            firstBtnText = firstBtnTxt
            secondBtnText = secondBtnTxt
            setBtnSecondBorder(isSecondBtnBordered)

            setOnBtnFirst {
                btnFirstFunction.invoke()
                dismiss()
            }
            setOnBtnSecond {
                onBtnSecondClicked.invoke()
                dismiss()
            }
            if (!fragmentManager.isDestroyed) {
                show(fragmentManager, "")
            }
            isClickable = isClickableOutside
        }
    }

    @JvmStatic
    fun showDialogInformationWithOutImage(
        fragmentManager: FragmentManager,
        imgPath: String,
        imgName: String,
        titleTxt: String,
        subTitleTxt: String,
        btnFirstFunction: () -> Unit = {},
        isClickableOutside: Boolean = true,
        firstBtnTxt: String = ""
    ) {
        val bottomSheet = BottomSheetGeneralNewSkinFragment()
        bottomSheet.apply {
            setFieldType(BottomSheetGeneralNewSkinFragment.DialogType.INFORMATIONWITHOUTIMAGE)
            imagePath = imgPath
            imageName = imgName
            titleText = titleTxt
            subtitleText = subTitleTxt
            firstBtnText = firstBtnTxt
            setOnBtnFirst {
                dismiss()
            }
            setOnDismiss {
                btnFirstFunction()
            }
            show(fragmentManager, "")
            isClickable = isClickableOutside
        }
    }

    @JvmStatic
    fun showDialogInformationWithAction(
        fragmentManager: FragmentManager,
        imgPath: String,
        imgName: String,
        titleTxt: String,
        subTitleTxt: String,
        btnFirstFunction: () -> Unit = {},
        btnThirdFunction: () -> Unit = {},
        isClickableOutside: Boolean = true,
        showCloseButton: Boolean,
        firstBtnTxt: String = "",
        thirdBtnTxt: String = "",
    ) {
        val bottomSheet = BottomSheetGeneralNewSkinFragment()
        bottomSheet.apply {
            setFieldType(BottomSheetGeneralNewSkinFragment.DialogType.INFORMATIONNOOUTLINEBTN)
            imagePath = imgPath
            imageName = imgName
            titleText = titleTxt
            subtitleText = subTitleTxt
            firstBtnText = firstBtnTxt
            thirdBtnText = thirdBtnTxt
            isCloseButtonVisible = showCloseButton
            setOnBtnFirst {
                btnFirstFunction()
                bottomSheet.dismiss()
            }
            setOnBtnSecond {
                btnThirdFunction()
                dismiss()
            }
            setOnDismiss {
                btnThirdFunction()
            }
            show(fragmentManager, "")
            isClickable = isClickableOutside
        }
    }

    @JvmStatic
    fun showDialogInformation(
        fragmentManager: FragmentManager,
        imgPath: String,
        imgName: String,
        titleTxt: String,
        subTitleTxt: String,
        btnFirstFunction: () -> Unit = {},
        btnSecondFunction: () -> Unit = {},
        isClickableOutside: Boolean = true,
        firstBtnTxt: String = "",
        thirdBtnTxt: String = "",
    ) {
        val bottomSheet = BottomSheetGeneralNewSkinFragment()
        bottomSheet.apply {
            setFieldType(BottomSheetGeneralNewSkinFragment.DialogType.INFORMATIONNOOUTLINEBTN)
            imagePath = imgPath
            imageName = imgName
            titleText = titleTxt
            subtitleText = subTitleTxt
            firstBtnText = firstBtnTxt
            thirdBtnText = thirdBtnTxt
            setOnBtnFirst {
                btnFirstFunction()
                dismiss()
            }
            setOnBtnSecond {
                btnSecondFunction()
                dismiss()
            }
            show(fragmentManager, "")
            isClickable = isClickableOutside
        }
    }

    @JvmStatic
    fun showDialogConfirmation(
        fragmentManager: FragmentManager,
        imgDrawable: Int,
        imgName: String,
        titleTxt: String,
        subTitleTxt: String,
        btnFirstFunction: () -> Unit = {},
        btnSecondFunction: () -> Unit = {},
        isClickableOutside: Boolean = true,
        firstBtnTxt: String = "",
        secondBtnTxt: String = "",
        withBgSecondBtn: Boolean = true,
        showCloseButton: Boolean = false,
        showPill: Boolean = true,
        ) {
        val bottomSheet = BottomSheetGeneralNewSkinFragment()
        bottomSheet.apply {
            setFieldType(BottomSheetGeneralNewSkinFragment.DialogType.CONFIRMATION)
            imageDrawable = imgDrawable
            imageName = imgName
            titleText = titleTxt
            subtitleText = subTitleTxt
            firstBtnText = firstBtnTxt
            secondBtnText = secondBtnTxt
            setOnBtnFirst {
                btnFirstFunction()
                dismiss()
            }
            setOnBtnSecond {
                btnSecondFunction()
                dismiss()
            }
            isClickable = isClickableOutside
            withBackgroundSecondBtn = withBgSecondBtn
            isCloseButtonVisible = showCloseButton
            isShowPillDecoration = showPill
            show(fragmentManager, "")
        }
    }

    @JvmStatic
    fun showDialogConfirmationWithStringFormat(
        fragmentManager: FragmentManager,
        imgDrawable: Int,
        imgName: String,
        titleTxt: String,
        subTitleTxt: SpannableString,
        btnFirstFunction: () -> Unit = {},
        btnSecondFunction: () -> Unit = {},
        isClickableOutside: Boolean = true,
        firstBtnTxt: String = "",
        secondBtnTxt: String = "",
        withBgSecondBtn: Boolean = true,
        showCloseButton: Boolean = false,
        showPill: Boolean = true,
        ) {
        val bottomSheet = BottomSheetGeneralNewSkinFragment()
        bottomSheet.apply {
            setFieldType(BottomSheetGeneralNewSkinFragment.DialogType.CONFIRMATION)
            imageDrawable = imgDrawable
            imageName = imgName
            titleText = titleTxt
            subtitleSpannable = subTitleTxt
            firstBtnText = firstBtnTxt
            secondBtnText = secondBtnTxt
            setOnDismiss {
                bottomSheet.dismiss()
            }
            setOnBtnFirst(btnFirstFunction)
            setOnBtnSecond(btnSecondFunction)
            isClickable = isClickableOutside
            withBackgroundSecondBtn = withBgSecondBtn
            isCloseButtonVisible = showCloseButton
            isShowPillDecoration = showPill
            show(fragmentManager, "")
        }
    }

    @JvmStatic
    fun showDialogConfirmationDismissEvent(
        fragmentManager: FragmentManager,
        imgDrawable: Int,
        imgName: String,
        titleTxt: String,
        subTitleTxt: String,
        btnFirstFunction: () -> Unit = {},
        btnSecondFunction: () -> Unit = {},
        btnDismissFunction: () -> Unit = {},
        isClickableOutside: Boolean = true,
        firstBtnTxt: String = "",
        secondBtnTxt: String = "",
        withBgSecondBtn: Boolean = true,
        showCloseButton: Boolean = false,
    ) {
        val bottomSheet = BottomSheetGeneralNewSkinFragment()
        bottomSheet.apply {
            setFieldType(BottomSheetGeneralNewSkinFragment.DialogType.CONFIRMATION)
            imageDrawable = imgDrawable
            imageName = imgName
            titleText = titleTxt
            subtitleText = subTitleTxt
            firstBtnText = firstBtnTxt
            secondBtnText = secondBtnTxt
            bottomSheet.setOnDismiss {
                if (!requireActivity().isFinishing && isAdded) {
                    bottomSheet.dismissAllowingStateLoss()
                }
                btnDismissFunction()
            }
            setOnBtnFirst(btnFirstFunction)
            setOnBtnSecond(btnSecondFunction)
            isClickable = isClickableOutside
            withBackgroundSecondBtn = withBgSecondBtn
            isCloseButtonVisible = showCloseButton
            show(fragmentManager, "")
        }
    }

    @JvmStatic
    fun showDialogConfirmationWithoutImage(
        fragmentManager: FragmentManager,
        titleTxt: String,
        subTitleTxt: String,
        btnFirstFunction: () -> Unit = {},
        btnSecondFunction: () -> Unit = {},
        isClickableOutside: Boolean = true,
        firstBtnTxt: String = "",
        secondBtnTxt: String = "",
        withBgSecondBtn: Boolean = true,
        showCloseButton: Boolean = false
    ) {
        val bottomSheet = BottomSheetGeneralNewSkinFragment()
        bottomSheet.apply {
            setFieldType(BottomSheetGeneralNewSkinFragment.DialogType.MESSAGEWITHOUTIMAGE)
            titleText = titleTxt
            subtitleText = subTitleTxt
            firstBtnText = firstBtnTxt
            secondBtnText = secondBtnTxt
            setOnBtnFirst {
                btnFirstFunction()
                dismiss()
            }
            setOnBtnSecond {
                btnSecondFunction()
                dismiss()
            }
            isClickable = isClickableOutside
            withBackgroundSecondBtn = withBgSecondBtn
            isCloseButtonVisible = showCloseButton
            show(fragmentManager, "")
        }
    }

    @JvmStatic
    fun showDialogInformationWithNoImage(
        fragmentManager: FragmentManager,
        imgPath: String,
        imgName: String,
        titleTxt: String,
        subTitleTxt: String,
        btnFirstFunction: () -> Unit = {},
        isClickableOutside: Boolean = true,
        firstBtnTxt: String = ""
    ) {
        val bottomSheet = BottomSheetGeneralNewSkinFragment()
        bottomSheet.apply {
            setFieldType(BottomSheetGeneralNewSkinFragment.DialogType.INFORMATIONWITHOUTIMAGE)
            imagePath = imgPath
            imageName = imgName
            titleText = titleTxt
            subtitleText = subTitleTxt
            firstBtnText = firstBtnTxt
            setOnBtnFirst {
                dismiss()
            }
            setOnDismiss {
                btnFirstFunction()
            }
            show(fragmentManager, "")
            isClickable = isClickableOutside
        }
    }

    @JvmStatic
    fun showDialogInformationHtmlSubtitle(
        fragmentManager: FragmentManager,
        imgPath: String,
        imgName: String,
        titleTxt: String,
        subTitleTxt: String,
        btnFirstFunction: () -> Unit = {},
        isClickableOutside: Boolean = true,
        firstBtnTxt: String = ""
    ) {
        val bottomSheet = BottomSheetGeneralNewSkinFragment()
        bottomSheet.apply {
            setFieldType(BottomSheetGeneralNewSkinFragment.DialogType.INFORMATIONHTML)
            imagePath = imgPath
            imageName = imgName
            titleText = titleTxt
            subtitleText = subTitleTxt
            firstBtnText = firstBtnTxt
            setOnBtnFirst {
                dismiss()
            }
            setOnDismiss {
                btnFirstFunction()
            }
            show(fragmentManager, "")
            isClickable = isClickableOutside
        }
    }

    @JvmStatic
    fun showDialogInformationWithActionAndTopText(
        fragmentManager: FragmentManager,
        txtTopTitle: String,
        txtTopDesc: String,
        imgPath: String,
        imgName: String,
        titleTxt: String,
        subTitleTxt: String,
        btnFirstFunction: () -> Unit = {},
        btnSecondFunction: () -> Unit = {},
        isClickableOutside: Boolean = true,
        firstBtnTxt: String = "",
        secondBtnTxt: String = "",
        showCloseButton: Boolean = false,
        showPill: Boolean = false
    ) {
        val bottomSheet = BottomSheetGeneralNewSkinFragment()
        bottomSheet.apply {
            setFieldType(BottomSheetGeneralNewSkinFragment.DialogType.INFORMATIONWITHTOPTEXT)
            titleTopText = txtTopTitle
            titleTopDesc = txtTopDesc
            imagePath = imgPath
            imageName = imgName
            titleText = titleTxt
            subtitleText = subTitleTxt
            firstBtnText = firstBtnTxt
            secondBtnText = secondBtnTxt
            setOnBtnFirst {
                btnFirstFunction()
                bottomSheet.dismiss()
            }
            setOnDismiss {
                btnSecondFunction()
            }
            show(fragmentManager, "")
            isClickable = isClickableOutside
            isCloseButtonVisible = showCloseButton
            isShowPillDecoration = showPill
        }
    }

    @JvmStatic
    fun showDialogInformationWithActionSelection(
        fragmentManager: FragmentManager,
        titleTxt: String,
        subTitleTxt: String,
        actionList: List<Triple<Int,String,((String) -> Unit)>>,
        btnFirstFunction: () -> Unit = {},
        isClickableOutside: Boolean = true,
        firstBtnTxt: String = ""
    ) {
        val bottomSheet = BottomSheetGeneralNewSkinFragment()
        bottomSheet.apply {
            setFieldType(BottomSheetGeneralNewSkinFragment.DialogType.INFORMATION_WITH_ACTION_SELECTION)
            titleText = titleTxt
            subtitleText = subTitleTxt
            firstBtnText = firstBtnTxt
            customActionViews = actionList
            setOnDismiss {
                btnFirstFunction()
                bottomSheet.dismiss()
            }
            show(fragmentManager, "")
            isClickable = isClickableOutside
        }
    }

    @JvmStatic
    fun showDialogInformationWithClearData(
        fragmentManager: FragmentManager,
        imgPath: String,
        imgName: String,
        titleTxt: String,
        subTitleTxt: String,
        btnFirstFunction: () -> Unit = {},
        onDismissFunction: () -> Unit = {},
        isClickableOutside: Boolean = true,
        firstBtnTxt: String = "",
        showCloseButton: Boolean = false,
        showPill: Boolean = true,
        isDismissOnFirstClick: Boolean = true
    ) {
        val bottomSheet = BottomSheetGeneralNewSkinFragment()
        bottomSheet.apply {
            setFieldType(BottomSheetGeneralNewSkinFragment.DialogType.INFORMATION)
            imagePath = imgPath
            imageName = imgName
            titleText = titleTxt
            subtitleText = subTitleTxt
            firstBtnText = firstBtnTxt
            setOnBtnFirst {
                btnFirstFunction()
                dismiss()
            }
            setOnDismiss {
                onDismissFunction()
                dismiss()
            }
            show(fragmentManager, "")
            isClickable = isClickableOutside
            isCloseButtonVisible = showCloseButton
            isShowPillDecoration = showPill
            this.isDismissOnFirstClick = isDismissOnFirstClick
        }
    }
}