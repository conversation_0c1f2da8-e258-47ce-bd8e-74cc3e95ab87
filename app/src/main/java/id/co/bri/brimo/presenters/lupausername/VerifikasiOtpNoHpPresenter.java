package id.co.bri.brimo.presenters.lupausername;

import java.util.concurrent.TimeUnit;

import id.co.bri.brimo.contract.IPresenter.lupausername.IVerifikasiOtpNoHpPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.lupausername.IVerifikasiOtpNoHpView;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.config.AppConfig;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.apimodel.request.forgetuserpass.ValidateOtpUserPassReq;
import id.co.bri.brimo.models.apimodel.request.pengelolaankartu.ResendOtpReissueReq;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.models.apimodel.response.forgetuserpass.OtpEmailRes;
import id.co.bri.brimo.models.apimodel.response.pengelolaankartu.OtpReissueResponse;
import id.co.bri.brimo.presenters.MvpPresenter;
import io.reactivex.disposables.CompositeDisposable;

public class VerifikasiOtpNoHpPresenter<V extends IVerifikasiOtpNoHpView & IMvpView> extends MvpPresenter<V> implements IVerifikasiOtpNoHpPresenter<V> {

    public VerifikasiOtpNoHpPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable,
                                      BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource,
                                      TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource,
                transaksiPfmSource);
    }

    protected String urlValidate;
    protected String urlResend;

    @Override
    public void setUrlValidate(String url) {
        urlValidate = url;
    }

    @Override
    public void setUrlResend(String url) {
        urlResend = url;
    }

    @Override
    public void resendOtp(ResendOtpReissueReq resendOtpReissueReq) {
        if (urlResend.isEmpty() || !isViewAttached()) return;

        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();

        getCompositeDisposable().add(
                getApiSource().getData(urlResend, resendOtpReissueReq, seqNum)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(getSchedulerProvider().io())
                        .observeOn(getSchedulerProvider().mainThread())
                        .subscribeWith(new ApiObserver(getView(), seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().hideProgress();
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                getView().hideProgress();
                                OtpReissueResponse otpReissueResponse = response.getData(OtpReissueResponse.class);
                                getView().onSuccessResendOtp(otpReissueResponse);
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                getView().onException(restResponse.getDesc());
                            }
                        })
        );
    }

    @Override
    public void validateOtp(ValidateOtpUserPassReq request) {
        if (urlValidate.isEmpty() || !isViewAttached()) return;

        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();

        getCompositeDisposable().add(
                getApiSource().getData(urlValidate, request, seqNum)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(getSchedulerProvider().io())
                        .observeOn(getSchedulerProvider().mainThread())
                        .subscribeWith(new ApiObserver(getView(), seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().hideProgress();
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                getView().hideProgress();
                                OtpEmailRes otpEmailRes = response.getData(OtpEmailRes.class);
                                getView().onSuccessValidateOtp(otpEmailRes);
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                getView().deleteInputOtp();
                                if (restResponse.getCode().equals(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.getValue()))
                                    getView().onExceptionTrxExpired(restResponse.getDesc());
                                else
                                    getView().onException(restResponse.getDesc());
                            }
                        })
        );
    }
}