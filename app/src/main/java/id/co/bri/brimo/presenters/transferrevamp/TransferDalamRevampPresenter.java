package id.co.bri.brimo.presenters.transferrevamp;

import com.singalarity.sdknbr.dynamickey.TAOSecure;

import id.co.bri.brimo.contract.IPresenter.transferrevamp.ITransferDalamRevampPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.transferrevamp.ITransferDalamRevampView;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.config.AppConfig;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.apimodel.request.FastInquiryTransferAliasRevampRequest;
import id.co.bri.brimo.models.apimodel.request.InquiryTransferAliasRevampRequest;
import id.co.bri.brimo.models.apimodel.request.UpdateSavedRequestTfMethod;
import id.co.bri.brimo.models.apimodel.response.BaseFormResponse;
import id.co.bri.brimo.models.apimodel.response.GeneralFormResponse;
import id.co.bri.brimo.models.apimodel.response.InquiryTransferRevampResponse;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.models.apimodel.response.SavedResponse;
import id.co.bri.brimo.presenters.MvpPresenter;

import java.util.concurrent.TimeUnit;

import id.co.bri.brimo.util.singalarity.helper.IC2KeyHelper;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.disposables.Disposable;

public class TransferDalamRevampPresenter<V extends IMvpView & ITransferDalamRevampView> extends MvpPresenter<V> implements ITransferDalamRevampPresenter<V> {

    protected String inquiryUrl;
    protected String konfirmasiUrl;
    protected String paymentUrl;
    protected Object inquiryRequest = null;
    protected TAOSecure taoSecure;
    public String mUrlForm;
    private String saveId = "";
    private String purchaseType = "";
    private String tfMethod = "";

    private boolean onLoad = false;
    private BaseFormResponse baseFormResponse = null;
    private IC2KeyHelper c2DynamicKeyHelper;
    private Disposable disposableInquiry;

    public TransferDalamRevampPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, TransaksiPfmSource transaksiPfmSource, IC2KeyHelper dynamicKey) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
        this.c2DynamicKeyHelper = dynamicKey;
    }

    @Override
    public void getDataForm() {
        if (mUrlForm == null || !isViewAttached()) {
            return;
        }

        String seqNum = getBRImoPrefRepository().getSeqNumber();

        getCompositeDisposable().add(
                getApiSource().getDataForm(mUrlForm, seqNum)//function(param)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(getSchedulerProvider().io())
                        .observeOn(getSchedulerProvider().mainThread())
                        .subscribeWith(new ApiObserver(getView(), seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                //TO-DO onSuccess
                                if (response.getData() != null) {
                                    onApiSuccess(response);
                                }

                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                onApiError(restResponse);
                            }
                        })
        );

    }

    /**
     * method untuk memanggil data form dari fast menu
     */
    @Override
    public void getDataFormFastMenu() {
        if (!isViewAttached() || mUrlForm.isEmpty())
            return;

        String seqNum = getBRImoPrefRepository().getSeqNumber();

        getCompositeDisposable().add(
                getApiSource().getDataC2(mUrlForm, getFastMenuRequest(), seqNum)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(getSchedulerProvider().single())
                        .observeOn(getSchedulerProvider().mainThread())
                        .subscribeWith(new ApiObserver(getView(), seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                if (errorMessage.equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_DISABLE_SINGALARITY.getValue())) {
                                    //update flag DKL C2
                                    getBRImoPrefRepository().saveDklC2(false);
                                    //retry hit form
                                    getDataFormFastMenu();
                                } else if (errorMessage.equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_ENABLE_SINGALARITY.getValue())) {
                                    //update flag DKL C2
                                    getBRImoPrefRepository().saveDklC2(true);
                                    //retry hit form
                                    getDataFormFastMenu();
                                } else {
                                    getView().onException(errorMessage);
                                }
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                getView().hideProgress();
                                if (response.getData() != null) {
                                    onApiSuccess(response);
                                }

                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                onApiError(restResponse);
                            }
                        })
        );
    }

    @Override
    public void cekFirstTimeVisit() {
        if (getBRImoPrefRepository().isFirstTimeVisitTransfer()) {
            getView().onShowBubbleFirstTimeVisit();
            saveStatusFirstTimeVisit(false);
        }
    }


    @Override
    public void onApiSuccess(RestResponse response) {
        super.onApiSuccess(response);

        if (!isViewAttached())
            return;

        if (response != null) {
            setBaseFormResponse(response.getData(BaseFormResponse.class));
            getView().onSuccessGetHistoryForm(getBaseFormResponse().getHistory());
            getView().onSuccessGetRestResponse(response);
        }
        getView().isHideSkeleton(true);
        cekFirstTimeVisit();
        GeneralFormResponse generalFormResponse = null;
        if (response != null) {
            generalFormResponse = response.getData(GeneralFormResponse.class);
        }
        if (generalFormResponse != null) {
            getView().onSuccessGetSavedForm(generalFormResponse.getSaved());
        }
    }

    public BaseFormResponse getBaseFormResponse() {
        return baseFormResponse;
    }

    public void setBaseFormResponse(BaseFormResponse baseFormResponse) {
        this.baseFormResponse = baseFormResponse;
    }

    @Override
    public void setUrlForm(String urlForm) {
        this.mUrlForm = urlForm;
    }


    /**
     * @param url
     * @param savedResponse
     * @param position
     * @param type
     */
    @Override
    public void setUpdateItemTf(String url, SavedResponse savedResponse, int position, int type) {
        if (url == null || !isViewAttached() || onLoad) {
            return;
        }

        onLoad = true;
        String s = savedResponse.getValue();
        String[] str1 = s.split("\\|");


        if (str1.length > 1) {
            saveId = str1[0];
            purchaseType = str1[1];

        } else {
            saveId = str1[0];
        }

        if (str1.length > 3) {
            tfMethod = str1[3];
        }

        UpdateSavedRequestTfMethod updateSavedRequest = new UpdateSavedRequestTfMethod(saveId, purchaseType, tfMethod);

        String seqNum = getBRImoPrefRepository().getSeqNumber();

        getCompositeDisposable().add(getApiSource().getData(url, updateSavedRequest, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                .subscribeOn(getSchedulerProvider().io())
                .observeOn(getSchedulerProvider().mainThread())
                .subscribeWith(new ApiObserver(getView(), seqNum) {

                    @Override
                    protected void onFailureHttp(String errorMessage) {
                        onLoad = false;
                        getView().onException(errorMessage);
                    }

                    @Override
                    protected void onApiCallSuccess(RestResponse response) {
                        //TO-DO onSuccess
                        onLoad = false;
                        getView().hideProgress();
                        getView().onSuccessUpdate(savedResponse, position, type);

                    }

                    @Override
                    protected void onApiCallError(RestResponse restResponse) {
                        onApiError(restResponse);
                    }
                }));

    }

    @Override
    public void getDataInquiry(String bankCode, String accountStr, boolean isFromFast) {
        if (inquiryUrl == null || !isViewAttached()) {
            return;
        }

        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        if (isFromFast) {
            inquiryRequest = new FastInquiryTransferAliasRevampRequest(getFastMenuRequest(), bankCode, accountStr);
            disposableInquiry = generateRevampInquiryDisposableC2(bankCode, accountStr, isFromFast, seqNum);
        } else {
            inquiryRequest = new InquiryTransferAliasRevampRequest(bankCode, accountStr);
            disposableInquiry = generateRevampInquiryDisposable(seqNum);
        }

        getCompositeDisposable()
                .add(disposableInquiry);
    }

    protected Disposable generateRevampInquiryDisposable(String seqNum) {
        return getApiSource().getData(inquiryUrl, inquiryRequest, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                .subscribeOn(getSchedulerProvider().io())
                .observeOn(getSchedulerProvider().mainThread())
                .subscribeWith(new ApiObserver(getView(), seqNum) {

                    @Override
                    protected void onFailureHttp(String errorMessage) {
                        getView().hideProgress();
                        getView().onException(errorMessage);
                    }

                    @Override
                    protected void onApiCallSuccess(RestResponse response) {
                        getView().hideProgress();
                        InquiryTransferRevampResponse responsebriva = restResponse.getData(InquiryTransferRevampResponse.class);
                        getView().onSuccessGetInquiry(responsebriva);
                    }

                    @Override
                    protected void onApiCallError(RestResponse restResponse) {
                        getView().hideProgress();
                        if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_99.getValue()))
                            getView().onException99(restResponse.getDesc());
                        else
                            getView().onException(restResponse.getDesc());

                    }
                });
    }

    protected Disposable generateRevampInquiryDisposableC2(String bankCode, String accountStr, boolean isFromFast, String seqNum) {
        return getApiSource().getDataC2(inquiryUrl, inquiryRequest, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                .subscribeOn(getSchedulerProvider().io())
                .observeOn(getSchedulerProvider().mainThread())
                .subscribeWith(new ApiObserver(getView(), seqNum) {

                    @Override
                    protected void onFailureHttp(String errorMessage) {
                        if (errorMessage.equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_DISABLE_SINGALARITY.getValue())) {
                            //update flag DKL C2
                            getBRImoPrefRepository().saveDklC2(false);
                            //retry hit inquiry
                            getDataInquiry(bankCode, accountStr, isFromFast);
                        } else if (errorMessage.equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_ENABLE_SINGALARITY.getValue())) {
                            //update flag DKL C2
                            getBRImoPrefRepository().saveDklC2(true);
                            //retry hit inquiry
                            getDataInquiry(bankCode, accountStr, isFromFast);
                        } else {
                            getView().onException(errorMessage);
                        }
                    }

                    @Override
                    protected void onApiCallSuccess(RestResponse response) {
                        getView().hideProgress();
                        InquiryTransferRevampResponse responsebriva = restResponse.getData(InquiryTransferRevampResponse.class);
                        getView().onSuccessGetInquiry(responsebriva);
                    }

                    @Override
                    protected void onApiCallError(RestResponse restResponse) {
                        getView().hideProgress();
                        getView().onException(restResponse.getDesc());
                    }
                });
    }


    @Override
    public void setUrlInquiry(String urlInquiry) {
        this.inquiryUrl = urlInquiry;
    }

    @Override
    public void saveStatusFirstTimeVisit(Boolean isFirstTime) {
        getBRImoPrefRepository().saveIsFirstTimeVisitTransfer(isFirstTime);
    }


    @Override
    public void checkAccessTokenC2() {
        //apakah pernah init token C2
        if (getBRImoPrefRepository().isInitC2TokenSuccess()) {
            getDataFormFastMenu();
        } else {
            c2DynamicKeyHelper.getAccessToken()
                    .subscribeOn(getSchedulerProvider().single())
                    .observeOn(getSchedulerProvider().mainThread())
                    .subscribe(() -> {
                        getDataFormFastMenu();
                    }, error -> {
                        getDataFormFastMenu();
                    });
        }
    }

}