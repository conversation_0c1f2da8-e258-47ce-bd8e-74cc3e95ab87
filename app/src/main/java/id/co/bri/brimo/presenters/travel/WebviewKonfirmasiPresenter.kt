package id.co.bri.brimo.presenters.travel

import id.co.bri.brimo.contract.IPresenter.travel.IWebviewKonfirmasiPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.travel.IWebviewKonfirmasiView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.api.observer.ApiObserverKonfirmasi
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimo.data.repository.category.CategoryPfmSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.config.LifestyleConfig

import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.cashback.CashbackRevFilterRequest
import id.co.bri.brimo.models.apimodel.request.cashback.PilihCashbackRequest
import id.co.bri.brimo.models.apimodel.request.lifestyle.RequestPaymentMoliga
import id.co.bri.brimo.models.apimodel.request.revampbriva.PayBrivaRevampRequest
import id.co.bri.brimo.models.apimodel.request.travel.TravelFlightPaymentRequest
import id.co.bri.brimo.models.apimodel.response.GeneralResponse
import id.co.bri.brimo.models.apimodel.response.InquiryBrivaRevampResponse
import id.co.bri.brimo.models.apimodel.response.ListAllCashbackFilterResponse
import id.co.bri.brimo.models.apimodel.response.ReceiptRevampResponse
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.SelectCashbackResponse
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.disposables.Disposable

class WebviewKonfirmasiPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBRImoPrefRepository: BRImoPrefSource,
    apiSource: ApiSource,
    categoryPfmSource: CategoryPfmSource,
    transaksiPfmSource: TransaksiPfmSource,
    anggaranPfmSource: AnggaranPfmSource
) : MvpPresenter<V>(
    schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource
),
    IWebviewKonfirmasiPresenter<V> where V : IMvpView, V : IWebviewKonfirmasiView {

    lateinit var mUrlPay: String
    lateinit var paymentRequest: Any
    private lateinit var urlGetListCashback: String
    private lateinit var selectCashbackUrl: String
    private lateinit var redeemCashbackRequest: Any
    private lateinit var cashbackRequest: Any

    override fun getDefaultSaldo() {
        var saldo = 0.0
        val saldoText = brImoPrefRepository.saldoRekeningUtama
        if (saldoText != "") {
            saldo = java.lang.Double.valueOf(saldoText)
        }
        val defaultAcc = brImoPrefRepository.accountDefault
        val saldoString = brImoPrefRepository.saldoRekeningUtamaString
        val saldoHold = brImoPrefRepository.saldoHold

        view.setDefaultSaldo(saldo, saldoString, defaultAcc, saldoHold)
    }

    override fun setUrlPayment(url: String) {
        mUrlPay = url
    }

    override fun getDataPaymentRevamp(
        pin: String, mInquiryBrivaRevampResponse: InquiryBrivaRevampResponse,
        account: String, saveAs: String, note: String, mType: String
    ) {
        if (mUrlPay.isEmpty() || !isViewAttached) return

        view.showProgress()
        val seqNum = brImoPrefRepository.seqNumber

        when (mType) {
            LifestyleConfig.Lifestyle.TRAVEL_KCIC -> {
                paymentRequest = PayBrivaRevampRequest(
                    mInquiryBrivaRevampResponse.referenceNumber,
                    account, mInquiryBrivaRevampResponse.amount.toString(), saveAs,
                    note, pin, mInquiryBrivaRevampResponse.pfmCategory.toString()
                )
            }

            LifestyleConfig.Lifestyle.TRAVEL_PESAWAT -> {
                paymentRequest = TravelFlightPaymentRequest(
                    mInquiryBrivaRevampResponse.referenceNumber,
                    account, pin, mInquiryBrivaRevampResponse.pfmCategory.toString(), note
                )
            }

            else -> {
                paymentRequest = RequestPaymentMoliga(
                    account,
                    mInquiryBrivaRevampResponse.referenceNumber,
                    pin,
                    mInquiryBrivaRevampResponse.pfmCategory.toString()
                )
            }

        }
        val disposable: Disposable =
            apiSource.getData(mUrlPay, paymentRequest, seqNum) //function(param)
                .subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserverKonfirmasi(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String?) {
                        getView().hideProgress()
                        getView().onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView().hideProgress()
                        val receiptRevampResponse =
                            response.getData(ReceiptRevampResponse::class.java)
                        getView().onSuccessGetPaymentRevamp(receiptRevampResponse)
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView().hideProgress()
                        when (restResponse.code) {
                            RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.value -> getView().onException93(restResponse.desc)
                            RestResponse.ResponseCodeEnum.RC_01.value -> getView().onException01(restResponse.desc)
                            RestResponse.ResponseCodeEnum.RC_94.value -> getView().onExceptionSnackbarBack(restResponse.desc)
                            RestResponse.ResponseCodeEnum.RC_LIMIT_EXCEEDED.value -> getView().onExceptionLimitExceed(restResponse.getData(GeneralResponse::class.java))
                            else -> getView().onException(restResponse.desc)
                        }
                    }
                })

        compositeDisposable.add(disposable)
    }

    override fun setUrlGetCashback(url: String) {
        this.urlGetListCashback = url
    }

    override fun getCashbackAll(
        accountNumber: String,
        referenceNumber: String
    ) {
        if (urlGetListCashback == null || !isViewAttached) {
            return
        }

        cashbackRequest = CashbackRevFilterRequest(
            accountNumber = accountNumber,
            referenceNumber = referenceNumber
        )

        view.showProgress()
        val seqNum = brImoPrefRepository.seqNumber
        compositeDisposable.add(
            apiSource.getData(urlGetListCashback, cashbackRequest, seqNum)
                .subscribeOn(schedulerProvider.single())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView().onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView().hideProgress()
                        if (response.code.equals(RestResponse.ResponseCodeEnum.RC_SUCCESS.value, ignoreCase = true)) {
                            val responseData = response.getData(
                                ListAllCashbackFilterResponse::class.java
                            )
                            getView().onSuccessGetCashback(responseData)
                        } else if (response.code.equals(
                                RestResponse.ResponseCodeEnum.RC_01.value,
                                ignoreCase = true
                            )
                        ) {
                            getView().onCashbackBlank(response.desc)
                        }
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView().hideProgress()
                        getView().onException(restResponse.desc)
                    }
                })
        )
    }

    override fun setRedeemCashbackUrl(url: String) {
        this.selectCashbackUrl = url
    }

    override fun getRedeemCashback(refNum: String, code: String) {
        if (selectCashbackUrl == null || !isViewAttached) {
            return
        }

        redeemCashbackRequest =
            PilihCashbackRequest(refNum, code)

        view.showProgress()
        val seqNum = brImoPrefRepository.seqNumber
        compositeDisposable.add(
            apiSource.getData(selectCashbackUrl, redeemCashbackRequest, seqNum)
                .subscribeOn(schedulerProvider.single())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView().onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView().hideProgress()
                        val selectCashbackResponse = response.getData(
                            SelectCashbackResponse::class.java
                        )
                        getView().onSuccessClearSelectedCashback(selectCashbackResponse)
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView().hideProgress()
                        when (restResponse.code) {
                            RestResponse.ResponseCodeEnum.RC_12.value -> getView().onException12(restResponse.desc)
                            else -> getView().onException(restResponse.desc)
                        }
                    }
                })
        )
    }
}