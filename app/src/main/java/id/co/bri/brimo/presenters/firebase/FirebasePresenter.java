package id.co.bri.brimo.presenters.firebase;

import android.util.Log;

import id.co.bri.brimo.contract.IPresenter.firebase.IFirebasePresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.firebase.IFirebaseView;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.config.AppConfig;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.apimodel.request.BaseSubscribeTopicRequest;
import id.co.bri.brimo.models.apimodel.request.SubscribeTopicRequest;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.presenters.MvpPresenter;

import java.util.concurrent.TimeUnit;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.schedulers.Schedulers;

public class FirebasePresenter<V extends IMvpView & IFirebaseView> extends MvpPresenter<V> implements IFirebasePresenter<V> {

    private static final String TAG = "FirebasePresenter";
    private static String urlSubscribe = "v1-push-notification-subscribe-topic";

    public FirebasePresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void subscribeTopicAll(String newToken) {
        if(!isViewAttached() || newToken.isEmpty() || urlSubscribe.isEmpty()){
            if(!GeneralHelper.isProd())
                Log.d(TAG, "Subcribe failed, view or token or urlInformasi is empty");
            return;
        }

        SubscribeTopicRequest subscribeTopicRequest = new SubscribeTopicRequest(
                getFastMenuRequest().getUsername(),
                getFastMenuRequest().getTokenKey(),
                newToken,
                Constant.TOPIC_ALL);

        getBRImoPrefRepository().saveFirebaseToken(newToken);

        String seqNum = getBRImoPrefRepository().getSeqNumber();

        getCompositeDisposable().add(getApiSource().getData(urlSubscribe,subscribeTopicRequest, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(new ApiObserver(getView(),seqNum) {
                    @Override
                    protected void onFailureHttp(String type) {

                    }

                    @Override
                    protected void onApiCallSuccess(RestResponse response) {

                    }

                    @Override
                    protected void onApiCallError(RestResponse restResponse) {

                    }
                })
        );
    }


    @Override
    public void updateTokenFirebase(String token) {

        BaseSubscribeTopicRequest request = new BaseSubscribeTopicRequest(
                getBRImoPrefRepository().getUsername(),
                getBRImoPrefRepository().getTokenKey(),
                token
        );

        String seqNum = getBRImoPrefRepository().getSeqNumber();

        getCompositeDisposable().add(
                getApiSource().getUpdateTokenFirebase(request, seqNum)
                        .subscribeOn(getSchedulerProvider().io())
                        .observeOn(getSchedulerProvider().mainThread())
                        .subscribeWith(new ApiObserver(getView(),seqNum) {


                            @Override
                            protected void onFailureHttp(String type) {

                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {

                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {

                            }
                        })
        );
    }





}
