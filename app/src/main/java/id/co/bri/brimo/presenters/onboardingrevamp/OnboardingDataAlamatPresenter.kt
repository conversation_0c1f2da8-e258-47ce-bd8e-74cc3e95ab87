package id.co.bri.brimo.presenters.onboardingrevamp

import com.google.gson.Gson
import id.co.bri.brimo.contract.IPresenter.onboardingrevamp.IOnboardingDataAlamatPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.onboardingrevamp.IOnboardingDataAlamatView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimo.data.repository.category.CategoryPfmSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.config.AppConfig
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.onboardingrevamp.OnboardingDataAlamatReq
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.Schedulers
import java.util.concurrent.TimeUnit

class OnboardingDataAlamatPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBRImoPrefRepository: BRImoPrefSource,
    apiSource: ApiSource,
    categoryPfmSource: CategoryPfmSource,
    transaksiPfmSource: TransaksiPfmSource,
    anggaranPfmSource: AnggaranPfmSource
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    mBRImoPrefRepository,
    apiSource,
    transaksiPfmSource
), IOnboardingDataAlamatPresenter<V> where V : IMvpView, V : IOnboardingDataAlamatView {

    private var urlDataAlamat: String = ""

    override fun setUrl(url: String) {
        urlDataAlamat = url
    }

    override fun sendAddressData(onboardingDataAlamatReq: OnboardingDataAlamatReq) {
        if (urlDataAlamat.isEmpty() || !isViewAttached) return
        view.showProgress()
        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable.add(
            apiSource.getData(urlDataAlamat, onboardingDataAlamatReq, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView().hideProgress()
                        getView().onExceptionNoBackAction(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView().hideProgress()
                        getView().onSuccessAddressData(Gson().toJson(response.data))
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView().hideProgress()
                        getView().onExceptionRevamp(restResponse.desc)
                    }
                })
        )
    }

    override fun getDeviceId(): String {
        return brImoPrefRepository.deviceId
    }

}