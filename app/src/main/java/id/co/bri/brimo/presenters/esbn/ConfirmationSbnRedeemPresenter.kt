package id.co.bri.brimo.presenters.esbn

import id.co.bri.brimo.contract.IPresenter.esbn.IConfirmationSbnRedeemPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.esbn.IConfirmationSbnRedeemView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimo.data.repository.category.CategoryPfmSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.config.AppConfig

import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.PaymentRequest
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.esbn.ReceiptResponseNew
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.disposables.CompositeDisposable
import java.util.concurrent.TimeUnit

class ConfirmationSbnRedeemPresenter<V>(
    schedulerProvider: SchedulerProvider?,
    compositeDisposable: CompositeDisposable?,
    mBRImoPrefRepository: BRImoPrefSource?,
    apiSource: ApiSource?,
    categoryPfmSource: CategoryPfmSource?,
    transaksiPfmSource: TransaksiPfmSource?,
    anggaranPfmSource: AnggaranPfmSource?
) :
    MvpPresenter<V>(
        schedulerProvider,
        compositeDisposable,
        mBRImoPrefRepository,
        apiSource,
        transaksiPfmSource
    ),
    IConfirmationSbnRedeemPresenter<V> where V : IMvpView?, V : IConfirmationSbnRedeemView? {
    var urlCairkan: String? = null
    override fun start() {
        super.start()
    }

    override fun stop() {
        super.stop()
    }

    override fun setIUrlPayment(url: String?) {
        urlCairkan = url
    }

    override fun getPaymentRedeem(request: PaymentRequest) {
        view!!.showProgress()
        val seqNum = brImoPrefRepository.seqNumber
        compositeDisposable.add(
            apiSource.getData(urlCairkan, request, seqNum)
                .subscribeOn(schedulerProvider.single())
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(type: String) {
                        getView()!!.onException(type)
                        view!!.hideProgress()
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        view!!.hideProgress()

                        if (response.code.equals(RestResponse.ResponseCodeEnum.RC_SUCCESS.value, ignoreCase = true)) {
                            val dataResponse = response.getData(ReceiptResponseNew::class.java)
                            getView()!!.onSuccessGetPaymentRedeem(dataResponse)
                        }
                        else if (response.code.equals(RestResponse.ResponseCodeEnum.RC_02.value, ignoreCase = true))
                            getView()!!.onException02(response.desc)
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView()!!.hideProgress()
                        if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value, ignoreCase = true))
                            getView()!!.onSessionEnd(restResponse.desc)
                        else if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_12.value, ignoreCase = true))
                            getView()!!.onException12(restResponse.desc)
                        else if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.value, ignoreCase = true))
                            getView()!!.onException93(restResponse.desc)
                        else if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_02.value, ignoreCase = true))
                            getView()!!.onException02(restResponse.desc)
                        else
                            getView()!!.onException(restResponse.desc)
                    }
                })
        )
    }
}