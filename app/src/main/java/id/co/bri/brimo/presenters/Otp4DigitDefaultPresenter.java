package id.co.bri.brimo.presenters;

import java.util.concurrent.TimeUnit;

import id.co.bri.brimo.contract.IPresenter.IOtp4DigitDefaultPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.IOtp4DigitDefaultView;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.config.AppConfig;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.apimodel.request.GeneralResendOtpRequest;
import id.co.bri.brimo.models.apimodel.request.GeneralValidateOtpRequest;
import id.co.bri.brimo.models.apimodel.response.EditEmailResponse;
import id.co.bri.brimo.models.apimodel.response.GeneralOtpResponse;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import io.reactivex.disposables.CompositeDisposable;

public class Otp4DigitDefaultPresenter<V extends IMvpView & IOtp4DigitDefaultView> extends MvpPresenter<V> implements IOtp4DigitDefaultPresenter<V> {

    private String urlSend;
    private String urlResend;

    public Otp4DigitDefaultPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void setUrlSend(String urlSend) {
        this.urlSend = urlSend;
    }

    @Override
    public void setUrlResend(String urlResend) {
        this.urlResend = urlResend;
    }

    @Override
    public void onSendData(GeneralValidateOtpRequest request) {
        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();

        if (isViewAttached()) {
            getCompositeDisposable().add(
                    getApiSource().getData(urlSend, request, seqNum)
                            .subscribeOn(getSchedulerProvider().single())
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .observeOn(getSchedulerProvider().mainThread())
                            .subscribeWith(new ApiObserver(getView(),seqNum) {
                                @Override
                                protected void onFailureHttp(String type) {
                                    getView().hideProgress();
                                    getView().onException(type);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    getView().hideProgress();
                                    getView().deleteInputOtp();
                                    EditEmailResponse editEmailResponse = response.getData(EditEmailResponse.class);
                                    getView().onSuccess(editEmailResponse);
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().hideProgress();
                                    getView().deleteInputOtp();
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue())) {
                                        getView().onSessionEnd(restResponse.getDesc());
                                    } else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.getValue())) {
                                        getView().onException93(restResponse.getDesc());
                                    } else
                                        getView().onException(restResponse.getDesc());
                                }
                            })
            );
        }
    }

    @Override
    public void onResendData(String refNum) {
        if (isViewAttached()) {
            getView().showProgress();
            String seqNum = getBRImoPrefRepository().getSeqNumber();
            GeneralResendOtpRequest generalResendOtpRequest = new GeneralResendOtpRequest(refNum);

            getCompositeDisposable().add(
                    getApiSource().getData(urlResend, generalResendOtpRequest, seqNum)
                            .subscribeOn(getSchedulerProvider().single())
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .observeOn(getSchedulerProvider().mainThread())
                            .subscribeWith(new ApiObserver(getView(),seqNum) {
                                @Override
                                protected void onFailureHttp(String type) {
                                    getView().hideProgress();
                                    getView().deleteInputOtp();
                                    getView().onException(type);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    getView().hideProgress();
                                    getView().deleteInputOtp();
                                    GeneralOtpResponse generalOtpResponse = response.getData(GeneralOtpResponse.class);
                                    getView().onResendSuccess(generalOtpResponse);
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().hideProgress();
                                    getView().deleteInputOtp();
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue())) {
                                        getView().onSessionEnd(restResponse.getDesc());
                                    } else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.getValue())) {
                                        getView().onException93(restResponse.getDesc());
                                    } else
                                        getView().onException(restResponse.getDesc());
                                }
                            })
            );
        }
    }
}