package id.co.bri.brimo.presenters.emas

import id.co.bri.brimo.contract.IPresenter.emas.ITambahDaftarEmasPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.emas.ITambahDaftarEmasView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimo.data.repository.category.CategoryPfmSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.config.AppConfig
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.SafetyPusatBantuanRequest
import id.co.bri.brimo.models.apimodel.request.emas.InquiryBeliEmasRequest
import id.co.bri.brimo.models.apimodel.response.QuestionResponse
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.emas.InquiryOpenEmasResponse
import id.co.bri.brimo.models.apimodel.response.emas.SafetyModeDrawerResponse
import id.co.bri.brimo.models.apimodel.response.onExceptionWH
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.Schedulers
import java.util.concurrent.TimeUnit

class TambahDaftarEmasPresenter<V>(schedulerProvider: SchedulerProvider,
                                   compositeDisposable: CompositeDisposable,
                                   mBRImoPrefRepository: BRImoPrefSource, apiSource: ApiSource,
                                   categoryPfmSource: CategoryPfmSource,
                                   transaksiPfmSource: TransaksiPfmSource,
                                   anggaranPfmSource: AnggaranPfmSource) : MvpPresenter<V>(
    schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource),
    ITambahDaftarEmasPresenter<V> where V : IMvpView, V : ITambahDaftarEmasView {
    private var inquiryUrl =""
    private var pusatBantuanUrl =""

    override fun getInquiryBeli(request: InquiryBeliEmasRequest) {
        if (!isViewAttached) {
            return
        }

        view.showProgress()
        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable
            .add(
                apiSource.getData(inquiryUrl, request, seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(Schedulers.single())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView().hideProgress()
                            getView().onException(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            //TO-DO onSuccess
                            getView().hideProgress()
                            val data = response.getData(
                                InquiryOpenEmasResponse::class.java
                            )
                            if (restResponse.code.equals("00", ignoreCase = true)) {
                                getView().onSuccessFormBeliEmas(data)
                            }

                            else if (restResponse.code.equals("02", ignoreCase = true)) {
                                val onExceptionCase02: onExceptionWH =
                                    restResponse.getData(
                                        onExceptionWH::class.java
                                    )
                                getView().exceptionEODEOM(onExceptionCase02)
                            }
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView()?.hideProgress()
                            if (restResponse.code.equals("05", ignoreCase = true)) {
                                getView()?.onSessionEnd(restResponse.desc)
                            }
                            else if (restResponse.code.equals("SM", ignoreCase = true)) {
                                val response = restResponse.getData(
                                    SafetyModeDrawerResponse::class.java
                                )
                                getView()?.onSafetyMode(response)
                            }
                            else getView()?.onException(
                                restResponse.desc
                            )
                        }
                    })
            )
    }

    override fun setInquiryUrl(url: String) {
        inquiryUrl = url
    }

    override fun getPusatBantuanSafety(code: String) {
        if (!isViewAttached) {
            return
        }

        view.showProgress()
        val safetyPusatBantuanRequest = SafetyPusatBantuanRequest(code)
        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable
            .add(
                apiSource.getData(pusatBantuanUrl, safetyPusatBantuanRequest, seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView()?.hideProgress()
                            getView()?.onException(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            //TO-DO onSuccess
                            getView().hideProgress()
                            val topicQuestionResponse = response.getData(
                                QuestionResponse::class.java
                            )
                            getView().onSuccessGetPusatBantuan(
                                topicQuestionResponse
                            )
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView().hideProgress()
                            if (restResponse.code.equals(
                                    "05",
                                    ignoreCase = true
                                )
                            ) getView().onSessionEnd(restResponse.desc) else getView()?.onException(
                                restResponse.desc
                            )
                        }
                    })
            )
    }

    override fun setUrlPusatBantuan(url: String) {
        pusatBantuanUrl = url
    }
}