package id.co.bri.brimo.presenters.ubahpin;

import java.util.concurrent.TimeUnit;

import javax.inject.Inject;

import id.co.bri.brimo.contract.IPresenter.ubahpin.IUbahPinPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.ubahpin.IUbahPinView;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.config.AppConfig;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.apimodel.request.UbahPinRequest;
import id.co.bri.brimo.models.apimodel.response.ErrorResponseNewSkin;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.models.apimodel.response.UbahPinResponse;
import id.co.bri.brimo.presenters.MvpPresenter;
import io.reactivex.disposables.CompositeDisposable;

public class UbahPinPresenter<V extends IMvpView & IUbahPinView> extends MvpPresenter<V> implements IUbahPinPresenter<V> {

    private String url = "";
    private String pin = "";

    @Inject
    public UbahPinPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void setUrl(String url) {
        this.url = url;
    }

    @Override
    public void ubahPin() {
        if (getView() == null) {
            return;
        }

        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        UbahPinRequest request = new UbahPinRequest(pin);
        if (isViewAttached()) {
            getCompositeDisposable().add(
                    getApiSource().getData(url, request, seqNum)
                            .subscribeOn(getSchedulerProvider().single())
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .observeOn(getSchedulerProvider().mainThread())
                            .subscribeWith(new ApiObserver(getView(),seqNum) {
                                @Override
                                protected void onFailureHttp(String type) {
                                    getView().hideProgress();
                                    getView().onException(type);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    getView().hideProgress();
                                    UbahPinResponse ubahPinResponse = response.getData(UbahPinResponse.class);
                                    getView().onSuccess(ubahPinResponse.getReference_number());
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().hideProgress();
//                                    getView().resetInputPin();
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue())) {
                                        getView().onSessionEnd(restResponse.getDesc());
                                    }
                                    else if(restResponse.getCode().equalsIgnoreCase("UB")){
                                        getView().onErrorBlock(restResponse.getData(ErrorResponseNewSkin.class));
                                    }
                                    else if (restResponse.getCode().equalsIgnoreCase("A3"))
                                        getView().onErrorPin(restResponse.getDesc());
                                    else if (restResponse.getCode().equalsIgnoreCase("A4"))
                                        getView().onExceptionErrorAttemps(restResponse.getData(ErrorResponseNewSkin.class));
                                }
                            })
            );


        }

    }

    @Override
    public void setPin(String pin) {
        this.pin = pin;
    }

}