package id.co.bri.brimo.presenters.cardless;

import id.co.bri.brimo.contract.IPresenter.cardless.ISetorTunaiPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.base.IBaseFormView;
import id.co.bri.brimo.contract.IView.cardless.ISetorTunaiView;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.presenters.base.BaseFormPresenter;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.schedulers.Schedulers;

public class SetorTunaiPresenter <V extends IMvpView & IBaseFormView & ISetorTunaiView> extends BaseFormPresenter<V> implements ISetorTunaiPresenter<V> {

    protected String formUrl;
    protected Object deleteRequest = null;

    public SetorTunaiPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, categoryPfmSource, transaksiPfmSource, anggaranPfmSource);
    }

    @Override
    public void onGetBatalSetor() {
        if (formUrl == null || !isViewAttached()) {
            return;
        }
        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        getCompositeDisposable()
                .add(getApiSource().getDataForm(formUrl, seqNum)
                        .subscribeOn(Schedulers.io())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(new ApiObserver(getView(),seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().hideProgress();
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                //TO-DO onSuccess
                                getView().hideProgress();
                                if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SUCCESS.getValue())) {
                                    getView().onSuccesGetBatal(response.getDesc());
                                }
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                    getView().onSessionEnd(restResponse.getDesc());
                                else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_12.getValue()))
                                    getView().onException12(restResponse.getDesc());
                                else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.getValue()))
                                    getView().onException93(restResponse.getDesc());
                                else
                                    getView().onException(restResponse.getDesc());

                            }
                        }));

    }

    @Override
    public void onGetBatalSetorFm(boolean isFromFastMenu) {
        if (formUrl == null || !isViewAttached()) {
            return;
        }
        if (!isFromFastMenu)
            return;

        deleteRequest = getFastMenuRequest();

        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        getCompositeDisposable()
                .add(getApiSource().getData(formUrl, deleteRequest, seqNum)
                        .subscribeOn(Schedulers.io())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(new ApiObserver(getView(),seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().hideProgress();
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                //TO-DO onSuccess
                                getView().hideProgress();
                                if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SUCCESS.getValue())) {
                                    getView().onSuccesGetBatal(response.getDesc());
                                }
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                    getView().onSessionEnd(restResponse.getDesc());
                                else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_12.getValue()))
                                    getView().onException12(restResponse.getDesc());
                                else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.getValue()))
                                    getView().onException93(restResponse.getDesc());
                                else
                                    getView().onException(restResponse.getDesc());

                            }
                        }));

    }

    @Override
    public void setFormUrl(String formUrl) {
        this.formUrl = formUrl;
    }

}