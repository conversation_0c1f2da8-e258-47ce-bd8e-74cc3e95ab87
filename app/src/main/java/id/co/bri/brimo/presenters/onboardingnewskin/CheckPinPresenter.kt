package id.co.bri.brimo.presenters.onboardingnewskin

import id.co.bri.brimo.contract.IPresenter.newskinonboarding.ICheckPinPresenter
import id.co.bri.brimo.contract.IView.newskinonboarding.ICheckPinView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.domain.config.AppConfig
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.onboardingrevamp.PinRequest
import id.co.bri.brimo.models.apimodel.response.onboardingrevamp.PinCheckSuccessData
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.Schedulers
import java.util.concurrent.TimeUnit

class CheckPinPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    private val brImoPrefRepository: BRImoPrefSource,
    private val apiSource: ApiSource
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    brImoPrefRepository,
    apiSource,
    null
), ICheckPinPresenter<V> where V : ICheckPinView {

    private var urlValidatePin = ""
    private var errorMess = ""
    override fun setValidatePinUrl(url: String) {
        urlValidatePin = url
    }

    override fun isInputValid(pin: String): Boolean {
        if (pin.length != 6) return false
        if (!pin.matches(Regex("^\\d{6}$"))) return false
        return true
    }

    override fun confirmPin(pin: String) {
    }

    override fun onCheckPinSubmit(pin: String) {
        if (!isViewAttached || !isInputValid(pin) || urlValidatePin.isEmpty()) return

        view.showProgress()

        val seqNum = brImoPrefRepository.seqNumber

        val requestBody = PinRequest(pin = pin)

        compositeDisposable.add(
            apiSource.getData(urlValidatePin, requestBody, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onApiCallSuccess(response: RestResponse) {
                        view.hideProgress()
                        val checkResponse = response.getData(PinCheckSuccessData::class.java)
                        getView().onPinValid(checkResponse.referenceNumber, pin = pin)

                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        view.hideProgress()
                        val res = restResponse.getData(PinCheckSuccessData::class.java)
                        errorMess = restResponse.desc
                        getView().onPinCheckFailed(res, restResponse.code)
                    }

                    override fun onFailureHttp(errorMessage: String) {
                        view.hideProgress()
                        getView().onFailure(true)
                    }
                })
        )
    }
}
