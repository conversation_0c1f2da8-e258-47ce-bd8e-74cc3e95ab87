package id.co.bri.brimo.presenters.emas

import id.co.bri.brimo.contract.IPresenter.emas.IProductBriefEmasPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.emas.IProductBriefEmasView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimo.data.repository.category.CategoryPfmSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.config.AppConfig
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.SafetyPusatBantuanRequest
import id.co.bri.brimo.models.apimodel.response.onExceptionWH
import id.co.bri.brimo.models.apimodel.response.QuestionResponse
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.emas.PersonalDataResponse
import id.co.bri.brimo.models.apimodel.response.emas.SafetyModeDrawerResponse
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.disposables.Disposable
import io.reactivex.schedulers.Schedulers
import java.util.concurrent.TimeUnit

class ProductBriefEmasPresenter<V>(schedulerProvider: SchedulerProvider?,
                                   compositeDisposable: CompositeDisposable?,
                                   mBRImoPrefRepository: BRImoPrefSource?, apiSource: ApiSource?,
                                   categoryPfmSource: CategoryPfmSource?,
                                   transaksiPfmSource: TransaksiPfmSource?,
                                   anggaranPfmSource: AnggaranPfmSource?) : MvpPresenter<V>(
    schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource
),
        IProductBriefEmasPresenter<V> where V : IMvpView?, V : IProductBriefEmasView {

    var mUrlData : String? = null
    var mUrlPusatBantuan : String? = null

    override fun setUrl(url: String) {
        this.mUrlData = url
    }

    override fun setUrlPusatBantuan(urlPusatBantuan: String) {
        this.mUrlPusatBantuan = urlPusatBantuan
    }

    override fun getDataProductBriefEmas() {
        view.showProgress()
        val seqNum = brImoPrefRepository.seqNumber
        val disposable: Disposable =
                apiSource.getData(mUrlData, "", seqNum) //function(param)
                        .subscribeOn(schedulerProvider.io())
                        .observeOn(schedulerProvider.mainThread())
                        .subscribeWith(object : ApiObserver(view, seqNum) {
                            override fun onFailureHttp(errorMessage: String) {
                                getView().onException(errorMessage)
                            }

                            override fun onApiCallSuccess(response: RestResponse) {
                                getView().hideProgress()
                                if (response.code.equals(RestResponse.ResponseCodeEnum.RC_SUCCESS.value, ignoreCase = true)) {
                                    val response = response.getData(PersonalDataResponse::class.java)
                                    getView().onSuccessGetPersonalData(response)
                                }
                                else if (response.code.equals(RestResponse.ResponseCodeEnum.RC_SM.value, ignoreCase = true)) {
                                    val response = restResponse.getData(
                                            SafetyModeDrawerResponse::class.java
                                    )
                                    getView()!!.exceptionSM(response)
                                }
                                else if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_02.value, ignoreCase = true)) {
                                    val onOnExceptionWH: onExceptionWH =
                                            restResponse.getData(
                                                    onExceptionWH::class.java
                                            )
                                    getView().exceptionEODEOM(onOnExceptionWH)
                                }

                            }

                            override fun onApiCallError(restResponse: RestResponse) {
                                getView().hideProgress()
                                if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SM.value, ignoreCase = true)) {
                                    val response = restResponse.getData(
                                            SafetyModeDrawerResponse::class.java
                                    )
                                    getView()!!.exceptionSM(response)
                                }
                                else if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_02.value, ignoreCase = true)) {
                                    val onOnExceptionWH: onExceptionWH =
                                            restResponse.getData(
                                                    onExceptionWH::class.java
                                            )
                                    getView().exceptionEODEOM(onOnExceptionWH)
                                }else {
                                    getView()!!.onException(restResponse.desc)
                                }
                            }
                        })
        compositeDisposable.add(disposable)
    }

    override fun getPusatBantuanSafety(code: String) {
        if (mUrlPusatBantuan == null || !isViewAttached) {
//            Log.d(TAG, "getDataInquiry: view atau inquiry urlInformasi null");
            return
        }

        view!!.showProgress()
        val safetyPusatBantuanRequest = SafetyPusatBantuanRequest(code)
        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable
                .add(
                        apiSource.getData(mUrlPusatBantuan, safetyPusatBantuanRequest, seqNum)
                                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                                .subscribeOn(Schedulers.io())
                                .observeOn(AndroidSchedulers.mainThread())
                                .subscribeWith(object : ApiObserver(view, seqNum) {
                                    override fun onFailureHttp(errorMessage: String) {
                                        getView()!!.hideProgress()
                                        getView()!!.onException(errorMessage)
                                    }

                                    override fun onApiCallSuccess(response: RestResponse) {
                                        //TO-DO onSuccess
                                        getView()!!.hideProgress()
                                        val topicQuestionResponse = response.getData(
                                                QuestionResponse::class.java
                                        )
                                        if (mUrlPusatBantuan != null) getView()!!.onSuccessGetPusatBantuan(
                                                topicQuestionResponse
                                        )
                                    }

                                    override fun onApiCallError(restResponse: RestResponse) {
                                        getView()!!.hideProgress()

                                        if (restResponse.code.equals(
                                                        RestResponse.ResponseCodeEnum.RC_SESSION_END.value,
                                                        ignoreCase = true
                                                )
                                        ) getView()!!.onSessionEnd(restResponse.desc) else getView()!!.onException(
                                                restResponse.desc
                                        )
                                    }
                                })
                )
    }


}