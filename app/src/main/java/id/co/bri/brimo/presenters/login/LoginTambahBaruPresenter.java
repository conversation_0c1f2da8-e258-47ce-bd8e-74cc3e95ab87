package id.co.bri.brimo.presenters.login;

import id.co.bri.brimo.contract.IPresenter.login.ILoginTambahBaruPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.login.ILoginTambahBaruView;
import id.co.bri.brimo.contract.IView.login.ILoginView;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;

import io.reactivex.disposables.CompositeDisposable;

public class LoginTambahBaruPresenter<V extends IMvpView & ILoginView & ILoginTambahBaruView> extends LoginActivityPresenter<V> implements ILoginTambahBaruPresenter<V> {

    public LoginTambahBaruPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, categoryPfmSource, transaksiPfmSource, anggaranPfmSource);
    }
}