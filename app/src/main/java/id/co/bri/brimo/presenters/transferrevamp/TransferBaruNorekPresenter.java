package id.co.bri.brimo.presenters.transferrevamp;

import id.co.bri.brimo.contract.IPresenter.transferrevamp.ITransferBaruNorekPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.transferrevamp.ITransferBaruNorekView;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.config.AppConfig;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.apimodel.request.InquiryTransferAliasRevampRequest;
import id.co.bri.brimo.models.apimodel.request.revamptransfer.SavedDataInquiryRequest;
import id.co.bri.brimo.models.apimodel.response.InquiryTransferRevampResponse;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.models.apimodel.response.transferrevamp.SavedDataExistResponse;
import id.co.bri.brimo.models.apimodel.response.transferrevamp.SavedDataResponse;
import id.co.bri.brimo.presenters.MvpPresenter;

import java.util.concurrent.TimeUnit;

import io.reactivex.disposables.CompositeDisposable;

public class TransferBaruNorekPresenter<V extends IMvpView & ITransferBaruNorekView> extends MvpPresenter<V> implements ITransferBaruNorekPresenter<V> {

    protected String inquiryUrl;
    public TransferBaruNorekPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void getDataInquiry(InquiryTransferAliasRevampRequest inquiryTransferAliasRevampRequest) {
        if (inquiryUrl == null || !isViewAttached()) {
            return;
        }

        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();

        getCompositeDisposable()
                .add(getApiSource().getData(inquiryUrl, inquiryTransferAliasRevampRequest,seqNum)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(getSchedulerProvider().io())
                        .observeOn(getSchedulerProvider().mainThread())
                        .subscribeWith(new ApiObserver(getView(),seqNum) {
                            @Override
                            public void addOnPropertyChangedCallback(OnPropertyChangedCallback callback) {

                            }

                            @Override
                            public void removeOnPropertyChangedCallback(OnPropertyChangedCallback callback) {

                            }

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().hideProgress();
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                getView().hideProgress();
                                InquiryTransferRevampResponse responsebriva = restResponse.getData(InquiryTransferRevampResponse.class);
                                getView().onSuccessGetInquiry(responsebriva);
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();

                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_ACCOUNT_NOT_FOUND.getValue()))
                                    getView().onException14(restResponse.getDesc(), inquiryTransferAliasRevampRequest.getAccount());
                                else
                                    onApiError(restResponse);
                            }
                        }));
    }

    @Override
    public void getDataSavedListInquiry(SavedDataInquiryRequest savedDataInquiryRequest) {
        if (inquiryUrl == null || !isViewAttached()) {
            return;
        }

        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();

        getCompositeDisposable()
                .add(getApiSource().getData(inquiryUrl, savedDataInquiryRequest, seqNum)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(getSchedulerProvider().io())
                        .observeOn(getSchedulerProvider().mainThread())
                        .subscribeWith(new ApiObserver(getView(), seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().hideProgress();
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                getView().hideProgress();
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_01.getValue())) {
                                    SavedDataExistResponse resData = response.getData(SavedDataExistResponse.class);
                                    getView().onExceptionAlreadySaved(resData.getTitle(), resData.getSubtitle());
                                } else {
                                    SavedDataResponse resData = response.getData(SavedDataResponse.class);
                                    getView().onSuccessGetSavedDataInquiry(resData);
                                }
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                onApiError(restResponse);
                            }
                        })
                );
    }

    @Override
    public void setInquiryUrl(String url) {
        this.inquiryUrl = url;
    }
}