package id.co.bri.brimo.presenters.transfer;

import id.co.bri.brimo.contract.IPresenter.transfer.ITambahDaftarTransferPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.transfer.ITambahDaftarTransferView;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.AccountModel;
import id.co.bri.brimo.models.apimodel.request.InquiryTransferRequest;
import id.co.bri.brimo.models.apimodel.response.InquiryTransferResponse;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.presenters.MvpPresenter;

import java.util.ArrayList;
import java.util.List;

import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.disposables.Disposable;

/**
 * Created by FNS
 */

public class TambahDaftarTransferPresenter<V extends IMvpView & ITambahDaftarTransferView> extends MvpPresenter<V> implements ITambahDaftarTransferPresenter<V> {

    private List<AccountModel> accountModelListFailed = new ArrayList<>();
    private String urlInquiry;

    public TambahDaftarTransferPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }


    @Override
    public void getData(String bankCode, String account) {
        if (getView() != null) {
            //initiate param with getter from view
            getView().showProgress();
            String seqNum = getBRImoPrefRepository().getSeqNumber();
            InquiryTransferRequest request = new InquiryTransferRequest(bankCode, account);
            Disposable disposable = getApiSource().getData(urlInquiry, request, seqNum)//function(param)
                    .subscribeOn(getSchedulerProvider().io())
                    .observeOn(getSchedulerProvider().mainThread())
                    .subscribeWith(new ApiObserver(getView(),seqNum) {

                        @Override
                        protected void onFailureHttp(String errorMessage) {
                            getView().hideProgress();
                            getView().onException(errorMessage);
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            getView().hideProgress();
                            InquiryTransferResponse inquiryTransferResponse = response.getData(InquiryTransferResponse.class);
                            getView().onSuccessGetData(inquiryTransferResponse);
//                            getSaldo(inquiryTransferResponse.getAccountModelList());
                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            getView().hideProgress();
                            if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                getView().onSessionEnd(restResponse.getDesc());
                            else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_99.getValue()))
                                getView().onException99(restResponse.getDesc());
                            else
                                getView().onException(restResponse.getDesc());

                        }
                    });

            getCompositeDisposable().add(disposable);
        }
    }

}