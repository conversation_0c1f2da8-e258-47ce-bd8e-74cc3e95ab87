package id.co.bri.brimo.presenters.brivarevamp

import id.co.bri.brimo.contract.IPresenter.brivarevamp.ITambahDaftarBrivaRevampPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.brivarevamp.ITambahDaftarBrivaRevampView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimo.data.repository.category.CategoryPfmSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.config.AppConfig
import id.co.bri.brimo.domain.config.Constant

import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.InquiryBrivaRequest
import id.co.bri.brimo.models.apimodel.response.InquiryBrivaRevampResponse
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.disposables.CompositeDisposable
import java.util.concurrent.TimeUnit

class TambahDaftarBrivaRevampPresenter<V>(
        schedulerProvider: SchedulerProvider,
        compositeDisposable: CompositeDisposable,
        mBRImoPrefRepository: BRImoPrefSource,
        apiSource: ApiSource,
        categoryPfmSource: CategoryPfmSource,
        transaksiPfmSource: TransaksiPfmSource,
        anggaranPfmSource: AnggaranPfmSource
) : MvpPresenter<V>(
    schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource
),
        ITambahDaftarBrivaRevampPresenter<V> where V : IMvpView, V : ITambahDaftarBrivaRevampView {

    private lateinit var urlInquiry: String

    override fun setUrlInquiry(urlInquiry: String) {
        this.urlInquiry = urlInquiry
    }

    override fun getDataInquiry(request: InquiryBrivaRequest) {
        if (urlInquiry.isEmpty() && !isViewAttached) return

        view.showProgress()

        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable
                .add(
                        apiSource.getData(urlInquiry, request, seqNum)
                                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                                .subscribeOn(schedulerProvider.io())
                                .observeOn(schedulerProvider.mainThread())
                                .subscribeWith(object : ApiObserver(view, seqNum) {
                                    override fun onFailureHttp(errorMessage: String) {
                                        getView().hideProgress()
                                        getView().onException(errorMessage)
                                    }

                                    override fun onApiCallSuccess(response: RestResponse) {
                                        //TO-DO onSuccess
                                        getView().hideProgress()
                                        val responsebriva = response.getData(
                                                InquiryBrivaRevampResponse::class.java
                                        )
                                        getView().onSuccessGetInquiry(
                                                responsebriva
                                        )
                                    }

                                    override fun onApiCallError(restResponse: RestResponse) {
                                        getView().hideProgress()
                                        if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value))
                                            getView().onSessionEnd(restResponse.desc)
                                        else if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_ACCOUNT_NOT_FOUND.value))
                                            getView().onException14(restResponse.desc)
                                        else if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_58.value))
                                            getView().onException58(restResponse.desc)
                                        else if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_59.value))
                                            getView().onException59(restResponse.desc)
                                        else if(restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_88_BILL_ALREADY_PAID.value))
                                            getView().onException88(restResponse.desc)
                                        else if(restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_60.value))
                                            getView().onException60(restResponse.desc)
                                        else if (restResponse.code.equals(Constant.RE_EXPIRED_BILL))
                                            getView().onExpiredBill(restResponse.desc)
                                        else getView().onException(restResponse.desc)
                                    }
                                })
                )
    }

}