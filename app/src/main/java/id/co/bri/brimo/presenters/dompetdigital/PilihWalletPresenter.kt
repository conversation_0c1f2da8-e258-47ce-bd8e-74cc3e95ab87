package id.co.bri.brimo.presenters.dompetdigital

import id.co.bri.brimo.contract.IPresenter.dompetdigitalrevamp.IPilihWalletPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.dompetdigitalrevamp.IPilihWalletView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimo.data.repository.category.CategoryPfmSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.disposables.CompositeDisposable

class PilihWalletPresenter <V>(schedulerProvider: SchedulerProvider?,
                               compositeDisposable: CompositeDisposable?,
                               mBRImoPrefRepository: BRImoPrefSource?, apiSource: ApiSource?,
                               categoryPfmSource: CategoryPfmSource?,
                               transaksiPfmSource: TransaksiPfmSource?,
                               anggaranPfmSource: AnggaranPfmSource?) : MvpPresenter<V>(
    schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource
),
    IPilihWalletPresenter<V> where V : IMvpView?, V : IPilihWalletView? {


}