package id.co.bri.brimo.presenters.rdnrevamp

import id.co.bri.brimo.contract.IPresenter.rdnrevamp.IInquiryWithdrawRdnPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.rdnrevamp.IInquiryWithdrawRdnView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimo.data.repository.category.CategoryPfmSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.rdn.RdnConfirmationWithdrawRequest
import id.co.bri.brimo.models.apimodel.response.GeneralConfirmationResponse
import id.co.bri.brimo.models.apimodel.response.GeneralResponse
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.disposables.Disposable

class InquiryWithdrawRdnPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBRImoPrefRepository: BRImoPrefSource,
    apiSource: ApiSource,
    transaksiPfmSource: TransaksiPfmSource
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    mBRImoPrefRepository,
    apiSource,
    transaksiPfmSource
), IInquiryWithdrawRdnPresenter<V> where V : IMvpView, V : IInquiryWithdrawRdnView {
    private var urlConfirmation: String? = null

    override fun setUrlConfirmationWithdraw(urlConfirmation: String) {
        this.urlConfirmation = urlConfirmation
    }

    override fun getConfirmation(request: RdnConfirmationWithdrawRequest) {
        urlConfirmation.let { urlString ->
            view.showProgress()
            val seqNum = brImoPrefRepository.seqNumber
            val disposable: Disposable =
                apiSource.getData(urlString, request, seqNum)
                    .subscribeOn(schedulerProvider.io())
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView().hideProgress()
                            getView().onException(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            getView().hideProgress()
                            val detailResponse = response.getData(GeneralConfirmationResponse::class.java)
                            if (detailResponse.confirmationStatus == RestResponse.ResponseCodeEnum.RC_LIMIT_EXCEEDED.value || detailResponse.confirmationStatus == RestResponse.ResponseCodeEnum.RC_SM.value) {
                                getView().onExceptionLimitExceedGetConfirmationWithdraw(detailResponse)
                            } else {
                                getView().onSuccessGetConfirmationWithdraw(detailResponse)
                            }
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView().hideProgress()
                            when (restResponse.code) {
                                RestResponse.ResponseCodeEnum.RC_LIMIT_EXCEEDED.value, RestResponse.ResponseCodeEnum.RC_SM.value -> getView().onExceptionLimitExceed(restResponse.getData(GeneralResponse::class.java))
                                RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.value -> getView().onExceptionTrxExpired(restResponse.desc)
                                else -> getView().onException(restResponse.desc)
                            }
                        }
                    })
            compositeDisposable.add(disposable)
        }
    }

}