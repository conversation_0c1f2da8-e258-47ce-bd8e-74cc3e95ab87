package id.co.bri.brimo.presenters.kesehatan

import android.content.Context
import id.co.bri.brimo.contract.IPresenter.kesehatan.IKonfirmasiKesehatanLifestylePresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.kesehatan.IKonfirmasiKesehatanLifestyleView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimo.data.repository.category.CategoryPfmSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.disposables.CompositeDisposable

class KonfirmasiKesehatanLifestylePresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBRImoPrefRepository: BRImoPrefSource,
    apiSource: ApiSource,
    categoryPfmSource: CategoryPfmSource,
    transaksiPfmSource: TransaksiPfmSource,
    anggaranPfmSource: AnggaranPfmSource,
    val context: Context
) : MvpPresenter<V>(
    schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource
), IKonfirmasiKesehatanLifestylePresenter<V> where V : IMvpView, V: IKonfirmasiKesehatanLifestyleView {
    override fun getAccountDefault(): String = brImoPrefRepository.accountDefault

    override fun getSaldoRekeningUtama(): String = brImoPrefRepository.saldoRekeningUtama

    private fun getDefaultSaldo() {
        var saldo = 0.0
        val saldoText = brImoPrefRepository.saldoRekeningUtamaString
        if (saldoText != "") {
            val cleanString = saldoText.replace("[,.]".toRegex(), "")
            try {
                saldo = cleanString.toDouble() / 100
            } catch (e: Exception) {
                // do nothing
            }
        }

        val saldoHold = brImoPrefRepository.saldoHold
        val defaultAcc = brImoPrefRepository.accountDefault
        val saldoString = brImoPrefRepository.saldoRekeningUtamaString
        view.setDefaultSaldo(saldo, saldoString, defaultAcc, saldoHold)
    }

    override fun start() {
        super.start()
        getDefaultSaldo()
    }
}