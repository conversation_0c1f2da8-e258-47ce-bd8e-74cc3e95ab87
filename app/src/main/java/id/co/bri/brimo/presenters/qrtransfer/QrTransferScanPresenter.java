package id.co.bri.brimo.presenters.qrtransfer;

import java.util.concurrent.TimeUnit;

import id.co.bri.brimo.contract.IPresenter.qrtransfer.IQrTransferScanPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.qrtransfer.IQrTransferScanView;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.config.AppConfig;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.apimodel.request.QrTransferInquiryRequest;
import id.co.bri.brimo.models.apimodel.response.GeneralInquiryResponse;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.presenters.MvpPresenter;
import io.reactivex.disposables.CompositeDisposable;

public class QrTransferScanPresenter<V extends IMvpView & IQrTransferScanView>
        extends MvpPresenter<V> implements IQrTransferScanPresenter<V> {

    protected String urlInquiry;
    protected String urlKonfirmasi;
    protected String urlPayment;

    public QrTransferScanPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void setInquiryUrl(String urlInquiry) {
        this.urlInquiry = urlInquiry;
    }

    @Override
    public void setKonfirmasiUrl(String urlKonfirmasi) {
        this.urlKonfirmasi = urlKonfirmasi;
    }

    @Override
    public void setPaymentUrl(String urlPayment) {
        this.urlPayment = urlPayment;
    }

    @Override
    public void geDataInquiry(String qrCode) {
        if (isViewAttached()) {

            getView().showProgress();
            QrTransferInquiryRequest request = new QrTransferInquiryRequest(qrCode);
            String seqNum = getBRImoPrefRepository().getSeqNumber();
            getCompositeDisposable().add(
                    getApiSource().getData(urlInquiry, request, seqNum)
                            .subscribeOn(getSchedulerProvider().io())
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .observeOn(getSchedulerProvider().mainThread())
                            .subscribeWith(new ApiObserver(getView(),seqNum) {
                                @Override
                                protected void onFailureHttp(String type) {
                                    getView().hideProgress();
                                    getView().onException(type);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    getView().hideProgress();
                                    GeneralInquiryResponse generalResponse = response.getData(GeneralInquiryResponse.class);

                                    if (urlKonfirmasi != null && urlPayment != null)
                                        getView().onSuccessGetInquiry(generalResponse, urlKonfirmasi, urlPayment, false);
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().hideProgress();
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                        getView().onSessionEnd(restResponse.getDesc());
                                    else
                                        getView().onException(restResponse.getDesc());
                                }
                            })
            );
        }
    }
}