package id.co.bri.brimo.presenters.ssc

import id.co.bri.brimo.contract.IPresenter.ssc.IBaseComplaintPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.ssc.IBaseComplaintView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimo.data.repository.category.CategoryPfmSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.config.AppConfig

import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.CekNoRekRequest
import id.co.bri.brimo.models.apimodel.request.RevokeSessionRequest
import id.co.bri.brimo.models.apimodel.request.ssc.GetBankTfInternasionalReq
import id.co.bri.brimo.models.apimodel.response.ssc.ComplaintTicketCreateRes
import id.co.bri.brimo.models.apimodel.response.DestinationNameRes
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.TokenVoipResponse
import id.co.bri.brimo.models.apimodel.response.ssc.GetBankTfInternasionalRes
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.Schedulers
import java.util.concurrent.TimeUnit

class BaseComplaintPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBRImoPrefRepository: BRImoPrefSource,
    apiSource: ApiSource,
    categoryPfmSource: CategoryPfmSource,
    transaksiPfmSource: TransaksiPfmSource,
    anggaranPfmSource: AnggaranPfmSource
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    mBRImoPrefRepository,
    apiSource,
    transaksiPfmSource
), IBaseComplaintPresenter<V> where V : IMvpView, V : IBaseComplaintView {

    private var urlCreateTicket = ""
    private var urlCheckNoRek = ""
    private var urlVoip = ""
    private var urlRevoke = ""
    private var urlBankListTfInternational = ""

    override fun setUrlCreateTicket(url: String) {
        urlCreateTicket = url
    }

    override fun setUrlCheckAccountNumber(url: String) {
        urlCheckNoRek = url
    }

    override fun setUrlVoip(url: String) {
        urlVoip = url
    }

    override fun setUrlRevoke(url: String) {
        urlRevoke = url
    }

    override fun setUrlBankListInternational(url: String) {
        urlBankListTfInternational = url
    }

    override fun sendCreateTicket(request: Any) {
        if (urlCreateTicket.isEmpty() || !isViewAttached) return

        view.showProgress()
        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable.add(
            apiSource.getData(urlCreateTicket, request, seqNum).subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView().hideProgress()
                        getView().onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView().hideProgress()
                        val ticketCreateRes = response.getData(
                            ComplaintTicketCreateRes::class.java
                        )
                        getView().onSuccessCreateTicket(ticketCreateRes)
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView().hideProgress()
                        if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value))
                            getView().onSessionEnd(restResponse.desc)
                        else if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.value)) getView().onException93(
                            restResponse.desc
                        )
                        else getView().onException(restResponse.desc)
                    }
                })
        )
    }

    override fun sendCheckAccountNumber(request: CekNoRekRequest) {
        if (urlCheckNoRek.isEmpty() || !isViewAttached) return

        view.showProgress()
        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable.add(
            apiSource.getData(urlCheckNoRek, request, seqNum).subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView().hideProgress()
                        getView().onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView().hideProgress()
                        val destinationRes = response.getData(
                            DestinationNameRes::class.java
                        )
                        getView().onSuccessAccountNumber(destinationRes.destinationName)
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView().hideProgress()
                        if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value)) getView().onSessionEnd(restResponse.desc)
                        else if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.value)) getView().onException93(
                            restResponse.desc
                        )
                        else getView().onExceptionAccountNumber(restResponse.desc)
                    }
                })
        )
    }

    override fun getBankList(request: GetBankTfInternasionalReq) {
        if (urlBankListTfInternational.isEmpty() || !isViewAttached) return

        view.showProgress()
        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable.add(
                apiSource.getData(urlBankListTfInternational, request, seqNum).subscribeOn(Schedulers.io())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(object : ApiObserver(view, seqNum) {
                            override fun onFailureHttp(errorMessage: String) {
                                getView().hideProgress()
                                getView().onException(errorMessage)
                            }

                            override fun onApiCallSuccess(response: RestResponse) {
                                getView().hideProgress()
                                val bankTfInternasionalRes = response.getData(
                                        GetBankTfInternasionalRes::class.java
                                )
                                getView().onSuccessGetBankList(bankTfInternasionalRes)
                            }

                            override fun onApiCallError(restResponse: RestResponse) {
                                getView().hideProgress()
                                if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value)) getView().onSessionEnd(restResponse.desc)
                                else if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.value)) getView().onException93(
                                        restResponse.desc
                                )
                                else getView().onExceptionAccountNumber(restResponse.desc)
                            }
                        })
        )
    }

    override fun getVoip() {
        if (urlVoip.isEmpty() || !isViewAttached) return

        view.showProgress()
        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable.add(
            apiSource.getDataTanpaRequest(urlVoip, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(Schedulers.io()).observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String?) {
                        getView().hideProgress()
                        getView().onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView().hideProgress()
                        val voipResponse = response.getData(
                            TokenVoipResponse::class.java
                        )
                        getView().onSuccessVoip(voipResponse)
                    }

                    override fun onApiCallError(restResponse: RestResponse?) {
                        onApiError(restResponse)
                    }
                })
        )
    }

    override fun getInitiateResource() {
        view.onInitiateResourceSuccess(brImoPrefRepository.username, brImoPrefRepository.tokenKey)
    }

    override fun revokeSession(request: RevokeSessionRequest?) {
        if (urlRevoke.isEmpty() || !isViewAttached) return

        view.showProgress()
        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable.add(
            apiSource.getData(urlRevoke, request, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(Schedulers.io()).observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView().hideProgress()
                        getView().onCallbackVoip()
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView().hideProgress()
                        getView().onCallbackVoip()
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView().onCallbackVoip()
                    }
                })
        )
    }
}