package id.co.bri.brimo.presenters.registrasirevamp

import id.co.bri.brimo.contract.IPresenter.registrasirevamp.IRegistrasiConfirmPinPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.registrasirevamp.IRegistrasiConfirmPinView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimo.data.repository.category.CategoryPfmSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.config.AppConfig
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.registrasi.RegisPinRequest
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.registrasi.RegisOtpPrivyRes
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.Schedulers
import java.util.concurrent.TimeUnit


class RegistrasiConfirmPinPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBRImoPrefRepository: BRImoPrefSource,
    apiSource: ApiSource,
    categoryPfmSource: CategoryPfmSource,
    transaksiPfmSource: TransaksiPfmSource,
    anggaranPfmSource: AnggaranPfmSource
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    mBRImoPrefRepository,
    apiSource,
    transaksiPfmSource
), IRegistrasiConfirmPinPresenter<V> where V : IMvpView, V : IRegistrasiConfirmPinView? {

    private var urlPin: String? = null

    override fun getDeviceId(): String {
        return brImoPrefRepository.deviceId
    }

    override fun setUrlConfirmPin(url: String) {
        urlPin = url
    }

    override fun sendConfirmPin(regisPinRequest: RegisPinRequest) {
        if (urlPin != null && isViewAttached) {

            view.showProgress()
            val seqNum = brImoPrefRepository.seqNumber

            compositeDisposable.add(
                apiSource.getData(urlPin, regisPinRequest, seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView().hideProgress()
                            getView().onExceptionNoBackAction(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            getView().hideProgress()
                            if (response.code.equals(RestResponse.ResponseCodeEnum.RC_SUCCESS.value)) {
                                val regisOtpResponse =
                                    response.getData(RegisOtpPrivyRes::class.java)
                                brImoPrefRepository.saveUsername(regisOtpResponse.username)
                                brImoPrefRepository.saveTokenKey(regisOtpResponse.tokenKey)
                                brImoPrefRepository.saveUserType(Constant.IB_TYPE)
                                updateLoginFlag(true)
                                getView().onSuccessDashboard(regisOtpResponse)
                            } else {
                                getView().onSuccessLogin()
                            }
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView().deleteAllPin()
                            getView().hideProgress()
                            if(restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_STATUS_NOT_MATCH.value))
                                getView().onExceptionStatusNotMatch()
                            else getView().onExceptionRevamp(restResponse.desc)
                        }
                    })
            )
        }
    }
}