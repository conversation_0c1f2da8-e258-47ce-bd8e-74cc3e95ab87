package id.co.bri.brimo.presenters.rdnrevamp

import id.co.bri.brimo.contract.IPresenter.rdnrevamp.IDashboardRdnRevampPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.rdnrevamp.IDashboardRdnRevampView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimo.data.repository.category.CategoryPfmSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.config.AppConfig
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.OnboardingRDNSBNRequest
import id.co.bri.brimo.models.apimodel.request.rdn.InquiryRdnRequest
import id.co.bri.brimo.models.apimodel.request.rdn.RdnInquiryWithdrawRequest
import id.co.bri.brimo.models.apimodel.request.rdn.RdnNewDetailRequest
import id.co.bri.brimo.models.apimodel.response.GeneralResponse
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.rdn.InquiryRdnResponse
import id.co.bri.brimo.models.apimodel.response.rdn.RdnOnBoardingResponse
import id.co.bri.brimo.models.apimodel.response.rdn.RdnOnCheckpointResponse
import id.co.bri.brimo.models.apimodel.response.rdnrevamp.RdnInquiryWithdrawResponse
import id.co.bri.brimo.models.apimodel.response.rdnrevamp.RdnNewDashboardResponse
import id.co.bri.brimo.models.apimodel.response.rdnrevamp.RdnNewDetailResponse
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.disposables.Disposable
import io.reactivex.schedulers.Schedulers
import java.util.concurrent.TimeUnit

class DashboardRdnRevampPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBRImoPrefRepository: BRImoPrefSource,
    apiSource: ApiSource,
    transaksiPfmSource: TransaksiPfmSource
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    mBRImoPrefRepository,
    apiSource,
    transaksiPfmSource
), IDashboardRdnRevampPresenter<V> where V : IMvpView, V : IDashboardRdnRevampView {
    private var urlGetDataDashboard: String? = null
    private var urlGetDataDetail: String? = null
    private var urlWithdrawRdn: String? = null
    private var urlRegisterRdn: String? = null
    private var urlTopUpRdn: String? = null

    override fun start() {
        getDataDashboardRevamp()
    }

    override fun setUrlDashboardRevamp(urlDashboard: String) {
        urlGetDataDashboard = urlDashboard
    }

    override fun setUrlDetailRdn(urlDetail: String) {
        urlGetDataDetail = urlDetail
    }

    override fun setUrlWithdrawRdn(urlWithdrawRdn: String) {
        this.urlWithdrawRdn = urlWithdrawRdn
    }

    override fun setUrlRegisterRdn(urlRegisterRdn: String) {
        this.urlRegisterRdn = urlRegisterRdn
    }

    override fun setUrlTopUpRdn(urlTopUpRdn: String) {
        this.urlTopUpRdn = urlTopUpRdn
    }

    override fun setBubbleDashboardRdnRevamp(isShowingBubble: Boolean) {
        brImoPrefRepository.saveBubbleDashboardRdnRevamp(isShowingBubble)
    }

    override fun getBubbleDashboardRdnRevamp(): Boolean {
        return brImoPrefRepository.bubbleDashboardRdnRevamp
    }

    override fun getDataDetailRdn(request: RdnNewDetailRequest) {
        urlGetDataDetail.let { urlString ->
            view.showProgress()
            val seqNum = brImoPrefRepository.seqNumber
            val disposable: Disposable =
                apiSource.getData(urlString, request, seqNum)
                    .subscribeOn(schedulerProvider.io())
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView().hideProgress()
                            getView().onException(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            getView().hideProgress()
                            val detailResponse = response.getData(
                                RdnNewDetailResponse::class.java
                            )
                            getView().onSuccesGetDataDetail(detailResponse, request.peCode)
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView().hideProgress()
                            getView().onException(restResponse.desc)
                        }
                    })
            compositeDisposable.add(disposable)
        }
    }

    override fun getDataDashboardRevamp() {
        urlGetDataDashboard.let { urlString ->
            val seqNum = brImoPrefRepository.seqNumber
            compositeDisposable.add(
                apiSource.getDataTanpaRequest(urlString, seqNum)
                    .subscribeOn(schedulerProvider.single())
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(type: String) {
                            getView().onException(type)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            if (response.code == RestResponse.ResponseCodeEnum.RC_NF.value) {
                                getView().onException(response.desc)
                                return
                            }

                            val dataResponse = response.getData(RdnNewDashboardResponse::class.java)
                            when {
                                dataResponse.rdnAccountStatus == Constant.RE01 && dataResponse.rdnAccounts.isNullOrEmpty() -> getView().onSuccessGetDataDashboardCheckStatus(dataResponse)
                                dataResponse.rdnAccountStatus == Constant.RE02 && dataResponse.rdnAccounts.isNullOrEmpty() -> getView().onSuccesGetDataDashboardOnboarding(dataResponse)
                                else -> getView().onSuccesGetDataDashboard(dataResponse)
                            }
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            if (restResponse.code.equals(Constant.RE_REFRESH, ignoreCase = true))
                                getView().onExceptionTimeout()
                            else
                                getView().onException(restResponse.desc)
                        }
                    })
            )
        }
    }

    override fun getWithdrawRdn(request: RdnInquiryWithdrawRequest) {
        urlWithdrawRdn.let { urlString ->
            view.showProgress()
            val seqNum = brImoPrefRepository.seqNumber
            val disposable: Disposable =
                apiSource.getData(urlString, request, seqNum)
                    .subscribeOn(schedulerProvider.io())
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView().hideProgress()
                            getView().onException(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            getView().hideProgress()
                            val detailResponse = response.getData(RdnInquiryWithdrawResponse::class.java)

                            when (detailResponse.inquiryStatus) {
                                RestResponse.ResponseCodeEnum.RC_SUCCESS.value -> getView().onSuccessGetWithdraw(detailResponse)
                                RestResponse.ResponseCodeEnum.RC_INSUFFICIENT_BALANCE_RDN.value -> getView().onExceptionInsufficientBalanceRdn(detailResponse)
                            }
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView().hideProgress()
                            when (restResponse.code) {
                                RestResponse.ResponseCodeEnum.RC_LIMIT_EXCEEDED.value, RestResponse.ResponseCodeEnum.RC_SM.value -> getView().onExceptionLimitExceed(restResponse.getData(GeneralResponse::class.java))
                                else -> getView().onException(restResponse.desc)
                            }
                        }
                    })
            compositeDisposable.add(disposable)
        }
    }

    override fun getDataRegisterRdn() {
        urlRegisterRdn?.let {
            view.showProgress()
            val request = OnboardingRDNSBNRequest(brImoPrefRepository.firstRdn)
            val seqNum = brImoPrefRepository.seqNumber
            compositeDisposable.add(apiSource.getData(it, request, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView().hideProgress()
                        getView().onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        if (response.code.equals(Constant.RE_SUCCESS, ignoreCase = true)) {
                            val rdnOnBoardingResponse = response.getData(RdnOnBoardingResponse::class.java)
                            getView().onSuccessOnBoardingRegisterRdn(rdnOnBoardingResponse)
                            getView().hideProgress()
                        } else if (response.code.equals(Constant.RE01, ignoreCase = true)) {
                            val rdnOnBoardingResponse = response.getData(RdnOnCheckpointResponse::class.java)
                            getView().onSuccessCheckPointRegisterRdn(rdnOnBoardingResponse)
                            getView().hideProgress()
                        }
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView().hideProgress()
                        getView().onException(restResponse.desc)
                    }
                }))
        }
    }

    override fun getTopUpRdn(request: InquiryRdnRequest) {
        urlTopUpRdn?.let {
            view.showProgress()
            val seqNum = brImoPrefRepository.seqNumber
            compositeDisposable.add(apiSource.getData(it, request, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String?) {
                        getView().hideProgress()
                        getView().onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView().hideProgress()
                        val inquiryRdnResponse = response.getData(InquiryRdnResponse::class.java)
                        getView().onSuccessInquiryTopUpRdn(inquiryRdnResponse)
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView().hideProgress()
                        getView().onException(restResponse.desc)
                    }
                }))
        }
    }
}