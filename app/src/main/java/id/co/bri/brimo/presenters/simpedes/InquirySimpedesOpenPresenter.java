package id.co.bri.brimo.presenters.simpedes;

import java.util.concurrent.TimeUnit;

import id.co.bri.brimo.contract.IPresenter.simpedes.IInquirySimpedesOpenPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.simpedes.IInquirySimpedesOpenView;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.config.AppConfig;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.apimodel.request.KonfirmasiSimpedesOpenRequest;
import id.co.bri.brimo.models.apimodel.response.KonfirmasiRencanaResponse;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.presenters.MvpPresenter;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.schedulers.Schedulers;

public class InquirySimpedesOpenPresenter<V extends IMvpView & IInquirySimpedesOpenView>
        extends MvpPresenter<V> implements IInquirySimpedesOpenPresenter<V> {

    protected String urlConfirm;

    public InquirySimpedesOpenPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void setUrlConfirm(String urlConfirm) {
        this.urlConfirm = urlConfirm;
    }

    @Override
    public void getDataConfirm(String referenceNumber, String accountNumber) {
        if (isViewAttached()) {
            KonfirmasiSimpedesOpenRequest konfirmasiRequest = new KonfirmasiSimpedesOpenRequest(referenceNumber, accountNumber);

            getView().showProgress();
            String seq = getBRImoPrefRepository().getSeqNumber();
            getCompositeDisposable()
                    .add(getApiSource().getData(urlConfirm, konfirmasiRequest, seq)
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .subscribeOn(Schedulers.io())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribeWith(new ApiObserver(getView(),seq) {

                                @Override
                                protected void onFailureHttp(String errorMessage) {
                                    getView().hideProgress();
                                    getView().onException(errorMessage);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    getView().hideProgress();
                                    KonfirmasiRencanaResponse confirmRes = response.getData(KonfirmasiRencanaResponse.class);
                                    getView().getDataSuccessConfirm(confirmRes);
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().hideProgress();
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                        getView().onSessionEnd(restResponse.getDesc());
                                    else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.getValue()))
                                        getView().onException93(restResponse.getDesc());
                                    else
                                        getView().onException(restResponse.getDesc());
                                }
                            }));

        }
    }

    protected void getDefaultSaldo() {
        double saldo = 0.0;
        String saldoText = getBRImoPrefRepository().getSaldoRekeningUtama();
        if (!saldoText.equals("")) {
            saldo = Double.valueOf(saldoText);
        }

        String akunDefault = getBRImoPrefRepository().getAccountDefault();
        String saldoString = getBRImoPrefRepository().getSaldoRekeningUtamaString();
        boolean saldoHold = getBRImoPrefRepository().getSaldoHold();

        getView().setDefaultSaldo(saldo, saldoString, akunDefault, saldoHold);
    }

    @Override
    public void start() {
        super.start();
        getDefaultSaldo();
    }
}