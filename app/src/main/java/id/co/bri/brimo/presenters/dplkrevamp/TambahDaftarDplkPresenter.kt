package id.co.bri.brimo.presenters.dplkrevamp

import id.co.bri.brimo.contract.IPresenter.brivarevamp.ITambahDaftarBrivaRevampPresenter
import id.co.bri.brimo.contract.IPresenter.dplkrevamp.ITambahDaftarDplkPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.brivarevamp.ITambahDaftarBrivaRevampView
import id.co.bri.brimo.contract.IView.dplkrevamp.ITambahDaftarDplkView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimo.data.repository.category.CategoryPfmSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.config.AppConfig
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.InquiryBrivaRequest
import id.co.bri.brimo.models.apimodel.request.InquiryDplkRequest
import id.co.bri.brimo.models.apimodel.response.InquiryBrivaRevampResponse
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.dompetdigitalrevamp.InquiryDompetDigitalResponse
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.disposables.CompositeDisposable
import java.util.concurrent.TimeUnit

class TambahDaftarDplkPresenter <V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBRImoPrefRepository: BRImoPrefSource,
    apiSource: ApiSource,
    categoryPfmSource: CategoryPfmSource,
    transaksiPfmSource: TransaksiPfmSource,
    anggaranPfmSource: AnggaranPfmSource
) : MvpPresenter<V>(
    schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource),
    ITambahDaftarDplkPresenter<V> where V : IMvpView, V : ITambahDaftarDplkView {

    private lateinit var urlInquiry: String

    override fun setUrlInquiry(urlInquiry: String) {
        this.urlInquiry = urlInquiry
    }

    override fun getDataInquiry(request: InquiryDplkRequest) {
        if (urlInquiry.isEmpty() && !isViewAttached) return

        view.showProgress()

        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable
            .add(
                apiSource.getData(urlInquiry, request, seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(schedulerProvider.io())
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView().hideProgress()
                            getView().onException(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            //TO-DO onSuccess
                            getView().hideProgress()
                            val responsebriva = response.getData(
                                InquiryDompetDigitalResponse::class.java
                            )
                            getView().onSuccessGetInquiry(
                                responsebriva
                            )
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            onApiError(restResponse)
                        }
                    })
            )
    }

}