package id.co.bri.brimo.reactnative;

import static id.co.bri.brimo.models.apimodel.response.RestResponse.CODE_SESSION_END;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import androidx.fragment.app.FragmentActivity;
import androidx.fragment.app.FragmentManager;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.brimodsdk.RNBrimodSDKDelegate;
import com.google.gson.Gson;
import com.google.gson.JsonObject;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

import id.co.bri.brimo.R;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.converter.MapperHelper;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.domain.helpers.GeneralHelperNewSkin;
import id.co.bri.brimo.domain.helpers.BrimoGeneralErrorView;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.security.MyCryptStatic;
import id.co.bri.brimo.ui.activities.DashboardIBActivity;
import id.co.bri.brimo.ui.activities.ReactNativeActivity;
import id.co.bri.brimo.ui.fragments.bottomsheet.BottomSheetGeneralNewSkinFragment;
import id.co.bri.brimo.ui.fragments.bottomsheet.OpenBottomSheetGeneralNewSkinFragment;
import id.co.bri.brimo.ui.fragments.bottomsheet.OpenBottomSheetGeneralNewSkinFragment.*;

/**
 * BrimodSDK
 * <p>
 * Main React Native bridge for BRImo Android. This class provides API calls, navigation, event emitters,
 * and other utilities to ensure consistency with the iOS implementation. All main methods are accessible from JS.
 * <p>
 * Main features:
 * - requestApiCall: Call backend APIs from React Native
 * - Navigation to native and React Native screens
 * - Two-way event emitter (send data to React Native & native)
 * - Dismiss React Native screens from JS
 * - Helper for dynamic JSON parsing
 */
public class BrimodSDK implements RNBrimodSDKDelegate {
    private final ExecutorService executor = Executors.newSingleThreadExecutor();
    private final ApiSource apiSource;
    private final BRImoPrefSource prefSource;
    private static Context context = null;

    private static final String TAG = "BrimodSDK";
    public static final String COMPONENT_NAME_KEY = "component_name";
    public static final String INITIAL_PROPS_KEY = "initial_props";
    public static final String BUNDLE_NAME_KEY = "bundle_name";
    public static final String ARG_MODULE_NAME = "module_name";

    // OTA Constants
    public static final String API_OTA_APP_LIST = "http://localhost:9001/api/apps/list-app-latest-version";
    public static final String API_GET_PUBLIC_KEY = "http://localhost:9001/api/keys/get";
    public static final String OTA_VERSION_CHECK_URL = "https://brimo-ota-service.feedloop.ai/latest-bundle-name";
    public static final String OTA_BUNDLE_DOWNLOAD_URL = "https://brimo-ota-service.feedloop.ai/download";

    // Bundle Name
    public static final String BUNDLE_HOMEPAGE = "Homepage";
    public static final String BUNDLE_PORTFOLIO = "Portfolio";

    // Central registry of registered bundles for OTA
    public static final List<String> REGISTERED_BUNDLES = Arrays.asList(BUNDLE_HOMEPAGE, BUNDLE_PORTFOLIO);

    // Default Module Name
    public static final String DEFAULT_MODULE_HOMEPAGE = "BrimoHomepage";
    public static final String DEFAULT_MODULE_PORTFOLIO = "BrimoPortfolio";

    public static boolean isForDemo() {
        // Demo/Development Mode Config
        // Default is true, can be changed via setter
        return true;
    }

    // Handle CODE_SESSION_END
    private static final AtomicBoolean isSessionEndHandled = new AtomicBoolean(false);
    public static void resetSessionEndHandled() {
        isSessionEndHandled.set(false);
    }

    public BrimodSDK(Context context, ApiSource apiSource, BRImoPrefSource prefSource) {
        BrimodSDK.context = context;
        this.apiSource = apiSource;
        this.prefSource = prefSource;
    }

    @Override
    public void requestApiCall(Map<String, Object> apiObject, Map<String, Object> payload, OnSuccessCallback onSuccess, OnErrorCallback onError, Activity currentActivity) {
        executor.execute(() -> {
            try {
                Log.d(TAG, "===== REQUEST API CALL =====");

                // Extract URL from apiObject (similar to iOS implementation)
                String encryptedUrl = null;
                if (apiObject != null && apiObject.containsKey("android")) {
                    Object androidValue = apiObject.get("android");
                    if (androidValue instanceof String) {
                        encryptedUrl = (String) androidValue;
                    } else {
                        Log.e(TAG, "Invalid android value type: " + (androidValue != null ? androidValue.getClass().getName() : "null"));
                        onError.onError("INVALID_API_OBJECT", "Android value is not a string", null);
                        return;
                    }
                }

                if (encryptedUrl == null || encryptedUrl.isEmpty()) {
                    onError.onError("INVALID_API_OBJECT", "Api Object not found or invalid", null);
                    return;
                }

                Log.d(TAG, "===== API " + encryptedUrl + " =====");

                Object requestData;
                if (payload == null || payload.isEmpty()) {
                    JsonObject jsonObject = new JsonObject();
                    jsonObject.addProperty("", "");
                    requestData = jsonObject;
                } else {
                    requestData = new Gson().toJsonTree(payload);
                }

                Gson gson = new Gson();
                String seqNum = prefSource.getSeqNumber();
                io.reactivex.Observable<String> apiCall;
                if (payload == null || payload.isEmpty()) {
                    apiCall = apiSource.getDataTanpaRequest(encryptedUrl, seqNum);
                } else {
                    apiCall = apiSource.getData(encryptedUrl, requestData, seqNum);
                }
                apiCall
                        .timeout(30, TimeUnit.SECONDS)
                        .subscribeOn(io.reactivex.schedulers.Schedulers.io())
                        .observeOn(io.reactivex.android.schedulers.AndroidSchedulers.mainThread())
                        .subscribe(
                                response -> {
                                    try {
                                        RestResponse restResponse = MapperHelper.stringToRestResponse(response, seqNum);
                                        if (restResponse == null) {
                                            onError.onError("API_ERROR", "Response validation failed", null);
                                            return;
                                        }

                                        Log.i(TAG, "Response Code: " + restResponse.getCode(), null);
                                        if ("00".equals(restResponse.getCode()) || "01".equals(restResponse.getCode())) {
                                            try {
                                                String jsonResponse = gson.toJson(restResponse.getData());
                                                onSuccess.onSuccess(jsonResponse);
                                            } catch (Exception e) {
                                                Log.i(TAG, "Error message: " + e.getMessage().toString(), null);
                                                onError.onError("JSON_ERROR", "Failed to serialize response data", e);
                                            }
                                        } else if (CODE_SESSION_END.equals(restResponse.getCode())) {
                                            if (isSessionEndHandled.compareAndSet(false, true)) {
                                                // Use Handler to ensure UI operations run on main thread
                                                Handler mainHandler = new Handler(Looper.getMainLooper());
                                                mainHandler.post(() -> {
                                                    try {
                                                        // Check if currentActivity is FragmentActivity
                                                        if (currentActivity instanceof FragmentActivity) {
                                                            FragmentActivity fragmentActivity = (FragmentActivity) currentActivity;
                                                            BrimoGeneralErrorView.showByCode(fragmentActivity, restResponse.getCode(), restResponse.getDesc(), false);
                                                        } else {
                                                            // Fallback: try to get the current activity from context
                                                            if (context instanceof FragmentActivity) {
                                                                FragmentActivity fragmentActivity = (FragmentActivity) context;
                                                                BrimoGeneralErrorView.showByCode(fragmentActivity, restResponse.getCode(), restResponse.getDesc(), false);
                                                            } else {
                                                                Log.w(TAG, "Cannot show session end dialog: no FragmentActivity available");
                                                                onError.onError("API_ERROR", "Cannot show session end dialog: no FragmentActivity available", null);
                                                            }
                                                        }
                                                    } catch (Exception e) {
                                                        Log.e(TAG, "Error showing session end dialog: " + e.getMessage(), e);
                                                        onError.onError("API_ERROR", e.getMessage(), null);
                                                    }
                                                });
                                            }
                                            String errorMessage = "API returned error code: " + restResponse.getCode() + " - " + restResponse.getDesc();
                                            Log.i(TAG, "Error message: " + errorMessage, null);
                                            onError.onError("UNAUTHORIZED", errorMessage, null);
                                        } else {
                                            if (!("12".equals(restResponse.getCode()))) {
                                                // Use Handler to ensure UI operations run on main thread
                                                Handler mainHandler = new Handler(Looper.getMainLooper());
                                                mainHandler.post(() -> {
                                                    try {
                                                        // Check if currentActivity is FragmentActivity
                                                        if (currentActivity instanceof FragmentActivity) {
                                                            FragmentActivity fragmentActivity = (FragmentActivity) currentActivity;
                                                            BrimoGeneralErrorView.showByCode(fragmentActivity, restResponse.getCode(), restResponse.getDesc(), true);
                                                        } else {
                                                            // Fallback: try to get the current activity from context
                                                            if (context instanceof FragmentActivity) {
                                                                FragmentActivity fragmentActivity = (FragmentActivity) context;
                                                                BrimoGeneralErrorView.showByCode(fragmentActivity, restResponse.getCode(), restResponse.getDesc(), true);
                                                            } else {
                                                                Log.w(TAG, "Cannot show session end dialog: no FragmentActivity available");
                                                                onError.onError("API_ERROR", "Cannot show session end dialog: no FragmentActivity available", null);
                                                            }
                                                        }
                                                    } catch (Exception e) {
                                                        Log.e(TAG, "Error showing session end dialog: " + e.getMessage(), e);
                                                        onError.onError("API_ERROR", e.getMessage(), null);
                                                    }
                                                });
                                            }
                                            String errorMessage = "API returned error code: " + restResponse.getCode() + " - " + restResponse.getDesc();
                                            Log.i(TAG, "Error message: " + errorMessage, null);
                                            onError.onError("API_ERROR", errorMessage, null);
                                        }
                                    } catch (Exception e) {
                                        Log.i(TAG, "Error message: " + e.getMessage().toString(), null);
                                        onError.onError("JSON_ERROR", "Failed to parse API response: " + e.getMessage(), e);
                                    }
                                },
                                error -> {
                                    String errorMessage = error != null ? error.getMessage() : "Unknown network error";
                                    Log.i(TAG, "Error message: " + errorMessage, null);
                                    onError.onError("NETWORK_ERROR", errorMessage, error);
                                }
                        );
            } catch (Exception e) {
                Log.i(TAG, "Error message: " + e.getMessage().toString(), null);
                onError.onError("BRIDGE_ERROR", "Bridge execution error: " + e.getMessage(), e);
            }
        });
    }

    @Override
    public void selectTabBarItem(int index, Map<String, Object> params, OnSuccessCallback onSuccess, OnErrorCallback onError, Activity currentActivity) {
        Log.d(TAG, "===== SELECT TAB BAR ITEM index: " + index + ", params: " + params + " =====");
        if (currentActivity instanceof DashboardIBActivity) {
            ((DashboardIBActivity) currentActivity).selectTabByIndex(index, params);
            onSuccess.onSuccess("onSuccess");
        } else {
            onError.onError("TABBAR_NOT_FOUND", "TabBarController not found", null);
        }
    }

    @Override
    public void sendDataToNative(String name, Map<String, Object> data) {
        Log.d(TAG, "===== SEND DATA TO NATIVE, name: " + name + ", data: " + data + " =====");
        Intent intent = new Intent(name);
        if (data != null) {
            for (Map.Entry<String, Object> entry : data.entrySet()) {
                intent.putExtra(entry.getKey(), entry.getValue().toString());
            }
        }
        LocalBroadcastManager.getInstance(context).sendBroadcast(intent);
    }

    @Override
    public void dismiss(OnSuccessCallback onSuccess, OnErrorCallback onError, Activity currentActivity) {
        Log.d(TAG, "===== DISMISS =====");
        Handler mainHandler = new Handler(Looper.getMainLooper());
        mainHandler.post(() -> {
            if (currentActivity instanceof ReactNativeActivity) {
                currentActivity.finish();
                onSuccess.onSuccess("onSuccess");
            } else {
                onError.onError("DISMISS ERROR", "Top View Controller not found", null);
            }
        });
    }

    @Override
    public void push(Map<String, Object> navigateObject, Map<String, Object> params, Activity currentActivity) {
        Log.d(TAG, "===== PUSH " + navigateObject + " =====");
        Handler mainHandler = new Handler(Looper.getMainLooper());
        mainHandler.post(() -> {
            try {
                String activityName = extractActivityName(navigateObject);
                if (activityName == null || activityName.isEmpty()) {
                    Log.e(TAG, "ViewController not found");
                    return;
                }
                openNativeActivity(activityName, params);
            } catch (Exception e) {
                Log.e(TAG, "Push navigation error: " + e.getMessage(), e);
            }
        });
    }

    @Override
    public void present(Map<String, Object> navigateObject, Map<String, Object> params, Activity currentActivity) {
        Log.d(TAG, "===== PRESENT " + navigateObject + " =====");
        Handler mainHandler = new Handler(Looper.getMainLooper());
        mainHandler.post(() -> {
            try {
                String activityName = extractActivityName(navigateObject);
                if (activityName == null || activityName.isEmpty()) {
                    Log.e(TAG, "ViewController not found");
                    return;
                }

                // For present, we create a new activity with NEW_TASK flag
                Intent intent = new Intent(currentActivity != null ? currentActivity : context,
                        Class.forName(activityName));
                if (params != null) {
                    Bundle bundle = mapToBundle(params);
                    intent.putExtras(bundle);
                }
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                if (currentActivity != null) {
                    currentActivity.startActivity(intent);
                } else {
                    context.startActivity(intent);
                }
            } catch (Exception e) {
                Log.e(TAG, "Present navigation error: " + e.getMessage(), e);
            }
        });
    }

    @Override
    public void pop(Activity currentActivity) {
        Log.d(TAG, "===== POP =====");
        Handler mainHandler = new Handler(Looper.getMainLooper());
        mainHandler.post(() -> {
            if (currentActivity != null) {
                currentActivity.finish();
            }
        });
    }

    @Override
    public void popToRoot(Activity currentActivity) {
        Log.d(TAG, "===== POP TO ROOT =====");
        Handler mainHandler = new Handler(Looper.getMainLooper());
        mainHandler.post(() -> {
            if (currentActivity != null) {
                Intent intent = new Intent(currentActivity, DashboardIBActivity.class);
                intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK);
                currentActivity.startActivity(intent);
                currentActivity.finish();
            }
        });
    }

    @Override
    public void popToScreen(Map<String, Object> navigateObject, Activity currentActivity) {
        Log.d(TAG, "===== POP TO SCREEN " + navigateObject + " =====");
        Handler mainHandler = new Handler(Looper.getMainLooper());
        mainHandler.post(() -> {
            try {
                String activityName = extractActivityName(navigateObject);
                if (activityName == null || activityName.isEmpty()) {
                    Log.e(TAG, "ViewController not found");
                    return;
                }

                // For popToScreen, we need to find the target activity in the back stack
                // This is a simplified implementation - in a real app you might need more complex logic
                Intent intent = new Intent(currentActivity != null ? currentActivity : context,
                        Class.forName(activityName));
                intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
                if (currentActivity != null) {
                    currentActivity.startActivity(intent);
                } else {
                    context.startActivity(intent);
                }
            } catch (Exception e) {
                Log.e(TAG, "PopToScreen navigation error: " + e.getMessage(), e);
            }
        });
    }

    @Override
    public void getValueSharePref(String name, String key, boolean isShouldDecrypt, OnSuccessCallback onSuccess, OnErrorCallback onError) {
        try {
            Log.d(TAG, "===== GET VALUE SHARE PREF, name: " + name + ", key: " + key + " =====");

            if (context == null) {
                onError.onError("CONTEXT_ERROR", "Context is null", null);
                return;
            }

            SharedPreferences sp = context.getSharedPreferences(name, Context.MODE_PRIVATE);
            String value = sp.getString(key, "");
            Log.d(TAG, "getValueSharePref: valueEncrypt=" + value);
            if (isShouldDecrypt && !value.isEmpty()) {
                try {
                    value = MyCryptStatic.decryptAsBase64(value);
                } catch (Exception e) {
                    Log.e(TAG, "decrypt error: " + e.getMessage(), e);
                    value = "";
                }
            }

            Log.d(TAG, "getValueSharePref: valueDecrypt=" + value);
            onSuccess.onSuccess(value);
        } catch (Exception e) {
            Log.e(TAG, "getValueSharePref error: " + e.getMessage(), e);
            onError.onError("SHAREDPREF_ERROR", e.getMessage(), e);
        }
    }

    @Override
    public void saveValueSharePref(String name, String key, String value, boolean isShouldEncrypt) {
        try {
            Log.d(TAG, "===== SAVE DATA SHARE PREF, name: " + name + ", key: " + key + ", value: " + value + " =====");

            if (context == null) {
                Log.e(TAG, "Context is null, cannot save shared preference");
                return;
            }

            SharedPreferences sp = context.getSharedPreferences(name, Context.MODE_PRIVATE);
            SharedPreferences.Editor editor = sp.edit();
            String toSave = value;
            if (isShouldEncrypt && value != null && !value.isEmpty()) {
                try {
                    toSave = MyCryptStatic.encryptAsBase64(value);
                } catch (Exception e) {
                    Log.e(TAG, "encrypt error: " + e.getMessage(), e);
                    toSave = value;
                }
            }
            editor.putString(key, toSave);
            editor.apply();
            Log.d(TAG, "saveDataSharePref: apply() called");
        } catch (Exception e) {
            Log.e(TAG, "saveDataSharePref error: " + e.getMessage(), e);
        }
    }

    @Override
    public void removeValueSharePref(String name, String key) {
        try {
            Log.d(TAG, "===== REMOVE DATA SHARE PREF, name: " + name + ", key: " + key + " =====");

            if (context == null) {
                Log.e(TAG, "Context is null, cannot remove shared preference");
                return;
            }

            SharedPreferences sp = context.getSharedPreferences(name, Context.MODE_PRIVATE);
            SharedPreferences.Editor editor = sp.edit();
            editor.remove(key);
            editor.apply();
            Log.d(TAG, "removeDataSharePref: apply() called");
        } catch (Exception e) {
            Log.e(TAG, "removeDataSharePref error: " + e.getMessage(), e);
        }
    }

    public static void navigateToReact(String bundleName, String appName, Map<String, Object> params, Activity currentActivity) {
        try {
            Log.d(TAG, "===== NAVIGATE TO REACT NATIVE, bundleName: " + bundleName + ", appName: " + appName + " =====");

            if (context == null) {
                Log.e(TAG, "Context is null, cannot navigate to React Native");
                return;
            }

            Intent intent = new Intent(currentActivity != null ? currentActivity : context, ReactNativeActivity.class);
            intent.putExtra(COMPONENT_NAME_KEY, appName);
            intent.putExtra("bundle_name", bundleName);
            if (params != null) {
                Bundle bundle = mapToBundle(params);
                intent.putExtra(INITIAL_PROPS_KEY, bundle);
            }
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            if (currentActivity != null) {
                currentActivity.startActivity(intent);
            } else {
                context.startActivity(intent);
            }
        } catch (Exception e) {
            Log.e(TAG, "Navigate to React error: " + e.getMessage(), e);
        }
    }

    private String extractActivityName(Map<String, Object> navigateObject) {
        if (navigateObject == null) return null;

        try {
            // Check if it's React Native navigation (bundleName + appName)
            if (navigateObject.containsKey("bundleName") && navigateObject.containsKey("appName")) {
                Object bundleNameObj = navigateObject.get("bundleName");
                Object appNameObj = navigateObject.get("appName");

                if (bundleNameObj instanceof String && appNameObj instanceof String) {
                    String appName = (String) appNameObj;
                    String bundleName = (String) bundleNameObj;

                    // Navigate to React Native activity
                    navigateToReact(bundleName, appName, navigateObject, null);
                    return null; // Return null since we're handling React Native navigation
                } else {
                    Log.e(TAG, "Invalid bundleName or appName type: bundleName=" +
                            (bundleNameObj != null ? bundleNameObj.getClass().getName() : "null") +
                            ", appName=" + (appNameObj != null ? appNameObj.getClass().getName() : "null"));
                }
            }

            // Check if it's native navigation (activityName)
            if (navigateObject.containsKey("activityName")) {
                Object activityNameObj = navigateObject.get("activityName");
                if (activityNameObj instanceof String activityName) {
                    if (!activityName.isEmpty()) {
                        return activityName;
                    }
                } else {
                    Log.e(TAG, "Invalid activityName type: " +
                            (activityNameObj != null ? activityNameObj.getClass().getName() : "null"));
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error extracting activity name: " + e.getMessage(), e);
        }

        return null;
    }

    private void openNativeActivity(String activityName, Map<String, Object> params) {
        try {
            if (activityName == null || activityName.isEmpty()) return;
            Class<?> clazz = Class.forName(activityName);
            if (!Activity.class.isAssignableFrom(clazz)) return;
            Intent intent = new Intent(context, clazz);
            if (params != null) {
                Bundle bundle = mapToBundle(params);
                intent.putExtras(bundle);
            }
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            context.startActivity(intent);
        } catch (Exception e) {
            Log.w(TAG, Objects.requireNonNull(e.getMessage()));
        }
    }

    private static Bundle mapToBundle(Map<String, Object> map) {
        Bundle bundle = new Bundle();
        if (map == null) return bundle;

        try {
            for (Map.Entry<String, Object> entry : map.entrySet()) {
                Object value = entry.getValue();
                String key = entry.getKey();

                if (key == null) {
                    Log.w(TAG, "Skipping null key in mapToBundle");
                    continue;
                }

                if (value instanceof Integer) {
                    bundle.putInt(key, (Integer) value);
                } else if (value instanceof Boolean) {
                    bundle.putBoolean(key, (Boolean) value);
                } else if (value instanceof Double) {
                    bundle.putDouble(key, (Double) value);
                } else if (value instanceof Long) {
                    bundle.putLong(key, (Long) value);
                } else if (value instanceof String) {
                    bundle.putString(key, (String) value);
                } else {
                    // For other types, convert to string
                    bundle.putString(key, value != null ? value.toString() : null);
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error converting map to bundle: " + e.getMessage(), e);
        }

        return bundle;
    }

    // Helper: Verify manifest signature (RSA SHA256)
    public static boolean verifyManifestSignature(String canonicalManifest, String signatureHex, String publicKeyPem) {
        try {
            android.util.Log.i(TAG, "[SIGNATURE] Starting verification");
            android.util.Log.i(TAG, "[SIGNATURE] canonicalManifest length: " + (canonicalManifest != null ? canonicalManifest.length() : 0));
            android.util.Log.i(TAG, "[SIGNATURE] signatureBase64 (first 32): " + (signatureHex != null && signatureHex.length() > 32 ? signatureHex.substring(0, 32) : signatureHex));
            android.util.Log.i(TAG, "[SIGNATURE] publicKeyPem (first 32): " + (publicKeyPem != null && publicKeyPem.length() > 32 ? publicKeyPem.substring(0, 32) : publicKeyPem));

            android.util.Log.i(TAG, "[SIGNATURE] Parsing public key...");
            java.security.PublicKey publicKey = getPublicKeyFromPem(publicKeyPem);
            android.util.Log.i(TAG, "[SIGNATURE] Public key parsed: " + (publicKey != null));

            android.util.Log.i(TAG, "[SIGNATURE] Initializing Signature object...");
            java.security.Signature sig = java.security.Signature.getInstance("SHA256withRSA");
            sig.initVerify(publicKey);
            android.util.Log.i(TAG, "[SIGNATURE] Updating signature with manifest bytes...");
            sig.update(canonicalManifest.getBytes(java.nio.charset.StandardCharsets.UTF_8));
            byte[] signatureBytes = hexStringToByteArray(signatureHex);
            android.util.Log.i(TAG, "[SIGNATURE] Decoded signature bytes length: " + (signatureBytes != null ? signatureBytes.length : 0));
            android.util.Log.i(TAG, "[SIGNATURE] Verifying signature...");
            boolean result = sig.verify(signatureBytes);
            android.util.Log.i(TAG, "[SIGNATURE] Signature verification result: " + result);
            return result;
        } catch (Exception e) {
            android.util.Log.e(TAG, "[SIGNATURE] Signature verification failed: " + e.getMessage(), e);
            return false;
        }
    }

    public static byte[] hexStringToByteArray(String s) {
        int len = s.length();
        byte[] data = new byte[len / 2];
        for (int i = 0; i < len; i += 2) {
            data[i / 2] = (byte) ((Character.digit(s.charAt(i), 16) << 4)
                    + Character.digit(s.charAt(i+1), 16));
        }
        return data;
    }

    // Helper: Parse PEM public key
    public static java.security.PublicKey getPublicKeyFromPem(String pem) throws Exception {
        String publicKeyPEM = pem.replace("-----BEGIN PUBLIC KEY-----", "")
                .replace("-----END PUBLIC KEY-----", "")
                .replaceAll("\\s", "");
        byte[] encoded = android.util.Base64.decode(publicKeyPEM, android.util.Base64.DEFAULT);
        java.security.spec.X509EncodedKeySpec keySpec = new java.security.spec.X509EncodedKeySpec(encoded);
        java.security.KeyFactory keyFactory = java.security.KeyFactory.getInstance("RSA");
        return keyFactory.generatePublic(keySpec);
    }

    // Helper: Canonicalize manifest JSON (remove signature field, sort keys)
    public static String canonicalizeManifest(org.json.JSONObject manifest) throws Exception {
        org.json.JSONObject clone = new org.json.JSONObject(manifest.toString());
        clone.remove("signature");
        java.util.TreeMap<String, Object> sorted = new java.util.TreeMap<>();
        java.util.Iterator<String> keys = clone.keys();
        while (keys.hasNext()) {
            String key = keys.next();
            sorted.put(key, clone.get(key));
        }
        org.json.JSONObject sortedJson = new org.json.JSONObject(sorted);
        // Hapus escape karakter pada slash
        return sortedJson.toString().replace("\\/", "/");
    }

    // Helper: SHA-256 hash file, return hex string
    public static String hashFileSHA256(java.io.File file) throws Exception {
        java.security.MessageDigest digest = java.security.MessageDigest.getInstance("SHA-256");
        java.io.FileInputStream fis = new java.io.FileInputStream(file);
        byte[] buffer = new byte[8192];
        int bytesRead;
        while ((bytesRead = fis.read(buffer)) != -1) {
            digest.update(buffer, 0, bytesRead);
        }
        fis.close();
        byte[] hashBytes = digest.digest();
        StringBuilder sb = new StringBuilder();
        for (byte b : hashBytes) {
            sb.append(String.format("%02x", b));
        }
        return sb.toString();
    }
}