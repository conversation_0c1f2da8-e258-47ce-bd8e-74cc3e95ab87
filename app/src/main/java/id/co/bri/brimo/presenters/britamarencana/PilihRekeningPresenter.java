package id.co.bri.brimo.presenters.britamarencana;

import java.util.concurrent.TimeUnit;

import id.co.bri.brimo.contract.IPresenter.britamarencana.IPilihRekeningPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.britamarencana.IPilihRekeningView;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.config.AppConfig;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.presenters.MvpPresenter;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.schedulers.Schedulers;

public class PilihRekeningPresenter<V extends IMvpView & IPilihRekeningView> extends MvpPresenter<V>
        implements IPilihRekeningPresenter<V> {

    protected String urlValidS3f;

    public PilihRekeningPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void getDataRekening() {

    }

    @Override
    public void setUrlValidationS3f(String urlValidS3f) {
        this.urlValidS3f = urlValidS3f;
    }

    @Override
    public void getDataValidationS3F() {
        if (isViewAttached()) {

            getView().showProgress();
            String sequence = getBRImoPrefRepository().getSeqNumber();
            getCompositeDisposable().add(getApiSource().getData(urlValidS3f, getBRImoPrefRepository().getUsername(), sequence)
                    .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(new ApiObserver(getView(), sequence) {
                        @Override
                        protected void onFailureHttp(String errorMessage) {
                            getView().hideProgress();
                            getView().onException(errorMessage);
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            getView().hideProgress();
                            if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SUCCESS.getValue()))
                                getView().onSuccessGetData();
                            else if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_S3E.getValue()))
                                getView().onGetS3E(response.getDesc());
                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            getView().hideProgress();
                            if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                getView().onSessionEnd(restResponse.getDesc());
                            else
                                getView().onException(restResponse.getDesc());
                        }
                    }));

        }
    }
}