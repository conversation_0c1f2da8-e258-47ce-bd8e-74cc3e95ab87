package id.co.bri.brimo.presenters.pengelolaankartu;

import java.util.concurrent.TimeUnit;
import id.co.bri.brimo.contract.IPresenter.pengelolaankartu.IPengelolaanKartuPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.pengelolaankartu.IPengelolaanKartuView;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.config.AppConfig;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.apimodel.request.pengelolaankartu.DetailKelolaKartuReq;
import id.co.bri.brimo.models.apimodel.response.pengelolaankartu.DetailKelolaKartuRes;
import id.co.bri.brimo.models.apimodel.response.ListPengelolaanKartuRes;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.presenters.MvpPresenter;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.schedulers.Schedulers;

public class PengelolaanKartuPresenter<V extends IMvpView & IPengelolaanKartuView>
        extends MvpPresenter<V> implements IPengelolaanKartuPresenter<V> {

    private String urlList;
    private String urlDetail;

    public PengelolaanKartuPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable,
                                     BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource,
                                     TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void setUrlCardList(String urlCardList) {
        this.urlList = urlCardList;
    }

    @Override
    public void setUrlCardDetail(String urlDetail) {
        this.urlDetail = urlDetail;
    }

    @Override
    public void getAccountWithCardNumber() {
        if (urlList != null && isViewAttached()) {
            String seqNum = getBRImoPrefRepository().getSeqNumber();

            getCompositeDisposable().add(getApiSource().getDataTanpaRequest(urlList, seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(new ApiObserver(getView(), seqNum) {
                        @Override
                        protected void onFailureHttp(String errorMessage) {
                            getView().onException(errorMessage);
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SUCCESS.getValue())) {
                                ListPengelolaanKartuRes listKartuRes = response.getData(ListPengelolaanKartuRes.class);
                                getView().onSuccessCardList(listKartuRes);
                            } else getView().onSuccessNoCard();
                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                getView().onSessionEnd(restResponse.getDesc());
                            else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_12.getValue()))
                                getView().onException12(restResponse.getDesc());
                            else getView().onException(restResponse.getCode());
                        }
                    }));
        }
    }

    @Override
    public void getCardDetail(DetailKelolaKartuReq detailKelolaKartuReq) {
        if (urlDetail != null && isViewAttached()) {
            getView().showProgress();
            String seqNum = getBRImoPrefRepository().getSeqNumber();

            getCompositeDisposable().add(getApiSource().getData(urlDetail, detailKelolaKartuReq, seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(new ApiObserver(getView(), seqNum) {

                        @Override
                        protected void onFailureHttp(String errorMessage) {
                            getView().hideProgress();
                            getView().onException(errorMessage);
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            getView().hideProgress();
                            DetailKelolaKartuRes detailResponse = response.getData(DetailKelolaKartuRes.class);
                            getView().onSuccessCardDetail(detailResponse);
                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            getView().hideProgress();
                            if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                getView().onSessionEnd(restResponse.getDesc());
                            else getView().onException(restResponse.getDesc());
                        }
                    }));
        }
    }
}