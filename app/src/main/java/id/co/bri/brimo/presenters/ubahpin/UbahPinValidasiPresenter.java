package id.co.bri.brimo.presenters.ubahpin;

import java.util.concurrent.TimeUnit;

import javax.inject.Inject;

import id.co.bri.brimo.contract.IPresenter.ubahpin.IUbahPinValidasiPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.ubahpin.IUbahPinValidasiView;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.config.AppConfig;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.apimodel.request.UbahPinResendOtpRequest;
import id.co.bri.brimo.models.apimodel.request.UbahPinValidateOtpRequest;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.models.apimodel.response.UbahPasswordResponse;
import id.co.bri.brimo.presenters.MvpPresenter;
import io.reactivex.disposables.CompositeDisposable;

public class UbahPinValidasiPresenter<V extends IMvpView & IUbahPinValidasiView> extends MvpPresenter<V> implements IUbahPinValidasiPresenter<V> {

    private String url = "";
    private String urlResendOtp = "";

    @Inject
    public UbahPinValidasiPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void ubahPinResend(String refNumber) {
        if (getView() == null) {
            return;
        }
        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        UbahPinResendOtpRequest ubahPinResendOtpRequest = new UbahPinResendOtpRequest(refNumber);

        if (isViewAttached()) {
            getCompositeDisposable().add(
                    getApiSource().getData(urlResendOtp, ubahPinResendOtpRequest, seqNum)
                            .subscribeOn(getSchedulerProvider().single())
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .observeOn(getSchedulerProvider().mainThread())
                            .subscribeWith(new ApiObserver(getView(),seqNum) {
                                @Override
                                protected void onFailureHttp(String type) {
                                    getView().hideProgress();
                                    getView().onException(type);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    getView().hideProgress();
                                    UbahPasswordResponse ubahPasswordResponse = response.getData(UbahPasswordResponse.class);
                                    getView().onResendSuccess(ubahPasswordResponse.getReference_number());
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().hideProgress();
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue())) {
                                        getView().onSessionEnd(restResponse.getDesc());
                                    } else
                                        getView().onException(restResponse.getDesc());
                                }
                            })
            );
        }
    }

    @Override
    public void ubahPin(String otp, String refNumber) {
        if (getView() == null) {
            return;
        }

        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        UbahPinValidateOtpRequest ubahPinOtpRequest = new UbahPinValidateOtpRequest(refNumber,otp);

        if (isViewAttached()) {
            getCompositeDisposable().add(
                    getApiSource().getData(url, ubahPinOtpRequest, seqNum)
                            .subscribeOn(getSchedulerProvider().single())
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .observeOn(getSchedulerProvider().mainThread())
                            .subscribeWith(new ApiObserver(getView(),seqNum) {
                                @Override
                                protected void onFailureHttp(String type) {
                                    getView().hideProgress();
                                    getView().onException(type);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    getView().hideProgress();
                                    UbahPasswordResponse ubahPasswordResponse = response.getData(UbahPasswordResponse.class);
                                    getView().onSuccess(ubahPasswordResponse.getReference_number());
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().hideProgress();
                                    getView().deleteOtp();
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue())) {
                                        getView().onSessionEnd(restResponse.getDesc());
                                    }
                                    else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.getValue())){
                                        getView().onException93(restResponse.getDesc());
                                    }else
                                        getView().onException(restResponse.getDesc());
                                }
                            })
            );
        }
    }

    @Override
    public void setUrl(String url) {
        this.url = url;
    }

    @Override
    public void setUrlResendOtp(String urlResendOtp) {
        this.urlResendOtp = urlResendOtp;
    }
}