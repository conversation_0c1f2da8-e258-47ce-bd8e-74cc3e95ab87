package id.co.bri.brimo.presenters.lifestyle

import android.util.Log
import id.co.bri.brimo.contract.IPresenter.lifestyle.IKonfirmasiLifestylePresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.lifestyle.IKonfirmasiLifestyleView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.api.observer.ApiObserverKonfirmasi
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimo.data.repository.category.CategoryPfmSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.config.DbConfig

import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.calendar.CalendarHelper
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.cashback.CashbackRevFilterRequest
import id.co.bri.brimo.models.apimodel.request.cashback.PilihCashbackRequest
import id.co.bri.brimo.models.apimodel.request.voucher.PaymentVoucherRequest
import id.co.bri.brimo.models.apimodel.response.GeneralResponse
import id.co.bri.brimo.models.apimodel.response.ListAllCashbackFilterResponse
import id.co.bri.brimo.models.apimodel.response.ReceiptRevampResponse
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.SelectCashbackResponse
import id.co.bri.brimo.models.apimodel.response.lifestyle.ConfirmationLifestyleResponse
import id.co.bri.brimo.models.apimodel.response.lifestyle.ReceiptLifestyleResponse
import id.co.bri.brimo.models.daomodel.Transaksi
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.disposables.Disposable
import io.reactivex.observers.DisposableSingleObserver

class KonfirmasiLifestylePresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBRImoPrefRepository: BRImoPrefSource,
    apiSource: ApiSource,
    categoryPfmSource: CategoryPfmSource,
    transaksiPfmSource: TransaksiPfmSource,
    anggaranPfmSource: AnggaranPfmSource
) : MvpPresenter<V>(
    schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource
),
    IKonfirmasiLifestylePresenter<V> where V : IMvpView, V : IKonfirmasiLifestyleView {

    private lateinit var mUrlPayment: String
    private lateinit var paymentRequest: Any
    private lateinit var urlGetListCashback: String
    private lateinit var selectCashbackUrl: String
    private lateinit var redeemCashbackRequest: Any
    private lateinit var cashbackRequest: Any

    override fun setUrlPayment(url: String) {
        mUrlPayment = url
    }

    override fun getDataPayment(
        confirmationLifestyleResponse: ConfirmationLifestyleResponse,
        account: String, pin: String, pfmCategory: String) {

        if (mUrlPayment.isEmpty() || !isViewAttached)
            return

        view.showProgress()
        val seqNum = brImoPrefRepository.seqNumber

        paymentRequest = PaymentVoucherRequest(confirmationLifestyleResponse.refferenceNumber,
            account, pin, confirmationLifestyleResponse.pfmCategory.toString())

        val disposable: Disposable = apiSource.getData(mUrlPayment, paymentRequest, seqNum) //function(param)
            .subscribeOn(schedulerProvider.io())
            .observeOn(schedulerProvider.mainThread())
            .subscribeWith(object : ApiObserverKonfirmasi(view, seqNum) {
                override fun onFailureHttp(errorMessage: String?) {
                    getView().hideProgress()
                    getView().onException(errorMessage)
                }

                override fun onApiCallSuccess(response: RestResponse) {
                    getView().hideProgress()

                    val receiptRevampResponse = response.getData(ReceiptRevampResponse::class.java)

                    if (receiptRevampResponse.immediatelyFlag) onSaveTransaksiPfm(
                        generateTransaksiModel(
                            confirmationLifestyleResponse.pfmCategory,
                            confirmationLifestyleResponse.payAmount,
                            confirmationLifestyleResponse.refferenceNumber,
                            confirmationLifestyleResponse.pfmDescription
                        )
                    )
                    getView().onSuccessGetPayment(receiptRevampResponse)
                }

                override fun onApiCallError(restResponse: RestResponse) {
                    getView().hideProgress()
                    val code = restResponse.code
                    val desc = restResponse.desc

                    when {
                        code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value, ignoreCase = true) -> {
                            getView().onSessionEnd(desc)
                        }
                        code.equals(RestResponse.ResponseCodeEnum.RC_12.value, ignoreCase = true) -> {
                            getView()?.onExceptionSnackbar(desc)
                        }
                        code.equals(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.value, ignoreCase = true) -> {
                            getView()?.onExceptionTrxExpired(desc)
                        }
                        code.equals(RestResponse.ResponseCodeEnum.RC_01.value, ignoreCase = true) -> {
                            getView().onException01(desc)
                        }
                        code.equals(RestResponse.ResponseCodeEnum.RC_61.value, ignoreCase = true) -> {
                            getView().onExceptionLimitExceed(
                                restResponse.getData(GeneralResponse::class.java)
                            )
                        }
                        else -> {
                            getView().onException(desc)
                        }
                    }
                }
            })

        compositeDisposable.add(disposable)
    }

    fun onSaveTransaksiPfm(transaksi: Transaksi?) {
        if (transaksi != null) {
            compositeDisposable.add(transaksiPfmSource
                .saveTransaksiPfm(transaksi)
                .subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : DisposableSingleObserver<Long?>() {
                    override fun onSuccess(aLong: Long) {}
                    override fun onError(e: Throwable) {
                        if (!GeneralHelper.isProd())
                            Log.d("saveTransaksiPfm", "onError: $e")
                    }
                })
            )
        }
    }

    fun generateTransaksiModel(kategoriId: Int, amount: Long, referenceNumber: String?, billingName: String?): Transaksi? {
        var transaksi: Transaksi? = null
        try {
            transaksi = Transaksi(kategoriId.toLong(),
                1,
                billingName,
                "",
                DbConfig.TRX_OUT,
                brImoPrefRepository.user,
                amount,
                CalendarHelper.getCurrentDate(),
                CalendarHelper.getCurrentTime(),
                java.lang.Long.valueOf(referenceNumber.toString()),
                0
            )
        } catch (e: Exception) {
            /*
            if(BuildConfig.DEBUG)
            e.printStackTrace();
             */
        }
        return transaksi
    }

    override fun start() {
        super.start()
        getDefaultSaldo()
    }

    override fun getAccountDefault(): String {
        return brImoPrefRepository.accountDefault
    }

    override fun getSaldoRekeningUtama(): String {
        return brImoPrefRepository.saldoRekeningUtama
    }

    private fun getDefaultSaldo() {
        var saldo = 0.0
        val saldoText = brImoPrefRepository.saldoRekeningUtamaString
        if (saldoText != "") {
            val cleanString = saldoText.replace("[,.]".toRegex(), "")
            try {
                saldo = cleanString.toDouble() / 100
            } catch (e: Exception) {
                // do nothing
            }
        }
        val saldoHold = brImoPrefRepository.saldoHold

        val defaultAcc = brImoPrefRepository.accountDefault

        val saldoString = brImoPrefRepository.saldoRekeningUtamaString
        view.setDefaultSaldo(saldo, saldoString, defaultAcc, saldoHold)
    }

    override fun setUrlGetCashback(url: String) {
        this.urlGetListCashback = url
    }

    override fun getCashbackAll(
        accountNumber: String,
        referenceNumber: String
    ) {
        if (urlGetListCashback == null || !isViewAttached) {
            return
        }

        cashbackRequest = CashbackRevFilterRequest(
            accountNumber = accountNumber,
            referenceNumber = referenceNumber
        )

        view.showProgress()
        val seqNum = brImoPrefRepository.seqNumber
        compositeDisposable.add(
            apiSource.getData(urlGetListCashback, cashbackRequest, seqNum)
                .subscribeOn(schedulerProvider.single())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView().onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView().hideProgress()
                        if (response.code.equals("00", ignoreCase = true)) {
                            val responseData = response.getData(
                                ListAllCashbackFilterResponse::class.java
                            )
                            getView().onSuccessGetCashback(responseData)
                        } else if (response.code.equals("01", ignoreCase = true)) {
                            getView().onCashbackBlank(response.desc)
                        }
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView().hideProgress()
                        onApiError(restResponse)
                    }
                })
        )
    }

    override fun setRedeemCashbackUrl(url: String) {
        this.selectCashbackUrl = url
    }

    override fun getRedeemCashback(refNum: String, code: String) {
        if (selectCashbackUrl == null || !isViewAttached) {
            return
        }

        redeemCashbackRequest = PilihCashbackRequest(refNum, code)

        view.showProgress()
        val seqNum = brImoPrefRepository.seqNumber
        compositeDisposable.add(
            apiSource.getData(selectCashbackUrl, redeemCashbackRequest, seqNum)
                .subscribeOn(schedulerProvider.single())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView().onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView().hideProgress()
                        val selectCashbackResponse = response.getData(
                            SelectCashbackResponse::class.java
                        )
                        getView().onSuccessClearSelectedCashback(selectCashbackResponse)
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView().hideProgress()
                        when {
                            restResponse.code.equals(
                                "12",
                                ignoreCase = true
                            ) -> getView().onExceptionSnackbar(restResponse.desc)

                            else -> getView().onException(restResponse.desc)
                        }
                    }
                })
        )
    }

}