package id.co.bri.brimo.presenters.virtualdebitcard

import id.co.bri.brimo.contract.IPresenter.virtualdebitcard.IVirtualDebitLabelPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.virtualdebitcard.IVirtualDebitLabelView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.ChipVirtualDebitUIModel
import id.co.bri.brimo.models.apimodel.request.InquiryBrizziRequest
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.disposables.CompositeDisposable

class VirtualDebitLabelPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBRImoPrefRepository: BRImoPrefSource,
    apiSource: ApiSource,
    transaksiPfmSource: TransaksiPfmSource,
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    mBRImoPrefRepository,
    apiSource,
    transaksiPfmSource
), IVirtualDebitLabelPresenter<V> where V : IMvpView, V : IVirtualDebitLabelView {

    private var chipList: List<ChipVirtualDebitUIModel> = emptyList()

    override fun getDataLabel() {
        val suggestLabelCard = "Hiburan, Liburan, Belanja, Jajan, Tabungan, Beli Game"

        val data = suggestLabelCard
            .split(",")
            .map { it.trim() }
            .map { ChipVirtualDebitUIModel(title = it) }

        chipList = data
        view.showChips(chipList)

    }

    override fun onClickChip(data: ChipVirtualDebitUIModel, position: Int) {
        chipList = chipList.mapIndexed { index, chip ->
            chip.copy(isSelected = (index == position))
        }
        view.showChips(chipList)
    }

}