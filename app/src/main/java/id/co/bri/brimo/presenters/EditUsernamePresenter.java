package id.co.bri.brimo.presenters;

import java.util.concurrent.TimeUnit;

import id.co.bri.brimo.contract.IPresenter.IEditUsernamePresenter;
import id.co.bri.brimo.contract.IView.IEditUsernameView;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.config.AppConfig;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.apimodel.request.EditUsernameRequest;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import io.reactivex.disposables.CompositeDisposable;

public class EditUsernamePresenter<V extends IMvpView & IEditUsernameView>
        extends MvpPresenter<V> implements IEditUsernamePresenter<V> {

    protected String url;

    public EditUsernamePresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void setUrlEdit(String url) {
        this.url = url;
    }

    @Override
    public void getEditUsername(String editUsername, String pin) {
        if (isViewAttached()) {
            EditUsernameRequest request = new EditUsernameRequest(editUsername, pin);

            String seqNum = getBRImoPrefRepository().getSeqNumber();
            getView().showProgress();
            getCompositeDisposable().add(
                    getApiSource().getData(url, request, seqNum)
                            .subscribeOn(getSchedulerProvider().single())
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .observeOn(getSchedulerProvider().mainThread())
                            .subscribeWith(new ApiObserver(getView(),seqNum) {
                                @Override
                                protected void onFailureHttp(String type) {
                                    getView().hideProgress();
                                    getView().onException(type);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    getView().hideProgress();

                                    //update login flag
                                    updateLoginFlag(true);

                                    getView().onSuccessEdit();
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().hideProgress();
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue())) {
                                        getView().onSessionEnd(restResponse.getDesc());
                                    } else
                                        getView().onException(restResponse.getDesc());
                                }
                            })
            );
        }
    }
}