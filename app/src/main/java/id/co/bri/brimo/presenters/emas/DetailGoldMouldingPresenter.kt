package id.co.bri.brimo.presenters.emas

import id.co.bri.brimo.contract.IPresenter.emas.IDetailGoldMouldingPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.emas.IDetailAmbilFisikEmasView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimo.data.repository.category.CategoryPfmSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.config.AppConfig
import id.co.bri.brimo.domain.config.Constant.RE12
import id.co.bri.brimo.domain.config.Constant.RE_TRX_EXPIRED
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.emas.DetailAmbilEmasFisikRequest
import id.co.bri.brimo.models.apimodel.request.emas.DetailTrxAmbilFisikEmasRequest
import id.co.bri.brimo.models.apimodel.response.ReceiptRevampResponse
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.emas.DetailAmbilEmasFisikResponse
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.Schedulers
import java.util.concurrent.TimeUnit

class DetailGoldMouldingPresenter <V>(schedulerProvider: SchedulerProvider?,
                                      compositeDisposable: CompositeDisposable?,
                                      mBRImoPrefRepository: BRImoPrefSource?, apiSource: ApiSource?,
                                      categoryPfmSource: CategoryPfmSource?,
                                      transaksiPfmSource: TransaksiPfmSource?,
                                      anggaranPfmSource: AnggaranPfmSource?) : MvpPresenter<V>(
        schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource),
        IDetailGoldMouldingPresenter<V> where V : IMvpView?, V : IDetailAmbilFisikEmasView {

    var mUrlGetDetailAmbilFisikEmas : String? = null
    var mUrlGetDetailTransaksi : String? = null
    override fun setUrlGetDetailTransaction(urlGetDetailTrx: String) {
        mUrlGetDetailTransaksi = urlGetDetailTrx
    }

    override fun setUrlGetDetailAmbilFisikEmas(urlGetDetail: String) {
        mUrlGetDetailAmbilFisikEmas = urlGetDetail
    }

    override fun getDetailAmbilFisikEmas(request: DetailAmbilEmasFisikRequest) {
        if (mUrlGetDetailAmbilFisikEmas == null || !isViewAttached) {
            return
        }
        view.showProgress()

        val seq = brImoPrefRepository.seqNumber

        compositeDisposable.add(
                apiSource.getData(mUrlGetDetailAmbilFisikEmas, request, seq) //function(param)
                        .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                        .subscribeOn(Schedulers.io())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(object : ApiObserver(view, seq) {
                            override fun onFailureHttp(errorMessage: String) {
                                getView().hideProgress()
                                getView().onException(errorMessage)
                            }

                            override fun onApiCallSuccess(response: RestResponse) {
                                getView().hideProgress()
                                val mutasiResponse = response.getData(DetailAmbilEmasFisikResponse::class.java)
                                getView().onSuccessGetDetailAmbilFisikEmas(mutasiResponse)
                            }

                            override fun onApiCallError(restResponse: RestResponse) {
                                getView().hideProgress()
                                when(restResponse.restResponse.code){
                                    RestResponse.ResponseCodeEnum.RC_12.value -> getView().onExceptionAlert(restResponse.desc)
                                    else -> onApiError(restResponse)
                                }
                            }
                        })
        )
    }

    override fun getDataPaymentRevamp(request: DetailTrxAmbilFisikEmasRequest) {
        if (mUrlGetDetailTransaksi == null || !isViewAttached) {
            return
        }
        view.showProgress()

        val seq = brImoPrefRepository.seqNumber

        compositeDisposable.add(
            apiSource.getData(mUrlGetDetailTransaksi, request, seq) //function(param)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(object : ApiObserver(view, seq) {
                    override fun onFailureHttp(errorMessage: String) {
                        view.hideProgress()
                        getView().onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        view.hideProgress()
                        val mutasiResponse = response.getData(ReceiptRevampResponse::class.java)
                        getView().onSuccessGetPaymentRevamp(mutasiResponse)
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        view.hideProgress()
                        when(restResponse.restResponse.code){
                            RestResponse.ResponseCodeEnum.RC_12.value -> getView().onException(restResponse.desc)
                            RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.value -> getView().onExceptionTrxExpired(restResponse.desc)
                            else -> onApiError(restResponse)
                        }
                    }
                })
        )    }

}