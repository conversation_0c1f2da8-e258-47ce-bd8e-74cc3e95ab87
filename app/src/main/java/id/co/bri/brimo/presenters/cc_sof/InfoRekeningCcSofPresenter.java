package id.co.bri.brimo.presenters.cc_sof;

import id.co.bri.brimo.contract.IPresenter.cc_sof.IInfoRekeningCcSofPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.cc_sof.IInfoRekeningCcSofView;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.apimodel.request.bindingcc.InfoCvvKKIRequest;
import id.co.bri.brimo.models.apimodel.request.InfoCvvRequest;
import id.co.bri.brimo.models.apimodel.response.GeneralConfirmationResponse;
import id.co.bri.brimo.models.apimodel.response.cc.InfoCvvKKIResponse;
import id.co.bri.brimo.models.apimodel.request.pengelolaankartu.InitChangePinCCRequest;
import id.co.bri.brimo.models.apimodel.response.InfoCvvResponse;
import id.co.bri.brimo.models.apimodel.response.InquiryBrivaRevampResponse;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.models.apimodel.response.SyaratKetentuanResponse;
import id.co.bri.brimo.models.apimodel.response.pengelolaankartu.InitChangePinResponse;
import id.co.bri.brimo.presenters.MvpPresenter;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.schedulers.Schedulers;

public class InfoRekeningCcSofPresenter<V extends IMvpView & IInfoRekeningCcSofView>
        extends MvpPresenter<V> implements IInfoRekeningCcSofPresenter<V> {

    protected String urlTerm;
    protected String urlInfoCvv;
    protected String urlInfoCvvKKI;

    public InfoRekeningCcSofPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable,
                                      BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource,
                                      TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void setUrlTerm(String urlTerm) {
        this.urlTerm = urlTerm;
    }

    @Override
    public void setUrlInfoCvv(String urlInfoCvv) {
        this.urlInfoCvv = urlInfoCvv;
    }

    @Override
    public void getDataTerm() {
        if (urlTerm.isEmpty() || !isViewAttached()) return;
        getView().showProgress();

        String seqNum = getBRImoPrefRepository().getSeqNumber();

        getCompositeDisposable()
                .add(getApiSource().getDataTanpaRequest(urlTerm, seqNum)
                        .subscribeOn(Schedulers.io())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(new ApiObserver(getView(), seqNum) {
                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().hideProgress();
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                getView().hideProgress();
                                SyaratKetentuanResponse syaratKetentuanResponse = response.getData(SyaratKetentuanResponse.class);
                                getView().onSuccessTerm(syaratKetentuanResponse);
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                onApiError(restResponse);
                            }
                        }));
    }

    @Override
    public void getDataInfoCvv(InfoCvvRequest infoCvvRequest) {
        if (urlInfoCvv.isEmpty() || !isViewAttached()) return;

        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();

        getCompositeDisposable()
                .add(getApiSource().getData(urlInfoCvv, infoCvvRequest, seqNum)
                        .subscribeOn(Schedulers.io())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(new ApiObserver(getView(), seqNum) {
                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().hideProgress();
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                getView().hideProgress();
                                InfoCvvResponse infoCvvResponse = response.getData(InfoCvvResponse.class);
                                getView().onSuccessInfoCvv(infoCvvResponse);
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                onApiError(restResponse);
                            }
                        }));
    }
    @Override
    public void getConfirmationKKI(String typeInquiry,String reqSeq, String urlConfirmation, String urlPay, String title) {
        if (urlConfirmation == null || !isViewAttached()) {
            return;
        }

        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        getCompositeDisposable()
                .add(getApiSource().getDataNotif(urlConfirmation, reqSeq, seqNum)
                        .subscribeOn(getSchedulerProvider().io())
                        .observeOn(getSchedulerProvider().mainThread())
                        .subscribeWith(new ApiObserver(getView(),seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().dismissFragmentCC();
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                getView().dismissFragmentCC();
                                getView().hideProgress();
                                GeneralConfirmationResponse generalConfirmationResponse = response.getData(GeneralConfirmationResponse.class);
                                getView().onSuccessConfirmationKKI(generalConfirmationResponse, urlConfirmation, urlPay, title,typeInquiry);
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().dismissFragmentCC();
                                onApiError(restResponse);
                            }
                        }));


    }

    @Override
    public void getDataInfoCvvKKI(InfoCvvKKIRequest infoCvvKKIRequest) {
        if (isViewAttached()) {

            getView().showProgress();
            String seqNum = getBRImoPrefRepository().getSeqNumber();

            getCompositeDisposable()
                    .add(getApiSource().getData(urlInfoCvvKKI, infoCvvKKIRequest, seqNum)
                            .subscribeOn(getSchedulerProvider().io())
                            .observeOn(getSchedulerProvider().mainThread())
                            .subscribeWith(new ApiObserver(getView(), seqNum) {

                                @Override
                                protected void onFailureHttp(String errorMessage) {
                                    getView().hideProgress();
                                    getView().onException(errorMessage);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    getView().hideProgress();
                                    InfoCvvKKIResponse infoCvvKKIResponse = response.getData(InfoCvvKKIResponse.class);
                                    getView().getDataSuccessInfoCvvKKI(infoCvvKKIResponse);
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().hideProgress();
                                    getView().onException(restResponse.getDesc());
                                }
                            }));
        }
    }

    @Override
    public void initChangePinCc(String urlInitPinCc, String cardToken) {
        if (urlInitPinCc.isEmpty() || !isViewAttached()) return;

        String seqNumb = getBRImoPrefRepository().getSeqNumber();
        InitChangePinCCRequest request = new InitChangePinCCRequest(cardToken);

        getView().showProgress();

        getCompositeDisposable().add(
                getApiSource().getData(urlInitPinCc, request, seqNumb)
                        .subscribeOn(Schedulers.io())
                        .observeOn(getSchedulerProvider().mainThread())
                        .subscribeWith(new ApiObserver(getView(), seqNumb) {
                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().hideProgress();
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                getView().hideProgress();
                                InitChangePinResponse initChangePinResponse = response.getData(InitChangePinResponse.class);
                                String code = response.getCode();
                                if (code.equalsIgnoreCase(Constant.RE_SUCCESS)) {
                                    getView().onSuccessInitPin(initChangePinResponse.getRefNumber());
                                } else {
                                    getView().onException(response.getDesc());
                                }
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                getView().onException(restResponse.getDesc());
                            }
                        })
        );
    }

    @Override
    public void setUrlInfoCvvKKI(String url) {
        urlInfoCvvKKI = url;
    }

}