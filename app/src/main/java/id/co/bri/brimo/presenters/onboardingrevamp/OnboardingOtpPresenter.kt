package id.co.bri.brimo.presenters.onboardingrevamp

import com.google.gson.Gson
import id.co.bri.brimo.contract.IPresenter.onboardingrevamp.IOnboardingOtpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.onboardingrevamp.IOnboardingOtpView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimo.data.repository.category.CategoryPfmSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.config.AppConfig
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.onboardingrevamp.OnboardingResendReq
import id.co.bri.brimo.models.apimodel.request.onboardingrevamp.OnboardingValidateReq
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.onboardingrevamp.OnboardingOtpRes
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.Schedulers
import java.util.concurrent.TimeUnit

class OnboardingOtpPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBRImoPrefRepository: BRImoPrefSource,
    apiSource: ApiSource,
    categoryPfmSource: CategoryPfmSource,
    transaksiPfmSource: TransaksiPfmSource,
    anggaranPfmSource: AnggaranPfmSource
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    mBRImoPrefRepository,
    apiSource,
    transaksiPfmSource
), IOnboardingOtpPresenter<V> where V : IMvpView, V : IOnboardingOtpView {

    private var urlResend: String = ""
    private var urlValidate: String = ""

    override fun setUrlResend(url: String) {
        urlResend = url
    }

    override fun setUrlSend(url: String) {
        urlValidate = url
    }

    override fun getDeviceId(): String {
        return brImoPrefRepository.deviceId
    }

    override fun sendResendOtp(resendRequest: OnboardingResendReq) {
        if (urlResend.isEmpty() || !isViewAttached) return

        val seqNum = brImoPrefRepository.seqNumber
        view.showProgress()

        compositeDisposable.add(
            apiSource.getData(urlResend, resendRequest, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView().hideProgress()
                        getView().onExceptionNoBackAction(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        val onboardingOtpResponse =
                            response.getData(OnboardingOtpRes::class.java)
                        getView().hideProgress()
                        getView().onSuccessResend(onboardingOtpResponse)
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView().deletePin()
                        getView().hideProgress()
                        if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_STATUS_NOT_MATCH.value))
                            getView().onExceptionStatusNotMatch()
                        else getView().onExceptionRevamp(restResponse.desc)
                    }
                })
        )
    }

    override fun sendSendOtp(validateRequest: OnboardingValidateReq) {
        if (urlValidate.isEmpty() || !isViewAttached) return
        val seqNum = brImoPrefRepository.seqNumber
        view.showProgress()

        compositeDisposable.add(
            apiSource.getData(urlValidate, validateRequest, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT_ONBOARDING.toLong(), TimeUnit.SECONDS)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView().hideProgress()
                        getView().onExceptionNoBackAction(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView().hideProgress()
                        getView().onSuccessSend(Gson().toJson(response.data))
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView().deletePin()
                        getView().hideProgress()
                        if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_STATUS_NOT_MATCH.value))
                            getView().onExceptionStatusNotMatch()
                        else getView().onExceptionRevamp(restResponse.desc)
                    }
                })
        )
    }
}