package id.co.bri.brimo.presenters.onboardingrevamp

import id.co.bri.brimo.contract.IPresenter.onboardingrevamp.IOnboardingConfirmPinPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.onboardingrevamp.IOnboardingConfirmPinView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimo.data.repository.category.CategoryPfmSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.config.AppConfig
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.PinRequest
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.onboardingrevamp.OnboardingReceiptResponse
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.Schedulers
import java.util.concurrent.TimeUnit

class OnboardingConfirmPinPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBRImoPrefRepository: BRImoPrefSource,
    apiSource: ApiSource,
    categoryPfmSource: CategoryPfmSource,
    transaksiPfmSource: TransaksiPfmSource,
    anggaranPfmSource: AnggaranPfmSource
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    mBRImoPrefRepository,
    apiSource,
    transaksiPfmSource
), IOnboardingConfirmPinPresenter<V> where V : IMvpView, V : IOnboardingConfirmPinView {

    private var urlConfirmPin: String = ""

    override fun setUrlConfirmPin(url: String) {
        urlConfirmPin = url
    }

    override fun sendConfirmPin(pinRequest: PinRequest) {
        if (urlConfirmPin.isEmpty() || !isViewAttached) return

        view.showProgress()
        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable.add(
            apiSource.getData(urlConfirmPin, pinRequest, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT_ONBOARDING.toLong(), TimeUnit.SECONDS)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView().hideProgress()
                        getView().onExceptionNoBackAction(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView().hideProgress()
                        when (response.code) {
                            RestResponse.ResponseCodeEnum.RC_SUCCESS.value -> {
                                val receiptResponse =
                                    response.getData(OnboardingReceiptResponse::class.java)
                                getView().onSuccessConfirm(receiptResponse)
                            }

                            RestResponse.ResponseCodeEnum.RC_01.value ->
                                getView().onGenerateAccount()

                            RestResponse.ResponseCodeEnum.RC_02.value ->
                                getView().onGenerateUser()
                        }
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        val userExist = "C1"
                        val userRegis = "C2"
                        getView().deleteAllPin()
                        getView().hideProgress()
                        if (restResponse.code.equals(userExist))
                            getView().onExceptionLogin()
                        else if (restResponse.code.equals(userRegis))
                            getView().onExceptionRegistration()
                        else if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_STATUS_NOT_MATCH.value))
                            getView().onExceptionStatusNotMatch()
                        else
                            getView().onExceptionRevamp(restResponse.desc)
                    }
                })
        )
    }

    override fun getDeviceId(): String {
        return brImoPrefRepository.deviceId
    }
}