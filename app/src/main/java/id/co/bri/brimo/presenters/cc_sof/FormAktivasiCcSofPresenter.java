package id.co.bri.brimo.presenters.cc_sof;

import id.co.bri.brimo.contract.IPresenter.cc_sof.IFormAktivasiCcSofPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.cc_sof.IFormAktivasiCcSofView;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.apimodel.request.ActivateCcSofRequest;
import id.co.bri.brimo.models.apimodel.response.GeneralOtpResponse;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.presenters.MvpPresenter;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.schedulers.Schedulers;

public class FormAktivasiCcSofPresenter<V extends IMvpView & IFormAktivasiCcSofView>
        extends MvpPresenter<V> implements IFormAktivasiCcSofPresenter<V> {

    protected String urlAktivasi;

    public FormAktivasiCcSofPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void setUrl(String urlAktivasi) {
        this.urlAktivasi = urlAktivasi;
    }

    @Override
    public void getDataAktivasi(ActivateCcSofRequest activateCcSofRequest) {
        if (isViewAttached()) {
            getView().showProgress();

            String seqNum = getBRImoPrefRepository().getSeqNumber();

            getCompositeDisposable()
                    .add(getApiSource().getData(urlAktivasi, activateCcSofRequest, seqNum)
                            .subscribeOn(Schedulers.io())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribeWith(new ApiObserver(getView(),seqNum) {

                                @Override
                                protected void onFailureHttp(String errorMessage) {
                                    getView().hideProgress();
                                    getView().onException(errorMessage);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    getView().hideProgress();
                                    GeneralOtpResponse otpResponse = response.getData(GeneralOtpResponse.class);
                                    getView().onSuccessAktivasi(otpResponse);
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().hideProgress();
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                        getView().onSessionEnd(restResponse.getDesc());
                                    else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.getValue()))
                                        getView().onException93(restResponse.getDesc());
                                    else
                                        getView().onException(restResponse.getDesc());
                                }
                            }));
        }
    }
}