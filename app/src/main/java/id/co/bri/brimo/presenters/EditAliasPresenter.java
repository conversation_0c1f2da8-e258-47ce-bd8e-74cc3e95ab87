package id.co.bri.brimo.presenters;

import android.util.Log;

import id.co.bri.brimo.contract.IPresenter.IEditAliasPresenter;
import id.co.bri.brimo.contract.IView.IEditAliasView;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.apimodel.request.UpdateEditAlias;
import id.co.bri.brimo.models.apimodel.response.RestResponse;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.schedulers.Schedulers;

public class EditAliasPresenter <V extends IMvpView & IEditAliasView> extends MvpPresenter<V> implements IEditAliasPresenter<V> {

    private static final String TAG = "EditAliasPresenter";

    public EditAliasPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void setUpdateAlias(String url, String saveTitle, String account) {
        if (url == null || !isViewAttached()) {
            Log.d(TAG, "updateTitle : view atau urlInformasi null");
            return;
        }

        UpdateEditAlias updateSavedRequest = new UpdateEditAlias(account,saveTitle);
        String seqNum = getBRImoPrefRepository().getSeqNumber();

        getView().showProgress();
        getCompositeDisposable().add(getApiSource().getData(url, updateSavedRequest, seqNum)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(new ApiObserver(getView(),seqNum) {

                    @Override
                    protected void onFailureHttp(String errorMessage) {
                        getView().hideProgress();
                        getView().onException(errorMessage);
                    }

                    @Override
                    protected void onApiCallSuccess(RestResponse response) {
                        //TO-DO onSuccess
                        getView().hideProgress();

                        getView().onSuccessGetUpdateTitle(saveTitle);

                    }

                    @Override
                    protected void onApiCallError(RestResponse restResponse) {
                        getView().hideProgress();
                        if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                            getView().onSessionEnd(restResponse.getDesc());
                        else
                            getView().onException(restResponse.getDesc());

                    }
                }));

    }
}