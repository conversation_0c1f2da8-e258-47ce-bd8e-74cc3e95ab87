package id.co.bri.brimo.presenters.property

import id.co.bri.brimo.contract.IPresenter.property.IInputBillingPropertyPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.property.IInputBillingPropertyView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.property.InquiryPropertyRequest
import id.co.bri.brimo.models.apimodel.response.InquiryBrivaRevampResponse
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.disposables.CompositeDisposable

class InputBillingPropertyPresenter<V>(
    schedulerProvider: SchedulerProvider?,
    compositeDisposable: CompositeDisposable?,
    mBRImoPrefRepository: BRImoPrefSource?,
    apiSource: ApiSource?,
    transaksiPfmSource: TransaksiPfmSource?
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    mBRImoPrefRepository,
    apiSource,
    transaksiPfmSource
),

    IInputBillingPropertyPresenter<V> where V : IMvpView, V : IInputBillingPropertyView {
    private var inquiryUrl: String = ""
    override fun getDataInquiry(request: InquiryPropertyRequest) {
        if (inquiryUrl.isEmpty() || !isViewAttached) {
            return
        }
        view?.showProgress()
        val seqNum = brImoPrefRepository.seqNumber
        compositeDisposable.add(
            apiSource.getData(inquiryUrl, request, seqNum).subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView().onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView().hideProgress()
                        val responseData = response.getData(InquiryBrivaRevampResponse::class.java)
                        getView().onSuccessGetInquiry(responseData)
                    }

                        override fun onApiCallError(restResponse: RestResponse) {
                            onLoad = false
                            getView().hideProgress()
                            when(restResponse.code){
                                RestResponse.ResponseCodeEnum.RC_SESSION_END.value -> getView().onSessionEnd(restResponse.desc)
                                RestResponse.ResponseCodeEnum.RC_99.value,
                                RestResponse.ResponseCodeEnum.RC_FO.value -> getView().onException99(restResponse.desc)
                                RestResponse.ResponseCodeEnum.RC_88_BILL_ALREADY_PAID.value -> getView().onBillingAlreadyPaid(restResponse.desc)
                                else -> getView().onException(restResponse.desc)
                            }
                        }
                    })
            )
    }

    override fun setInquiryUrl(url: String) {
        inquiryUrl = url;
    }

}