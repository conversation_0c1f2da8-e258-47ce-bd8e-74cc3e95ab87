package id.co.bri.brimo.presenters.mutasi;

import java.util.concurrent.TimeUnit;

import androidx.annotation.Nullable;
import id.co.bri.brimo.contract.IPresenter.mutasi.IRequestDownloadMutationPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.mutasi.IRequestDownloadMutationView;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.config.AppConfig;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.apimodel.request.DownloadMutationDateRangeRequest;
import id.co.bri.brimo.models.apimodel.request.DownloadMutationMonthRequest;
import id.co.bri.brimo.models.apimodel.response.EmptyStateResponse;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.presenters.MvpPresenter;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.schedulers.Schedulers;

public class RequestDownloadMutationPresenter<V extends IMvpView & IRequestDownloadMutationView> extends MvpPresenter<V> implements IRequestDownloadMutationPresenter<V> {

    protected String url;
    Object request = null;

    public RequestDownloadMutationPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void setUrlSubmitRequest(String mUrl) {
        this.url = mUrl;
    }

    @Override
    public void submitRequest(@Nullable DownloadMutationMonthRequest downloadMutationMonthRequest, @Nullable DownloadMutationDateRangeRequest downloadMutationDateRangeRequest) {
        if (!isViewAttached() || url == null)
            return;

        getView().showProgress();

        String seq = getBRImoPrefRepository().getSeqNumber();

        if (downloadMutationMonthRequest != null) {
            request = downloadMutationMonthRequest;
        } else if (downloadMutationDateRangeRequest != null) {
            request = downloadMutationDateRangeRequest;
        }

        getCompositeDisposable().add(
                getApiSource().getData(url, request, seq)//function(param)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(Schedulers.single())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(new ApiObserver(getView(), seq) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().hideProgress();
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                getView().hideProgress();
                                EmptyStateResponse emptyMutationResponse = response.getData(EmptyStateResponse.class);
                                getView().onSuccessSubmitRequest(emptyMutationResponse);
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                onApiError(restResponse);
                            }
                        })
        );
    }
}
