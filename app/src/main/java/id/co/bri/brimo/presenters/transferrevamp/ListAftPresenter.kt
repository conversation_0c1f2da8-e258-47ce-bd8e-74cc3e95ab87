package id.co.bri.brimo.presenters.transferrevamp

import id.co.bri.brimo.contract.IPresenter.transferrevamp.IListAftPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.transferrevamp.IListAftView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimo.data.repository.category.CategoryPfmSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource

import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.revamptransfer.DetailAftRequest
import id.co.bri.brimo.models.apimodel.request.revamptransfer.UpdateStatusAftRequest
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.transferrevamp.DetailAftResponse
import id.co.bri.brimo.models.apimodel.response.transferrevamp.ListAftResponse
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.disposables.Disposable

class ListAftPresenter<V>(
    schedulerProvider: SchedulerProvider?,
    compositeDisposable: CompositeDisposable?,
    mBRImoPrefRepository: BRImoPrefSource?,
    apiSource: ApiSource?,
    categoryPfmSource: CategoryPfmSource?,
    transaksiPfmSource: TransaksiPfmSource?,
    anggaranPfmSource: AnggaranPfmSource?
) :
    MvpPresenter<V>(
        schedulerProvider,
        compositeDisposable,
        mBRImoPrefRepository,
        apiSource,
        transaksiPfmSource
    ),
    IListAftPresenter<V> where V : IMvpView?, V : IListAftView? {
    private var urlList: String? = null
    private var mUrlUpdateStatus: String? = null
    private var mUrlDetailAft: String? = null



    override fun getDataListAft() {
        if (!isViewAttached) {
            return
        }
        //set flag Loading
        val seqNum = brImoPrefRepository.seqNumber
        view?.showSkeleton(true)
        val disposable: Disposable =
            apiSource.getDataTanpaRequest(urlList, seqNum) //function(param)
                .subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView()?.showSkeleton(false)
                        getView()?.onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView()?.showSkeleton(false)
                        val listAftResponse: ListAftResponse = response.getData(ListAftResponse::class.java)
                        getView()?.onSuccessGetListAft(listAftResponse)
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView()?.showSkeleton(false)
                        if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value, ignoreCase = true))
                            getView()?.onSessionEnd(restResponse.desc)
                        else if (restResponse.code == RestResponse.ResponseCodeEnum.RC_99.value)
                            getView()?.onException99(restResponse.desc)
                        else
                            getView()?.onException(restResponse.desc)
                    }
                })
        compositeDisposable.add(disposable)


    }

    override fun updateStatusAft(idAft: String, status: Boolean, pin: String)  {
        if (!isViewAttached) {
            return
        }
        //set flag Loading
        view?.showProgress()
        val seqNum = brImoPrefRepository.seqNumber

        val updateStatusAftRequest = UpdateStatusAftRequest(idAft, status, pin)

        val disposable: Disposable =
            apiSource.getData(mUrlUpdateStatus, updateStatusAftRequest, seqNum) //function(param)
                .subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView()?.hideProgress()
                        getView()?.onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView()?.hideProgress()
                        getView()?.onSuccessUpdateAft(response.desc)
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView()?.hideProgress()
                        if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value, ignoreCase = true))
                            getView()?.onSessionEnd(restResponse.desc)
                        else if (restResponse.code == RestResponse.ResponseCodeEnum.RC_99.value)
                            getView()?.onException99(restResponse.desc)
                        else
                            getView()?.onException(restResponse.desc)
                    }
                })
        compositeDisposable.add(disposable)


    }

    override fun getDetailAft(idAft: String) {
        if (!isViewAttached) {
            return
        }
        //set flag Loading
        view?.showProgress()
        val seqNum = brImoPrefRepository.seqNumber
        val detailAftRequest = DetailAftRequest(idAft)

        val disposable: Disposable =
            apiSource.getData(mUrlDetailAft, detailAftRequest, seqNum) //function(param)
                .subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView()?.hideProgress()
                        getView()?.onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView()?.hideProgress()
                        val detailAftResponse : DetailAftResponse = response.getData(DetailAftResponse::class.java)
                        getView()?.onSuccessGetDetailAft(detailAftResponse)
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView()?.hideProgress()
                        if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value, ignoreCase = true))
                            getView()?.onSessionEnd(restResponse.desc)
                        else if (restResponse.code == RestResponse.ResponseCodeEnum.RC_99.value)
                            getView()?.onException99(restResponse.desc)
                        else
                            getView()?.onException(restResponse.desc)
                    }
                })
        compositeDisposable.add(disposable)

    }

    override fun setUrlDetailAft(urlDetailAft: String) {
        this.mUrlDetailAft = urlDetailAft
    }

    override fun setUrlListAft(urlList: String) {
        this.urlList = urlList
    }

    override fun setUrlUpdateStatus(urlUpdateStatus: String) {
        this.mUrlUpdateStatus = urlUpdateStatus
    }

    override fun start() {
        super.start()
    }

    override fun stop() {
        super.stop()
    }
}