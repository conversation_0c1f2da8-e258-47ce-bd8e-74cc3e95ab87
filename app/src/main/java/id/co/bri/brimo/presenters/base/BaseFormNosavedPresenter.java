package id.co.bri.brimo.presenters.base;

import id.co.bri.brimo.contract.IPresenter.base.IBaseFormNosavedPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.base.IBaseFormNosavedView;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.config.AppConfig;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.apimodel.request.FastInquiryBrivaRequest;
import id.co.bri.brimo.models.apimodel.request.InquiryBrivaRequest;
import id.co.bri.brimo.models.apimodel.response.BaseFormResponse;
import id.co.bri.brimo.models.apimodel.response.GeneralInquiryResponse;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.presenters.MvpPresenter;

import java.util.concurrent.TimeUnit;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.schedulers.Schedulers;

public class BaseFormNosavedPresenter<V extends IMvpView & IBaseFormNosavedView> extends MvpPresenter<V> implements IBaseFormNosavedPresenter<V> {

    private static final String TAG = "BaseFormPresenter";

    protected String inquiryUrl;
    protected String konfirmasiUrl;
    protected String paymentUrl;
    protected String formUrl;
    protected Object inquiryRequest = null;
    protected Boolean isFirstTime = true;


    private String saveId = "";
    private String purchaseType = "";
    private BaseFormResponse baseFormResponse = null;


    public BaseFormNosavedPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }


    @Override
    public void getDataForm() {
        if (formUrl == null || !isViewAttached()) {
            return;
        }

        String seqNum = getBRImoPrefRepository().getSeqNumber();

        getCompositeDisposable().add(
                getApiSource().getDataForm(formUrl, seqNum)//function(param)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(Schedulers.io())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(new ApiObserver(getView(),seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                //TO-DO onSuccess
                                if (response.getData() != null) {
                                    onApiSuccess(response);
                                }

                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                onApiError(restResponse);
                            }
                        })
        );

    }

    /**
     * method untuk memanggil data form dari fast menu
     */
    @Override
    public void getDataFormFastMenu() {
        if (!isViewAttached() || formUrl.isEmpty())
            return;

        String seqNum = getBRImoPrefRepository().getSeqNumber();

        getCompositeDisposable().add(
                getApiSource().getData(formUrl, getFastMenuRequest(),seqNum)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(getSchedulerProvider().single())
                        .observeOn(getSchedulerProvider().mainThread())
                        .subscribeWith(new ApiObserver(getView(),seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                getView().hideProgress();
                                if (response.getData() != null) {
                                    onApiSuccess(response);
                                }

                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
//                                {"code":"93","description":"Anda tidak diizinkan untuk melakukan transaksi fast menu. Silahkan login terlebih dahulu."}
                                onApiError(restResponse);
                            }
                        })
        );
    }

    /**
     * method untuk memanggil data form dari fast menu
     * Duplicate fungsi dari getDataFormFastMenu()
     * jika failed akan finish, contoh case : RC 93, Anda tidak diizinkan untuk melakukan transaksi fast menu
     */
    @Override
    public void getDataFormFastMenuFinishActivity() {
        if (!isViewAttached() || formUrl.isEmpty())
            return;

        String seqNum = getBRImoPrefRepository().getSeqNumber();

        getCompositeDisposable().add(
                getApiSource().getData(formUrl, getFastMenuRequest(),seqNum)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(getSchedulerProvider().single())
                        .observeOn(getSchedulerProvider().mainThread())
                        .subscribeWith(new ApiObserver(getView(),seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                getView().hideProgress();
                                if (response.getData() != null) {
                                    onApiSuccess(response);
                                }

                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                if (restResponse.getCode().equals(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.getValue())) {
                                    getView().onException12(restResponse.getDesc());

                                }
                                else{
                                    onApiError(restResponse);
                                }
                            }
                        })
        );
    }

    /**
     * @param billingNumber
     * @param isFromFastMenu
     */
    @Override
    public void getDataInquiry(String billingNumber, boolean isFromFastMenu) {
        if (inquiryUrl == null || !isViewAttached()) {
            return;
        }

        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();

        if(isFromFastMenu){
            inquiryRequest = new FastInquiryBrivaRequest(getFastMenuRequest(), billingNumber, "");
        }else {
            inquiryRequest = new InquiryBrivaRequest(billingNumber);
        }

        getCompositeDisposable()
                .add(getApiSource().getData(inquiryUrl, inquiryRequest, seqNum)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(Schedulers.io())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(new ApiObserver(getView(),seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                //TO-DO onSuccess
                                getView().hideProgress();
                                try {
                                    GeneralInquiryResponse responsebriva = response.getData(GeneralInquiryResponse.class);

                                    if (inquiryUrl != null && konfirmasiUrl != null)
                                        getView().onSuccessGetInquiry(responsebriva, konfirmasiUrl, paymentUrl, isFromFastMenu);
                                } catch (Exception e) {

                                }
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                    getView().onSessionEnd(restResponse.getDesc());
                                else
                                    getView().onException(restResponse.getDesc());

                            }
                        }));
    }




    /*
     * method untuk memproses restResponse jika berhasil mendapatkan balikan dari getDataForm()
     */
    @Override
    public void onApiSuccess(RestResponse response) {
        super.onApiSuccess(response);

        if (!isViewAttached())
            return;

        if (response != null)
            setBaseFormResponse(response.getData(BaseFormResponse.class));

        getView().isHideSkeleton(true);
        getView().onSuccessGetHistoryForm(getBaseFormResponse().getHistory());
        getView().onSuccessGetRestResponse(response);
        if (isFirstTime){
            getView().checkDataHistorySavedList();
            isFirstTime = false;
        }
    }


    /// override method untuk memproses exception dari backend servis
    @Override
    public void onApiError(RestResponse restResponse) {

        if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_12.getValue())) {
            getView().hideProgress();
            getView().onException12(restResponse.getDesc());
        }
        else if (restResponse.getCode().equals(RestResponse.ResponseCodeEnum.RC_99.getValue())){
            getView().onException99(restResponse.getDesc());
        }
        else {
            super.onApiError(restResponse);
        }
    }



    @Override
    public void setInquiryUrl(String url) {
        inquiryUrl = url;
    }

    @Override
    public void setKonfirmasiUrl(String url) {
        konfirmasiUrl = url;
    }

    @Override
    public void setPaymentUrl(String url) {
        paymentUrl = url;
    }

    @Override
    public void setFormUrl(String formUrl) {
        this.formUrl = formUrl;
    }


    public BaseFormResponse getBaseFormResponse() {
        return baseFormResponse;
    }

    public void setBaseFormResponse(BaseFormResponse baseFormResponse) {
        this.baseFormResponse = baseFormResponse;
    }


    @Override
    public void start() {
        super.start();
        setDisablePopup(false);
    }
}