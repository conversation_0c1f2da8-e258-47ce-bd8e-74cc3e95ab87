package id.co.bri.brimo.presenters.depositorevamp

import id.co.bri.brimo.contract.IPresenter.depositorevamp.IOnBoardingDepositoRevampPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.depositorevamp.IOnBoardingDepositoRevampView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimo.data.repository.category.CategoryPfmSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.config.AppConfig

import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.donasirevamp.ListUpdateDepositoRevampRequest
import id.co.bri.brimo.models.apimodel.response.EmptyRekeningResponse
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.TermConditionTabRes
import id.co.bri.brimo.models.apimodel.response.depositorevamp.GetListDepositoResponse
import id.co.bri.brimo.models.apimodel.response.depositorevamp.ListDepositoUpdateResponse
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.disposables.CompositeDisposable
import java.util.concurrent.TimeUnit

class OnboardingDepositoRevampPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBRImoPrefRepository: BRImoPrefSource,
    apiSource: ApiSource,
    categoryPfmSource: CategoryPfmSource,
    transaksiPfmSource: TransaksiPfmSource,
    anggaranPfmSource: AnggaranPfmSource
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    mBRImoPrefRepository,
    apiSource,
    transaksiPfmSource
), IOnBoardingDepositoRevampPresenter<V> where V : IMvpView, V : IOnBoardingDepositoRevampView {

    private var termUrl: String? = null
    private var url: String? = null

    override fun getTermData() {
        val seqNum = brImoPrefRepository.seqNumber
        view.showProgressTerm()
        compositeDisposable.add(
            apiSource.getData(termUrl, "", seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView().hideProgressTerm()
                        getView().onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        //TO-DO onSucces
                        getView().hideProgressTerm()
                        if (response.code.equals(RestResponse.ResponseCodeEnum.RC_SUCCESS.value, ignoreCase = true)) {
                            val termConditionTabRes =
                                response.getData(TermConditionTabRes::class.java)
                            getView().onGetDataTerm(termConditionTabRes)
                        } else if (response.code.equals(RestResponse.ResponseCodeEnum.RC_01.value, ignoreCase = true)) {
                            val rekeningResponse =
                                response.getData(EmptyRekeningResponse::class.java)
                            getView().onGetDataProses(rekeningResponse)
                        }
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView().hideProgressTerm()
                        if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value, ignoreCase = true))
                            getView().onSessionEnd(restResponse.desc)
                        else getView().onException(restResponse.desc)
                    }
                })
        )
    }


    override fun getRekeningDepositoUpdate(
        formUrl: String,
        type: String,
        pin: String,
        isFtu: Boolean
    ) {
        val seqNum = brImoPrefRepository.seqNumber
        view.showProgressTerm()
        val request = ListUpdateDepositoRevampRequest(type, pin, isFtu)
        compositeDisposable.add(
            apiSource.getData(formUrl, request, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String?) {
                        getView().hideProgress()
                        getView().onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse?) {
                        getView().hideProgress()
                        if (response!!.code == RestResponse.ResponseCodeEnum.RC_SUCCESS.value) {
                            val depositoResponse =
                                restResponse.getData(ListDepositoUpdateResponse::class.java)
                            getView().onSuccessRekeningDepositoListUpdate(depositoResponse)
                        } else if (response.code == RestResponse.ResponseCodeEnum.RC_03.value) {
                            val emptyRekeningResponse =
                                restResponse.getData(EmptyRekeningResponse::class.java)
                            getView().showDialogLimit(emptyRekeningResponse)
                        } else {
                            val emptyRekeningResponse =
                                restResponse.getData(EmptyRekeningResponse::class.java)
                            getView().onSuccessRekeningDepositoNoListUpdate(emptyRekeningResponse)
                        }

                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView().hideProgress()
                        if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value, ignoreCase = true)) {
                            getView().onSessionEnd(restResponse.desc)
                        } else {
                            getView().onException(restResponse.desc)
                        }
                    }

                    override fun onComplete() {
                        super.onComplete()
                    }
                })
        )
    }

    override fun setUrl(url: String) {
        this.url = url
    }

    override fun getListDepositoAccount() {
        if (url != null) {
            val seqNum = brImoPrefRepository.seqNumber
            compositeDisposable
                .add(
                    apiSource.getDataTanpaRequest(url, seqNum)
                        .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                        .subscribeOn(schedulerProvider.io())
                        .observeOn(schedulerProvider.mainThread())
                        .subscribeWith(object : ApiObserver(view, seqNum) {
                            override fun onFailureHttp(errorMessage: String) {
                                getView().hideProgress()
                                getView().onException(errorMessage)
                            }

                            override fun onApiCallSuccess(response: RestResponse) {
                                getView().hideProgress()
                                val getListDepositoRes = response.getData(
                                    GetListDepositoResponse::class.java
                                )
                                if (response.code.equals(RestResponse.ResponseCodeEnum.RC_01.value, ignoreCase = true)
                                    || getListDepositoRes.accountList.isEmpty()
                                ) {
                                    getView().onNoDepositoFound(getListDepositoRes)
                                } else if (response.code.equals(RestResponse.ResponseCodeEnum.RC_SUCCESS.value, ignoreCase = true)) {
                                    getView().onDashboardDepositoView(getListDepositoRes)
                                } else if (response.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value, ignoreCase = true))
                                    getView().onSessionEnd(restResponse.desc)

                            }

                            override fun onApiCallError(restResponse: RestResponse) {
                                getView().hideProgress()
                                if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value, ignoreCase = true)
                                ) getView().onSessionEnd(restResponse.desc)
                                else getView().onException(restResponse.desc)
                            }
                        })
                )
        }
    }

    override fun setTermUrl(url: String) {
        termUrl = url
    }


}