package id.co.bri.brimo.presenters.emas

import id.co.bri.brimo.contract.IPresenter.emas.IInquiryGoldMouldingPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.emas.IInquiryCetakEmasView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimo.data.repository.category.CategoryPfmSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.config.AppConfig
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.emas.ConfirmationCetakEmasRequest
import id.co.bri.brimo.models.apimodel.response.onExceptionWH
import id.co.bri.brimo.models.apimodel.response.GeneralConfirmationResponse
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.Schedulers
import java.util.concurrent.TimeUnit

class InquiryGoldMouldingPresenter<V>(schedulerProvider: SchedulerProvider?,
                                      compositeDisposable: CompositeDisposable?,
                                      mBRImoPrefRepository: BRImoPrefSource?, apiSource: ApiSource?,
                                      categoryPfmSource: CategoryPfmSource?,
                                      transaksiPfmSource: TransaksiPfmSource?,
                                      anggaranPfmSource: AnggaranPfmSource?) : MvpPresenter<V>(
        schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource),
        IInquiryGoldMouldingPresenter<V> where V : IMvpView?, V : IInquiryCetakEmasView ? {

    private var urlConfirmationCetakEmas: String? = null

    override fun setUrlKonfirmasiCetakEmas(url: String) {
        this.urlConfirmationCetakEmas = url
    }

    override fun getDataKonfirmasiCetakEmas(request: ConfirmationCetakEmasRequest) {
        if (isViewAttached) {

            view?.showProgress()
            val seqNum = brImoPrefRepository.seqNumber
            val listConnectableObservable =
                    apiSource.getData(urlConfirmationCetakEmas, request, seqNum).subscribeOn(
                            schedulerProvider.io()
                    ).timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                            .observeOn(schedulerProvider.mainThread())
                            .replay()
            compositeDisposable.add(
                    listConnectableObservable
                            .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                            .subscribeOn(Schedulers.io())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribeWith(object : ApiObserver(view, seqNum) {
                                override fun onFailureHttp(errorMessage: String) {
                                    getView()?.hideProgress()
                                    getView()?.onException(errorMessage)
                                }

                                override fun onApiCallSuccess(response: RestResponse) {
                                    getView()?.hideProgress()
                                    when(response.code){
                                        RestResponse.ResponseCodeEnum.RC_SUCCESS.value -> {
                                            val generalConfirmationResponse = response.getData(
                                                GeneralConfirmationResponse::class.java
                                            )
                                            getView()?.onGetDataKonfirmasi(generalConfirmationResponse)
                                        }
                                        RestResponse.ResponseCodeEnum.RC_01.value -> getView()?.onException01(restResponse.desc)
                                        RestResponse.ResponseCodeEnum.RC_02.value -> {
                                            val onOnExceptionWH: onExceptionWH =
                                                restResponse.getData(
                                                    onExceptionWH::class.java
                                                )
                                            getView()?.onException02(onOnExceptionWH)
                                        }
                                    }

                                }

                                override fun onApiCallError(restResponse: RestResponse) {
                                    getView()?.hideProgress()
                                    when(restResponse.code){
                                        RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.value -> getView()?.onExceptionTrxExpired(restResponse.desc)
                                        else -> getView()?.onException(restResponse.desc)
                                    }
                                }

                            })
            )
            listConnectableObservable.connect()
        }
    }

    override fun getDefaultSaldo() {
        var saldo = 0.0
        val saldoText = brImoPrefRepository.saldoRekeningUtama
        if (saldoText != "") {
            saldo = java.lang.Double.valueOf(saldoText)
        }
        var defaultAcc = ""
        if (brImoPrefRepository.accountDefault.isNotEmpty())
            defaultAcc = brImoPrefRepository.accountDefault

        var saldoString = ""
        if (brImoPrefRepository.saldoRekeningUtamaString.isNotEmpty())
            saldoString = brImoPrefRepository.saldoRekeningUtamaString

        val saldoHold = brImoPrefRepository.saldoHold

        view?.setDefaultSaldo(saldo, saldoString, defaultAcc, saldoHold)
    }

}