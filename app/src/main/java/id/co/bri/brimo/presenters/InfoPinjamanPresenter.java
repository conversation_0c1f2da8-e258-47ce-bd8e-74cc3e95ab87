package id.co.bri.brimo.presenters;

import java.util.ArrayList;
import java.util.concurrent.TimeUnit;

import id.co.bri.brimo.R;
import id.co.bri.brimo.contract.IPresenter.IInfoPinjamanPresenter;
import id.co.bri.brimo.contract.IView.IInfoPinjamanBriView;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.config.AppConfig;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.apimodel.request.InfoPinjamanRequest;
import id.co.bri.brimo.models.apimodel.request.PartnerIdRequest;
import id.co.bri.brimo.models.apimodel.response.GeneralWebviewResponse;
import id.co.bri.brimo.models.apimodel.response.InfoPinjamanResponse;
import id.co.bri.brimo.models.apimodel.response.ListPinjamanResponse;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.schedulers.Schedulers;

public class InfoPinjamanPresenter<V extends IMvpView & IInfoPinjamanBriView>
        extends MvpPresenter<V> implements IInfoPinjamanPresenter<V> {

    private String url;
    private String urlWebview;
    private ArrayList<ListPinjamanResponse.ListAccount> accountList = new ArrayList<>();

    public InfoPinjamanPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable,
                                 BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource,
                                 TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void getAccountWithLoan() {
        if (url == null || !isViewAttached()) {
            return;
        }

        String seqNum = getBRImoPrefRepository().getSeqNumber();
        getCompositeDisposable().add(
                getApiSource().getDataTanpaRequest(url, seqNum)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(Schedulers.io())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(new ApiObserver(getView(), seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                if (!getCompositeDisposable().isDisposed()) {
                                    if (accountList.size() > 0) {
                                        accountList.clear();
                                    }
                                    ListPinjamanResponse listPinjamanResponse = response.getData(ListPinjamanResponse.class);
                                    getView().onSuccessGetData(listPinjamanResponse);
                                    accountList.addAll(listPinjamanResponse.getListAccount());

                                    if (response.getData() != null) {
                                        onApiSuccess(response);
                                    }
                                }

                            }

                            @Override
                            public void onComplete() {
                                if (!getCompositeDisposable().isDisposed()) {
                                    InfoPinjamanRequest request;
                                    for (int i = 0; i < accountList.size(); i++) {
                                        request = new InfoPinjamanRequest(accountList.get(i).getAccount());
                                        getSisaPinjaman(request, i);
                                    }
                                }
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue())) {
                                    getCompositeDisposable().dispose();
                                    getView().onSessionEnd(restResponse.getDesc());
                                } else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.getValue())) {
                                    getView().onException93(restResponse.getDesc());
                                } else {
                                    getView().onException(restResponse.getDesc());
                                }
                            }
                        })
        );
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public void getSisaPinjaman(InfoPinjamanRequest request, int i) {
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        getCompositeDisposable().add(
                getApiSource().getData(GeneralHelper.getString(R.string.url_pinjaman_balance), request, seqNum)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(Schedulers.io())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(new ApiObserver(getView(), seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                if (!getCompositeDisposable().isDisposed()) {
                                    getSisaPinjaman(request, i);
                                }
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                if (!getCompositeDisposable().isDisposed()) {
                                    InfoPinjamanResponse sisaPinjamanResponse = response.getData(InfoPinjamanResponse.class);
                                    getView().onSuccessGetBalance(sisaPinjamanResponse, i);
                                    if (response.getData() != null) {
                                        onApiSuccess(response);
                                    }
                                }
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                if (!getCompositeDisposable().isDisposed()) {
                                    getView().onExceptionGetSisaPinjaman(i, restResponse.getDesc());
                                }
                            }
                        })
        );
    }

    @Override
    public void setUrlWebView(String urlWebView) {
        this.urlWebview = urlWebView;
    }

    @Override
    public void getWebView(PartnerIdRequest partnerIdRequest) {
        if (urlWebview == null || !isViewAttached()) {
            return;
        }

        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        getCompositeDisposable().add(
                getApiSource().getData(urlWebview, partnerIdRequest, seqNum)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(Schedulers.io())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(new ApiObserver(getView(), seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().hideProgress();
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                getView().hideProgress();
                                GeneralWebviewResponse webviewResponse = response.getData(GeneralWebviewResponse.class);
                                getView().onSuccessWebview(webviewResponse);
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue())) {
                                    getView().onSessionEnd(restResponse.getDesc());
                                } else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.getValue())) {
                                    getView().onException93(restResponse.getDesc());
                                } else {
                                    getView().onExceptionWebview(restResponse.getDesc());
                                }
                            }
                        })
        );
    }

}