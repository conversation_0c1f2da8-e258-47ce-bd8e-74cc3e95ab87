package id.co.bri.brimo.presenters.splitbill

import android.util.Log
import id.co.bri.brimo.contract.IPresenter.splitbill.ISplitBillSharePresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.splitbill.ISplitBillShareView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.config.AppConfig
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.splitbill.ItemMemberDraft
import id.co.bri.brimo.models.apimodel.request.splitbill.MemberDraft
import id.co.bri.brimo.models.apimodel.request.splitbill.ShareSplitBillRequest
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.splitbill.BillModel
import id.co.bri.brimo.models.apimodel.response.splitbill.ConfirmationSplitBillResponse
import id.co.bri.brimo.models.apimodel.response.splitbill.CurrentMemberResponse
import id.co.bri.brimo.models.apimodel.response.splitbill.Detail
import id.co.bri.brimo.models.apimodel.response.splitbill.DraftShareResponse
import id.co.bri.brimo.models.apimodel.response.splitbill.ItemData
import id.co.bri.brimo.models.apimodel.response.splitbill.ProcessSplitBillResponse
import id.co.bri.brimo.models.apimodel.response.splitbill.SplitBillDetailResponse
import id.co.bri.brimo.models.splitbill.SplitBillAddMemberItemViewModel
import id.co.bri.brimo.models.splitbill.SplitBillEditFormItemViewModel
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.disposables.Disposable
import io.reactivex.schedulers.Schedulers
import java.util.concurrent.TimeUnit

class SplitBillSharePresenter<V>(
    schedulerProvider: SchedulerProvider?,
    compositeDisposable: CompositeDisposable?,
    mBRImoPrefRepository: BRImoPrefSource?,
    apiSource: ApiSource?,
    transaksiPfmSource: TransaksiPfmSource?
) :
    MvpPresenter<V>(
        schedulerProvider,
        compositeDisposable,
        mBRImoPrefRepository,
        apiSource,
        transaksiPfmSource
    ), ISplitBillSharePresenter<V> where V : IMvpView?, V : ISplitBillShareView? {

    private var addedMembers =
        mutableListOf<SplitBillAddMemberItemViewModel>()
    private var currentMembers =
        mutableListOf<SplitBillAddMemberItemViewModel>()
    private var productItems = mutableListOf<SplitBillEditFormItemViewModel>()

    private var mUrlGetMember = ""
    private var mUrlConfirmation = ""
    private var mUrlDraftShare = ""
    private var mUrlHistoryBillList = ""
    private var mBillId = 0

    private var request: ShareSplitBillRequest? = null
    private var itemMemberShare: MutableList<ItemMemberDraft> = mutableListOf()

    private var mSplitBillDetailResponse: SplitBillDetailResponse? = null

    override fun setUrlGetMember(url: String) {
        mUrlGetMember = url
    }

    override fun setUrlConfirmation(urlConfirm: String) {
        mUrlConfirmation = urlConfirm
    }

    override fun setUrlDraftShare(urlDraft: String) {
        mUrlDraftShare = urlDraft
    }

    override fun setUrlHistoryList(urlHistory: String) {
        mUrlHistoryBillList = urlHistory
    }

    override fun fechData(splitBillDetailResponse: SplitBillDetailResponse) {
        if (splitBillDetailResponse.billModel != null &&
            splitBillDetailResponse.billModel.split != null
        ) {

            mSplitBillDetailResponse = splitBillDetailResponse
            mBillId = splitBillDetailResponse.billId

            splitBillDetailResponse.let { detailBill ->
                addedMembers.clear()
                addedMembers = detailBill.billModel.split?.members?.map {
                    SplitBillAddMemberItemViewModel(
                        id = it.id.toInt(),
                        name = it.identifier.takeIf { it.isNotEmpty() } ?: it.name,
                        accNumber = it.fmtAccountNumber,
                        alias = it.alias,
                        isListRemovable = true,
                        isChecked = true,
                        isNonBrimo = !it.isBrimo
                    )
                }?.toMutableList() ?: mutableListOf()

                val listMember: MutableList<List<SplitBillAddMemberItemViewModel>> = detailBill.billModel.split?.items
                    ?.map { item ->
                        item.members.mapNotNull { member ->
                            addedMembers.find { it.id == member.id }?.apply {
                                this.shareProductQty = member.itemShared
                            }
                        }
                    }?.toMutableList() ?: mutableListOf()

                val productsData = detailBill.billModel.split?.items?.mapIndexed { index, item ->
                    val memberInProduct = listMember[index]
                    SplitBillEditFormItemViewModel(
                        id = index.toLong(),
                        name = item.name,
                        quantity = item.quantity,
                        price = item.price,
                        members = memberInProduct.toMutableList()
                    )
                }?.toMutableList() ?: mutableListOf()

                productItems.clear()
                productItems.addAll(productsData)

                view?.apply {
                    showAddedMembers(addedMembers)
                    showProductItems(productItems)
                    showAmounts(detailBill.amountDetail)
                }

                validateSubmitButton()
            }
        }
    }

    override fun setData(
        processSplitBillResponse: ProcessSplitBillResponse,
        tempBillViewModel: MutableList<SplitBillEditFormItemViewModel>
    ) {

        mBillId = processSplitBillResponse.billId

        addedMembers.clear()
        addedMembers.addAll(
            listOf(
                SplitBillAddMemberItemViewModel(
                    id = processSplitBillResponse.userData.memberId,
                    name = processSplitBillResponse.userData.identifier,
                    accNumber = processSplitBillResponse.userData.fmtAccountNumber,
                    alias = processSplitBillResponse.userData.userAlias,
                    isListRemovable = true,
                    isChecked = true,
                    isNonBrimo = !processSplitBillResponse.userData.isBrimo
                )
            )
        )

        mSplitBillDetailResponse = SplitBillDetailResponse(
            billId = processSplitBillResponse.billId,
            amountDetail = processSplitBillResponse.amountDetails,
            billModel = BillModel(
                detail = Detail(
                    billName = processSplitBillResponse.billName,
                    transactionDate = processSplitBillResponse.billDate,
                    items = tempBillViewModel.map {
                        ItemData(
                            name = it.name,
                            price = it.price,
                            quantity = it.quantity
                        )
                    }
                )
            )
        )

        view?.showAddedMembers(addedMembers)

        productItems.clear()
        productItems.addAll(tempBillViewModel)

        view?.apply {
            showProductItems(productItems)
            showAmounts(processSplitBillResponse.amountDetails)
        }

        validateSubmitButton()

    }

    override fun updateAddOrDeleteMember(members: List<SplitBillAddMemberItemViewModel>) {
        addedMembers.clear()
        addedMembers.addAll(members)
        view?.showAddedMembers(addedMembers)

        productItems.forEach { it.members.clear() }
        view?.showProductItems(productItems)
        validateSubmitButton()
    }

    override fun getCurrentMembers() {
        if (isViewAttached) {

            val seqNum = brImoPrefRepository.seqNumber

            view?.showProgress()
            compositeDisposable.add(
                apiSource.getDataTanpaRequest(mUrlGetMember, seqNum)
                    .subscribeOn(schedulerProvider.io())
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(type: String) {
                            getView()?.onException(type)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            view?.hideProgress()
                            val membersResponse = response.getData(
                                CurrentMemberResponse::class.java
                            )

                            currentMembers.clear()
                            currentMembers = membersResponse.members.map {
                                SplitBillAddMemberItemViewModel(
                                    id = it.memberId,
                                    name = it.accountName,
                                    identifier = it.identifier,
                                    accNumber = it.accountNumber,
                                    alias = it.userAlias,
                                    isListRemovable = true,
                                    isNonBrimo = !it.isBrimo,
                                    username = it.username
                                )
                            }.toMutableList()

                            addedMembers.forEach { addedMember ->
                                currentMembers.find {
                                    it.id == addedMember.id
                                }?.isChecked = true
                            }

                            getView()?.onSuccessGetCurrentMembers(addedMembers, currentMembers)
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            view?.hideProgress()

                        }
                    })
            )
        }
    }

    override fun onResetAllSharedBillMember() {
        val list = mutableListOf<SplitBillEditFormItemViewModel>()
        productItems.mapTo(list) {
            it.copy(members = mutableListOf())
        }
        view?.showProductItems(list)
        productItems.clear()
        productItems.addAll(list)
        validateSubmitButton()
    }

    override fun onShareBillEvenly() {
        val newList = addedMembers.map { it.copy(shareProductQty = 1) }
        productItems.forEach {
            it.members = newList.toMutableList()
        }

        view?.showProductItems(productItems)
        validateSubmitButton()
    }

    override fun initShareProductMembers(product: SplitBillEditFormItemViewModel) {
        val newList = addedMembers.map { it.copy(shareProductQty = 0) }

        for (member in newList){
            for (memberInProduct in product.members){
                if (memberInProduct.id == member.id){
                    member.shareProductQty = memberInProduct.shareProductQty
                }
            }
        }

        if (newList.isEmpty()) {
            view?.showShareProductMembersDialog(product, newList, true)
        } else {
            view?.showShareProductMembersDialog(product, newList, false)
        }
    }

    override fun onProductSharedToMembers(
        product: SplitBillEditFormItemViewModel,
        members: List<SplitBillAddMemberItemViewModel>
    ) {
        val productFind = productItems.indexOf(product)

        productItems[productFind].members = members
            .filter { it.shareProductQty > 0 }
            .map { it.copy() }
            .toMutableList()

        view?.showProductItems(productItems)
        validateSubmitButton()
    }

    private fun validateSubmitButton() {
        view?.shouldEnableSubmitButton(addedMembers.isNotEmpty())
    }

    override fun onBackFromHistory() {
        mSplitBillDetailResponse?.let {
            view?.onBackFromHistory(it)
        }
    }

    private fun setRequest(): ShareSplitBillRequest {
        itemMemberShare = productItems.map {
            ItemMemberDraft(
                name = it.name,
                quantity = it.quantity,
                price = it.price,
                members = it.members.map { member ->
                    MemberDraft(
                        id = member.id,
                        itemShared = member.shareProductQty
                    )

                }
            )
        }.toMutableList()

        request = ShareSplitBillRequest(
            mBillId,
            itemMemberShare
        )

        return request as ShareSplitBillRequest
    }

    override fun getDraftShare() {
        if (mUrlDraftShare.isEmpty() || !isViewAttached) {
            return
        }

        view?.showProgress()

        val seqNum = brImoPrefRepository.seqNumber

        val disposable: Disposable = apiSource.getData(mUrlDraftShare, setRequest(), seqNum)
            .subscribeOn(schedulerProvider.io())
            .observeOn(schedulerProvider.mainThread())
            .subscribeWith(object : ApiObserver(view, seqNum) {
                override fun onFailureHttp(errorMessage: String) {
                    getView()?.hideProgress()
                }

                override fun onApiCallSuccess(response: RestResponse) {
                    getView()?.hideProgress()
                    val draftShareResponse = response.getData(
                        DraftShareResponse::class.java
                    )

                    getView()?.onSuccessGetDraftShare(draftShareResponse)
                }

                override fun onApiCallError(restResponse: RestResponse) {
                    view?.hideProgress()
                    view?.onException(restResponse.desc)
                }
            })
        compositeDisposable.add(disposable)
    }

    override fun getConfirmationSplitBill() {
        val unassignedMembers = findUnassignedMembers()

        if (unassignedMembers.isNotEmpty()) {
            getView()?.onValidationConfirmation(unassignedMembers)
            return
        }

        if (mUrlConfirmation.isEmpty() || !isViewAttached) {
            return
        }

        view?.showProgress()

        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable.add(
            apiSource.getData(mUrlConfirmation, setRequest(), seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(Schedulers.single())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView()?.hideProgress()
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView()?.hideProgress()
                        val confirmationSplitBillResponse = response.getData(
                            ConfirmationSplitBillResponse::class.java
                        )
                        getView()?.onSuccessGetConfirmation(
                            confirmationSplitBillResponse
                        )
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        view?.hideProgress()
                        view?.onException(restResponse.desc)
                    }
                })
        )
    }

    override fun onButtonSubmitClick() {
        val unassignedMembers = findUnassignedMembers()

        if (unassignedMembers.isNotEmpty()) {
            getView()?.onValidationConfirmation(unassignedMembers)
        } else {
            getConfirmationSplitBill()
        }
    }

    private fun findUnassignedMembers(): List<SplitBillAddMemberItemViewModel> {
        val assignedMemberIds = productItems
            .flatMap { it.members }
            .map { it.id }
            .toSet()

        return addedMembers.filter { it.id !in assignedMemberIds }
    }
}
