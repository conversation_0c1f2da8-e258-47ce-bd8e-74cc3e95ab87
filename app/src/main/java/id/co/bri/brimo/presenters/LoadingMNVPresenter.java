package id.co.bri.brimo.presenters;

import static android.content.Context.CONNECTIVITY_SERVICE;

import android.content.Context;
import android.net.ConnectivityManager;
import android.net.Network;
import android.net.NetworkCapabilities;
import android.net.NetworkRequest;
import android.net.Uri;
import android.os.Build;
import android.util.Log;

import com.chuckerteam.chucker.api.ChuckerInterceptor;

import java.io.IOException;
import java.net.SocketTimeoutException;
import java.util.ArrayList;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import id.co.bri.brimo.R;
import id.co.bri.brimo.contract.IPresenter.ILoadingMNVPresenter;
import id.co.bri.brimo.contract.IView.ILoadingMNVView;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.NetworkDns;
import id.co.bri.brimo.domain.config.AppConfig;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.apimodel.request.PinRefnumRequest;
import id.co.bri.brimo.models.apimodel.request.mnv.CheckMNVReq;
import id.co.bri.brimo.models.apimodel.request.mnv.HistoryCallback;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.models.apimodel.response.mnv.MnvCheckResponse;
import id.co.bri.brimo.models.apimodel.response.mnv.MnvResponse;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.disposables.Disposable;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;

public class LoadingMNVPresenter<V extends IMvpView & ILoadingMNVView>
        extends MvpPresenter<V> implements ILoadingMNVPresenter<V> {

    private String urlReqMNV;
    private String urlCheckMNV;
    private String tempUrlMNV;

    private int mnvHitMaxRetry = 0;
    private int mnvHitCounter = 1;

    private final ArrayList<HistoryCallback> historyCallbacks = new ArrayList<>();
    private ConnectivityManager connectivityManager;
    private NetworkRequest networkRequest;
    private ConnectivityManager.NetworkCallback networkCallback;

    public LoadingMNVPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable,
                               BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource,
                               CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource,
                               AnggaranPfmSource anggaranPfmSource) {

        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource,
                transaksiPfmSource);
    }

    @Override
    public void setUrlReqMNV(String urlReqMNV) {
        this.urlReqMNV = urlReqMNV;
    }

    @Override
    public void setUrlCheckMNV(String urlCheckMNV) {
        this.urlCheckMNV = urlCheckMNV;
    }

    /**
     * @noinspection unchecked
     */
    @Override
    public void onSendReqMNV(PinRefnumRequest request) {
        if (urlReqMNV.isEmpty() || !isViewAttached()) return;

        String seqNum = getBRImoPrefRepository().getSeqNumber();

        Disposable disposable = getApiSource().getData(urlReqMNV, request, seqNum)
                .subscribeOn(getSchedulerProvider().single())
                .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                .observeOn(getSchedulerProvider().mainThread())
                .subscribeWith(new ApiObserver(getView(), seqNum) {

                    @Override
                    protected void onFailureHttp(String errorMessage) {
                        getView().onException(errorMessage);
                    }

                    @Override
                    protected void onApiCallSuccess(RestResponse response) {
                        MnvResponse mnvResponse = response.getData(MnvResponse.class);
                        getBRImoPrefRepository().saveTokenKey(mnvResponse.getTokenKey());
                        mnvHitMaxRetry = mnvResponse.getMaxRetry();
                        getView().onSuccessGetReqMNV(mnvResponse);
                    }

                    @Override
                    protected void onApiCallError(RestResponse restResponse) {
                        onApiError(restResponse);
                    }
                });
        getCompositeDisposable().add(disposable);
    }

    /**
     * @noinspection unchecked
     */
    @Override
    public void onMNVCheck(CheckMNVReq request) {
        if (urlCheckMNV.isEmpty() || !isViewAttached()) return;

        String seqNum = getBRImoPrefRepository().getSeqNumber();

        Disposable disposable = getApiSource().getData(urlCheckMNV, request, seqNum)
                .subscribeOn(getSchedulerProvider().single())
                .observeOn(getSchedulerProvider().mainThread())
                .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                .subscribeWith(new ApiObserver(getView(), seqNum) {

                    @Override
                    protected void onFailureHttp(String errorMessage) {
                        getView().onException(errorMessage);
                    }

                    @Override
                    protected void onApiCallSuccess(RestResponse response) {
                        MnvCheckResponse mnvCheckResponse = response.getData(MnvCheckResponse.class);

                        getBRImoPrefRepository().saveUsername(mnvCheckResponse.getUsername());
                        getBRImoPrefRepository().saveUserType(Constant.IB_TYPE);
                        updateLoginFlag(true);
                        getView().onLoginSuccess();
                        getBRImoPrefRepository().saveStatusAktivasi(false);
                    }

                    @Override
                    protected void onApiCallError(RestResponse restResponse) {
                        if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_58.getValue()))
                            getView().showDialogCellularNotMatch(restResponse.getDesc());
                        else onApiError(restResponse);
                    }
                });

        getCompositeDisposable().add(disposable);
    }

    @Override
    public void setConnectionMnv(Context ctx, String url, String refNum, Integer maxRetry) {
        tempUrlMNV = url;
        mnvHitMaxRetry = maxRetry;
        registerNetworkCallback(ctx, refNum);

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            connectivityManager.requestNetwork(networkRequest, networkCallback, 5000);
        } else {
            connectivityManager.requestNetwork(networkRequest, networkCallback);
        }
    }

    @Override
    public void registerNetworkCallback(Context context, String refNum) {
        this.connectivityManager = (ConnectivityManager) context.getSystemService(CONNECTIVITY_SERVICE);
        this.networkRequest = new NetworkRequest.Builder()
                .addTransportType(NetworkCapabilities.TRANSPORT_CELLULAR)
                .addCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
                .build();

        if (networkCallback == null) {
            networkCallback = new ConnectivityManager.NetworkCallback() {
                @Override
                public void onAvailable(Network network) {
                    networkDnsMnv(network, tempUrlMNV, refNum);
                }

                @Override
                public void onUnavailable() {
                    getView().showDialogCheckDataCellular();
                }
            };
        }
    }

    @Override
    public void unregisterNetworkCallback() {
        if (networkCallback != null) {
            connectivityManager.unregisterNetworkCallback(networkCallback);
            networkCallback = null;
        }
    }

    private void networkDnsMnv(Network network, String url, String refNum) {
        NetworkDns dns = NetworkDns.getInstance();
        dns.setNetwork(network);

        ChuckerInterceptor chuckerInterceptor = new ChuckerInterceptor.Builder((Context) getView())
                .build();

        OkHttpClient.Builder httpBuilder = new OkHttpClient
                .Builder()
                .socketFactory(network.getSocketFactory())
                .dns(dns)
                .followRedirects(false)
                .followSslRedirects(false)
                .connectTimeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS);

        if (!GeneralHelper.isProd()) {
            httpBuilder.addInterceptor(chuckerInterceptor);
        }

        OkHttpClient okHttpClient = httpBuilder.build();
        Request.Builder mRequestBuilder = new Request.Builder().url(url);

        try {
            Response response = okHttpClient.newCall(mRequestBuilder.build()).execute();
            if (response == null) {
                getView().onException(Constant.KONEKSI_TERPUTUS);
            } else {
                Constant.ResponseMNV responseMNV = Constant.ResponseMNV.fromCode(response.code());
                switch (Objects.requireNonNull(responseMNV)) {
                    case RC302, RC303:
                        String responseHeader = response.header(Constant.LOCATION_MNV);
                        if (responseHeader != null) {
                            historyCallbacks.add(new HistoryCallback(mnvHitCounter, tempUrlMNV, responseHeader, String.valueOf(response.code())));
                            if ((responseHeader.contains(Constant.STATE_MNV) && responseHeader.contains(Constant.CODE_MNV) &&
                                    getStateFromUrl(responseHeader).length() < Constant.MAX_LENGHT_URL && responseHeader.contains(Constant.TELKOMSEL_MNV)) ||
                                    responseHeader.contains(Constant.ERROR_MNV))
                                getView().onSuccessUrlMnv(responseHeader, String.valueOf(response.code()), mnvHitCounter, refNum, historyCallbacks);
                            else
                                checkMnvHitCounter(responseHeader, String.valueOf(response.code()), refNum, historyCallbacks);
                        } else {
                            getView().showDialogCellularNotMatch(GeneralHelper.getString(R.string.nomor_tidak_sesuai));
                        }
                        break;
                    case RC400:
                        historyCallbacks.add(new HistoryCallback(mnvHitCounter, tempUrlMNV, tempUrlMNV, String.valueOf(response.code())));
                        getView().onSuccessUrlMnv(tempUrlMNV, String.valueOf(response.code()), mnvHitCounter, refNum, historyCallbacks);
                        break;
                    default:
                        getView().onException(Constant.SERVER_UNDER_MAINTENANCE);
                        break;
                }
            }
        } catch (SocketTimeoutException e) {
            getView().showDialogCheckDataCellular();
        } catch (IOException e) {
            if (!GeneralHelper.isProd()) {
                Log.e("networkDnsMnv: ", Objects.requireNonNull(e.getMessage()));
            }

            if (Objects.requireNonNull(e.getMessage()).contains(GeneralHelper.getString(R.string.eperm)))
                getView().showDialogCheckVpn();
            else if (e.getMessage().contains(GeneralHelper.getString(R.string.read_timed_out)))
                getView().onException(Constant.KONEKSI_TERPUTUS);
            else
                getView().onException(Constant.SERVER_UNDER_MAINTENANCE);
        }
    }

    private String getStateFromUrl(String url) {
        Uri uri = Uri.parse(url);
        return uri.getQueryParameter(Constant.STATE_MNV);
    }

    private void checkMnvHitCounter(String responseHeader, String codeStatus, String refNum, ArrayList<HistoryCallback> historyCallbacks) {
        this.mnvHitCounter += 1;
        if (this.mnvHitCounter <= mnvHitMaxRetry) {
            setConnectionMnv((Context) getView(), responseHeader, refNum, mnvHitMaxRetry);
        } else {
            getView().onSuccessUrlMnv(responseHeader, codeStatus, mnvHitCounter, refNum, historyCallbacks);
        }
    }
}