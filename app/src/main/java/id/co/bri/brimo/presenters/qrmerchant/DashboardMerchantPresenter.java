package id.co.bri.brimo.presenters.qrmerchant;

import id.co.bri.brimo.contract.IPresenter.qrmerchant.IDashboardMerchantPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.qrmerchant.IDashboardMerchantView;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.config.AppConfig;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.apimodel.request.DefaultMerchantRequest;
import id.co.bri.brimo.models.apimodel.response.LastTransactionResponse;
import id.co.bri.brimo.models.apimodel.response.MerchantDefaultMainResponse;
import id.co.bri.brimo.models.apimodel.response.MerchantStatusResponse;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.presenters.MvpPresenter;

import java.util.concurrent.TimeUnit;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.schedulers.Schedulers;

public class DashboardMerchantPresenter <V extends IMvpView & IDashboardMerchantView> extends MvpPresenter<V> implements IDashboardMerchantPresenter<V> {

    private static final String TAG = "DashboardMerchantPresenter";

    protected String url, urlSetDefault, urlLast;

    public DashboardMerchantPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    /**
     * method success status merchant dan save ke shared preference
     */
    @Override
    public void getStatusMerchant() {
        if (url == null || !isViewAttached()) {
            return;
        }

        String seqNum = getBRImoPrefRepository().getSeqNumber();

        getCompositeDisposable().add(
                getApiSource().getDataForm(url, seqNum)//function(param)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(Schedulers.single())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(new ApiObserver(getView(),seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                if (response.getCode().equals(RestResponse.ResponseCodeEnum.RC_SUCCESS.getValue())){
                                    MerchantStatusResponse merchantStatusResponse = response.getData(MerchantStatusResponse.class);
                                    getBRImoPrefRepository().saveMidMerchant(merchantStatusResponse.getMerchantList().get(0).getMerchantId());
                                    getBRImoPrefRepository().saveAccNumberMerchant(merchantStatusResponse.getMerchantList().get(0).getAccountNumber());
                                    getView().onSuccessgetStatus(merchantStatusResponse);
                                }
                                else if(response.getCode().equals(RestResponse.ResponseCodeEnum.RC_01.getValue())){
                                    getView().onException01(response.getDesc());
                                }
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                    getView().onSessionEnd(restResponse.getDesc());
                                else if(restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_12.getValue()))
                                    getView().onException12(restResponse.getDesc());
                                else
                                    getView().onException(restResponse.getDesc());
                            }
                        })
        );
    }

    @Override
    public void setUrlStatus(String url) {
        this.url = url;
    }

    @Override
    public void setUrlSetDefault(String url) {
        this.urlSetDefault = url;
    }

    @Override
    public void setUrlLast(String urlLast) {
        this.urlLast = urlLast;
    }

    /**
     * method sukses get 5 transaksi terakhir merchant
     */
    @Override
    public void getLastTransaction() {
        if (urlLast == null || !isViewAttached()) {
            return;
        }
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        getCompositeDisposable().add(
                getApiSource().getDataForm(urlLast, seqNum)//function(param)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(Schedulers.single())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(new ApiObserver(getView(),seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                               if(response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SUCCESS.getValue())){
                                   LastTransactionResponse lastTransactionResponse = response.getData(LastTransactionResponse.class);
                                   getView().onSuccessGetTransaction(lastTransactionResponse.getTransactions());
                               }
                               else if(response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_01.getValue())) getView().onSuccess01();
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                    getView().onSessionEnd(restResponse.getDesc());
                                else if(restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_12.getValue()))
                                    getView().onException12(restResponse.getDesc());
                                else
                                    getView().onException(restResponse.getDesc());
                            }
                        })
        );
    }

    @Override
    public void saveMid(String mId) {
        getBRImoPrefRepository().saveMidMerchant(mId);
    }

    /**
     * method untuk mengubah default merchant(merchant yg dipakai jika lebih dari 1)
     * @param mId
     * @param accNumb
     */
    @Override
    public void setDefaultMerchant(String mId, String accNumb) {
        if (urlSetDefault == null || !isViewAttached()) {
            return;
        }

        DefaultMerchantRequest defaultMerchantRequest = new DefaultMerchantRequest(mId, accNumb);

        String seqNum = getBRImoPrefRepository().getSeqNumber();

        getCompositeDisposable().add(
                getApiSource().getData(urlSetDefault, defaultMerchantRequest, seqNum)//function(param)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(Schedulers.io())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(new ApiObserver(getView(),seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                MerchantDefaultMainResponse merchantStatusResponse = response.getData(MerchantDefaultMainResponse.class);
                                getBRImoPrefRepository().saveMidMerchant(merchantStatusResponse.getMerchant().getMerchantId());
                                getBRImoPrefRepository().saveAccNumberMerchant(merchantStatusResponse.getMerchant().getAccountNumber());
                                getView().onSuccessSetDefault(merchantStatusResponse);
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                    getView().onSessionEnd(restResponse.getDesc());
                                else if(restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_12.getValue()))
                                    getView().onException12(restResponse.getDesc());
                                else
                                    getView().onException(restResponse.getDesc());
                            }
                        })
        );
    }
}