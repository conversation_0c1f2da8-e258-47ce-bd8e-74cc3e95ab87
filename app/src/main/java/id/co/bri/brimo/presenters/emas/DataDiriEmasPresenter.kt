package id.co.bri.brimo.presenters.emas

import id.co.bri.brimo.contract.IPresenter.emas.IDataDiriEmasPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.emas.IDataDiriEmasView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimo.data.repository.category.CategoryPfmSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.config.AppConfig

import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.response.CheckPengkinianResponse
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.emas.InquiryOpenEmasResponse
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.disposables.Disposable
import io.reactivex.schedulers.Schedulers
import java.util.concurrent.TimeUnit

class DataDiriEmasPresenter<V>(schedulerProvider: SchedulerProvider?,
                               compositeDisposable: CompositeDisposable?,
                               mBRImoPrefRepository: BRImoPrefSource?, apiSource: ApiSource?,
                               categoryPfmSource: CategoryPfmSource?,
                               transaksiPfmSource: TransaksiPfmSource?,
                               anggaranPfmSource: AnggaranPfmSource?) : MvpPresenter<V>(
    schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource
),
        IDataDiriEmasPresenter<V> where V : IMvpView?, V : IDataDiriEmasView {

    var urlData : String? = null
    var urlDataPengkinian : String? = null

    override fun setUrl(url: String) {
        this.urlData = url
    }

    override fun setUrlPengkinianData(urlPengkinianData: String?) {
        this.urlDataPengkinian = urlPengkinianData!!
    }

    override fun getDataDiriEmas() {
        view.showProgress()
        val seqNum = brImoPrefRepository.seqNumber
        val disposable: Disposable =
                apiSource.getData(urlData, "", seqNum) //function(param)
                        .subscribeOn(schedulerProvider.io())
                        .observeOn(schedulerProvider.mainThread())
                        .subscribeWith(object : ApiObserver(view, seqNum) {
                            override fun onFailureHttp(errorMessage: String) {
                                getView().onException(errorMessage)
                            }

                            override fun onApiCallSuccess(response: RestResponse) {
                                getView().hideProgress()

                                val response = response.getData(
                                        InquiryOpenEmasResponse::class.java
                                )

                                getView().onSuccessGetData(response)

                            }

                            override fun onApiCallError(restResponse: RestResponse) {
                                getView().hideProgress()
                                if (restResponse.code.equals(
                                                RestResponse.ResponseCodeEnum.RC_SESSION_END.value,
                                                ignoreCase = true
                                        )
                                ) getView().onSessionEnd(restResponse.desc) else getView().onException(
                                        restResponse.desc
                                )
                            }
                        })
        compositeDisposable.add(disposable)
    }

    override fun onCheckPengkinianData() {
        view.showProgress()
        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable.add(
                apiSource.getDataTanpaRequest(urlDataPengkinian, seqNum)
                        .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                        .subscribeOn(Schedulers.io())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(object : ApiObserver(view, seqNum) {
                            override fun onFailureHttp(errorMessage: String) {
                                getView().hideProgress()
                                getView().onException(errorMessage)
                            }

                            override fun onApiCallSuccess(response: RestResponse) {
                                getView().hideProgress()
                                val checkResponse = response.getData(
                                        CheckPengkinianResponse::class.java
                                )
                                if (response.code.equals(RestResponse.ResponseCodeEnum.RC_SUCCESS.value, ignoreCase = true)) {
                                    getView().onSuccessCheckPengkinian(checkResponse)
                                } else getView().onFailedCheckPengkinian(checkResponse)
                            }

                            override fun onApiCallError(restResponse: RestResponse) {
                                getView().hideProgress()
                                if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value, ignoreCase = true)) {
                                    getView().onSessionEnd(restResponse.desc)
                                } else getView().onException(
                                        restResponse.desc
                                )
                            }
                        })
        )
    }


}