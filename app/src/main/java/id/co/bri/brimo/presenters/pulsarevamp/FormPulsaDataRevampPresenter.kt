package id.co.bri.brimo.presenters.pulsarevamp

import android.util.Log
import id.co.bri.brimo.contract.IPresenter.pulsarevamp.IFormPulsaDataPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.pulsarevamp.IFormPulsaDataView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimo.data.repository.category.CategoryPfmSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.config.AppConfig
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.UpdateSavedRequestTfMethod
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.SavedResponse
import id.co.bri.brimo.models.apimodel.response.pulsarevamp.FormPulsaDataResponse
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.disposables.CompositeDisposable
import java.util.concurrent.TimeUnit

open class FormPulsaDataRevampPresenter<V>(schedulerProvider: SchedulerProvider?,
                                           compositeDisposable: CompositeDisposable?,
                                           mBRImoPrefRepository: BRImoPrefSource?, apiSource: ApiSource?,
                                           categoryPfmSource: CategoryPfmSource?,
                                           transaksiPfmSource: TransaksiPfmSource?,
                                           anggaranPfmSource: AnggaranPfmSource?) : MvpPresenter<V>(
    schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource
),
    IFormPulsaDataPresenter<V> where V : IMvpView?, V : IFormPulsaDataView? {

    var urlFormPulsa : String? = null
    var urlInquiryPulsa : String? = null
    var urlKonfirmasiPulsa : String? = null

    var saveId = ""
    var purchaseType = ""
    var tfMethod = ""

    val onLoad = false

    var formPulsaDataResponse = FormPulsaDataResponse()

    override fun setFormUrl(urlForm: String) {
        urlFormPulsa = urlForm
    }

    override fun setInquiryUrl(urlInquiry: String) {
        urlInquiryPulsa = urlInquiry
    }

    override fun setKonfirmasiUrl(urlConfirmation: String) {
        urlKonfirmasiPulsa = urlConfirmation
    }

    override fun getDataForm() {

        val seqNum = brImoPrefRepository.seqNumber
        val listConnectableObservable = apiSource.getDataForm(urlFormPulsa, seqNum).subscribeOn(
            schedulerProvider.io()
        )
            .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
            .observeOn(schedulerProvider.mainThread())
            .replay()
        compositeDisposable.add(
            listConnectableObservable
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView()!!.hideSkeleton(false)
                        getView()!!.onExceptionGetDataForm(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        formPulsaDataResponse = response.getData(FormPulsaDataResponse::class.java)

                        getView()!!.hideSkeleton(true)
                        getView()!!.onSuccessGetData(formPulsaDataResponse)

                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView()!!.hideSkeleton(false)
                        if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value, ignoreCase = true)) {
                            getView()!!.onSessionEnd(
                                restResponse.desc
                            )
                        } else getView()!!.onExceptionGetDataForm(restResponse.desc)
                    }

                })
        )
        listConnectableObservable.connect()
    }

    /**
     * method untuk memanggil data form dari fast menu
     */
    override fun getDataFormFastMenu() {
        val seqNum = brImoPrefRepository.seqNumber
        val listConnectableObservable = apiSource.getData(urlFormPulsa, getFastMenuRequest(), seqNum).subscribeOn(
            schedulerProvider.io()
        )
            .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
            .observeOn(schedulerProvider.mainThread())
            .replay()
        compositeDisposable.add(
            listConnectableObservable
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView()!!.hideSkeleton(false)
                        getView()!!.onExceptionGetDataForm(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView()!!.hideSkeleton(true)
                        formPulsaDataResponse = response.getData(FormPulsaDataResponse::class.java)
                        getView()!!.onSuccessGetData(formPulsaDataResponse)

                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView()!!.hideSkeleton(false)
                        if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value, ignoreCase = true)) {
                            getView()!!.onSessionEnd(
                                restResponse.desc
                            )
                        }
                        else  if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.value, ignoreCase = true)) {
                            getView()!!.onException93(
                                    restResponse.desc
                            )
                        }
                        else getView()!!.onExceptionGetDataForm(restResponse.desc)
                    }

                })
        )
        listConnectableObservable.connect()
    }

    /**
     * @param url
     * @param savedResponse
     * @param position
     * @param type
     */
    override fun setUpdateItemTf(url: String, savedResponse: SavedResponse, position: Int, type: Int) {
        if (url == null || !isViewAttached || onLoad) {
            Log.d("TAG", "setUpdateItemTf: XXXXXXXXXXX")
            return
        }
        onLoad = true
        val s = savedResponse.value
        val str1 = s.split("\\|".toRegex()).dropLastWhile { it.isEmpty() }
            .toTypedArray()
        if (str1.size > 1) {
            saveId = str1[0]
            purchaseType = str1[1]
        } else {
            saveId = str1[0]
        }
        if (str1.size > 3) {
            tfMethod = str1[3]
        }
        val updateSavedRequest = UpdateSavedRequestTfMethod(saveId, purchaseType, tfMethod)
        val seqNum = brImoPrefRepository.seqNumber
        compositeDisposable.add(
            apiSource.getData(url, updateSavedRequest, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        onLoad = false
                        getView()!!.onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        onLoad = false
                        getView()!!.hideProgress()
                        getView()!!.onSuccessUpdate(savedResponse, position, type)
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        onLoad = false
                        onApiError(restResponse)
                    }
                })
        )
    }

}