package id.co.bri.brimo.presenters.cc_sof

import id.co.bri.brimo.contract.IPresenter.cc_sof.IVerifikasiOtpBindingCcPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.cc_sof.IVerifikasiOtpBindingCcView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimo.data.repository.category.CategoryPfmSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.config.AppConfig
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.bindingcc.Code2Request
import id.co.bri.brimo.models.apimodel.request.bindingcc.MethodRequest
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.bindingcc.CcOtpResponse
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.Schedulers
import java.util.concurrent.TimeUnit

class VerifikasiOtpBindingCcPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBRImoPrefRepository: BRImoPrefSource,
    apiSource: ApiSource,
    categoryPfmSource: CategoryPfmSource,
    transaksiPfmSource: TransaksiPfmSource,
    anggaranPfmSource: AnggaranPfmSource
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    mBRImoPrefRepository,
    apiSource,
    transaksiPfmSource
), IVerifikasiOtpBindingCcPresenter<V> where V : IMvpView, V : IVerifikasiOtpBindingCcView {

    private lateinit var urlResend: String
    private lateinit var urlSend: String

    override fun setUrlResend(url: String) {
        urlResend = url
    }

    override fun setUrlSend(url: String) {
        urlSend = url
    }

    override fun sendResendOtp(resendReq: MethodRequest) {
        if (urlResend.isNotEmpty() && isViewAttached) {
            val seqNum = brImoPrefRepository.seqNumber
            view.showProgress()

            compositeDisposable.add(
                apiSource.getData(urlResend, resendReq, seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView().hideProgress()
                            getView().onException(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            getView().hideProgress()
                            val otpResponse =
                                response.getData(CcOtpResponse::class.java)
                            getView().onSuccessGetResend(otpResponse)
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView().deletePin()
                            getView().hideProgress()
                            getView().onExceptionRevamp(restResponse.desc)
                        }
                    })
            )
        }
    }

    override fun sendSendOtp(sendOtpReq: Code2Request) {
        if (urlSend.isNotEmpty() && isViewAttached) {
            val seqNum = brImoPrefRepository.seqNumber
            view.showProgress()

            compositeDisposable.add(
                apiSource.getData(urlSend, sendOtpReq, seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView().hideProgress()
                            getView().onException(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            getView()?.hideProgress()
                            getView().onSuccessSend()
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView().deletePin()
                            getView().hideProgress()
                            getView().onExceptionRevamp(restResponse.desc)
                        }
                    })
            )
        }
    }
}