package id.co.bri.brimo.presenters.tartunNDS;

import id.co.bri.brimo.contract.IPresenter.tartunNds.IKonfirmasiTartunNDSPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.base.IBaseFormView;
import id.co.bri.brimo.contract.IView.tartunNDS.IKonfirmasiTartunNDSVIew;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.presenters.base.BaseFormPresenter;
import io.reactivex.disposables.CompositeDisposable;

public class KonfirmasiTartunNDSPresenter <V extends IMvpView & IBaseFormView & IKonfirmasiTartunNDSVIew> extends BaseFormPresenter<V> implements IKonfirmasiTartunNDSPresenter<V> {

    public KonfirmasiTartunNDSPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, categoryPfmSource, transaksiPfmSource, anggaranPfmSource);
    }
}
