package id.co.bri.brimo.presenters.brivarevamp

import id.co.bri.brimo.contract.IPresenter.brivarevamp.IInquiryBrivaPartialRevampPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.brivarevamp.IInquiryBrivaPartialRevampView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.config.Constant.JourneyType
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.revampbriva.ConfirmationBrivaRevampRequest
import id.co.bri.brimo.models.apimodel.request.revampbriva.FastConfirmationBrivaRevampRequest
import id.co.bri.brimo.models.apimodel.response.GeneralConfirmationResponse
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.util.toRestResponseCodeEnum
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.disposables.CompositeDisposable

class InquiryBrivaPartialRevampPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBrimoPrefRepository: BRImoPrefSource,
    apiSource: ApiSource,
    transaksiPfmSource: TransaksiPfmSource
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    mBrimoPrefRepository,
    apiSource,
    transaksiPfmSource
), IInquiryBrivaPartialRevampPresenter<V> where V : IMvpView, V : IInquiryBrivaPartialRevampView {

    private lateinit var mUrlConfirm: String
    private lateinit var confirmationRequest: Any

    override fun getDefaultSaldo() {
        var saldo = 0.0
        val saldoText = brImoPrefRepository.saldoRekeningUtama
        if (saldoText != "") {
            saldo = java.lang.Double.valueOf(saldoText)
        }
        val defaultAcc = brImoPrefRepository.accountDefault
        val saldoString = brImoPrefRepository.saldoRekeningUtamaString
        val saldoHold = brImoPrefRepository.saldoHold
        view.setDefaultSaldo(saldo, saldoString, defaultAcc, saldoHold)
    }

    override fun setUrlConfirm(urlConfirm: String) {
        mUrlConfirm = urlConfirm
    }

    override fun getDataConfirmation(
        refNum: String,
        accountNum: String,
        amount: String,
        save: String,
        note: String,
        fromFast: Boolean,
        journeyType: String
    ) {
        if (mUrlConfirm.isEmpty() || !isViewAttached) return
        confirmationRequest = if (fromFast) {
            FastConfirmationBrivaRevampRequest(getFastMenuRequest(), refNum, accountNum, amount, save, note)
        } else {
            ConfirmationBrivaRevampRequest(refNum, accountNum, amount, save, note)
        }
        view.showProgress()
        val seqNum = brImoPrefRepository.seqNumber
        compositeDisposable.add(apiSource.getData(mUrlConfirm, confirmationRequest, seqNum)
            .subscribeOn(schedulerProvider.io())
            .observeOn(schedulerProvider.mainThread())
            .subscribeWith(object : ApiObserver(view, seqNum) {
                override fun onFailureHttp(errorMessage: String?) {
                    view.hideProgress()
                    getView().onException(errorMessage)
                }

                override fun onApiCallSuccess(response: RestResponse) {
                    view.hideProgress()
                    val confirmDataResponse = response.getData(
                        GeneralConfirmationResponse::class.java
                    )

                    if (journeyType == JourneyType.JOURNEY_TYPE_REVAMP_PENDIDIKAN_OPEN) {
                        val bodyUp = confirmDataResponse.detailDataView
                        val bodyDown = confirmDataResponse.amountDataView
                        confirmDataResponse.detailDataView = bodyDown
                        confirmDataResponse.transactionDataView = bodyUp
                    }

                    getView().onGetDataConfirmation(confirmDataResponse)
                }

                override fun onApiCallError(restResponse: RestResponse) {
                    getView().hideProgress()
                    when (restResponse.code.toRestResponseCodeEnum()) {
                        RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED -> getView().onExceptionTrxExpired(restResponse.desc)
                        RestResponse.ResponseCodeEnum.RC_12 -> getView().onException12(restResponse.desc)
                        else -> getView().onException(restResponse.desc)
                    }
                }
            })
        )
    }
}