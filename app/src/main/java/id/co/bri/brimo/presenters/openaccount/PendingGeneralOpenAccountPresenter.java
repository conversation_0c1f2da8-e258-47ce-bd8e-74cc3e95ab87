package id.co.bri.brimo.presenters.openaccount;

import java.util.concurrent.TimeUnit;

import id.co.bri.brimo.contract.IPresenter.openaccount.IPendingGeneralOpenAccountPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.openaccount.IPendingGeneralOpenAccountView;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.config.AppConfig;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.apimodel.request.PendingGeneralOpenAccountRequest;
import id.co.bri.brimo.models.apimodel.response.PendingResponse;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.presenters.MvpPresenter;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.schedulers.Schedulers;

public class PendingGeneralOpenAccountPresenter <V extends IMvpView & IPendingGeneralOpenAccountView> extends MvpPresenter<V> implements IPendingGeneralOpenAccountPresenter<V> {

    private String url;

    private static final String TAG = "PendingGeneralOpenAccount";

    public PendingGeneralOpenAccountPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void getCekStatus(String ref) {
        if(isViewAttached()){
            getView().showProgress();
            String seq = getBRImoPrefRepository().getSeqNumber();
            PendingGeneralOpenAccountRequest paymentGeneralOpenAccountRequest = new PendingGeneralOpenAccountRequest(ref);
            getCompositeDisposable().add(getApiSource().getData(url,paymentGeneralOpenAccountRequest, seq)
                    .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(new ApiObserver(getView(),seq) {
                        @Override
                        protected void onFailureHttp(String type) {
                            getView().hideProgress();
                            getView().onException(type);
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            getView().hideProgress();
                            PendingResponse pendingResponse = response.getData(PendingResponse.class);
                            if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SUCCESS.getValue())){
                                getView().onSuccessCekStatus(pendingResponse);
                            }else{
                                getView().onTransactionFailed(response.getDesc());
                            }
                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            getView().hideProgress();
                            if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                getView().onSessionEnd(restResponse.getDesc());
                            else if(restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.getValue())){
                                getView().onException93(restResponse.getDesc());
                            }
                            else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_99.getValue()))
                                getView().onException99(restResponse.getDesc());
                            else
                                getView().onException(restResponse.getDesc());
                        }
                    }));
        }
    }

    @Override
    public void setUrl(String urlPayment) {
        this.url = urlPayment;
    }
}