package id.co.bri.brimo.presenters.voucher

import id.co.bri.brimo.contract.IPresenter.voucher.IDetailVoucherPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.voucher.IDetailVoucherView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimo.data.repository.category.CategoryPfmSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.config.AppConfig
import id.co.bri.brimo.domain.config.Constant

import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.DetailVoucherStreamingRequest
import id.co.bri.brimo.models.apimodel.request.voucher.VoucherRequest
import id.co.bri.brimo.models.apimodel.response.GeneralConfirmationResponse
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.voucher.TutorialVoucherResponse
import id.co.bri.brimo.models.apimodel.response.voucher.VoucherDetail
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.Schedulers
import java.util.concurrent.TimeUnit

class DetailVoucherPresenter<V>(
    schedulerProvider: SchedulerProvider?,
    compositeDisposable: CompositeDisposable?,
    mBRImoPrefRepository: BRImoPrefSource?,
    apiSource: ApiSource?,
    categoryPfmSource: CategoryPfmSource?,
    transaksiPfmSource: TransaksiPfmSource?,
    anggaranPfmSource: AnggaranPfmSource?
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    mBRImoPrefRepository,
    apiSource,
    transaksiPfmSource
), IDetailVoucherPresenter<V> where V : IMvpView?, V : IDetailVoucherView? {

    private var mUrlInquiry: String = ""
    private var mUrlPayment: String = ""
    private var mUrlDetailGame: String = ""
    private var mUrlCaraRedeem: String = ""
    private lateinit var request: Any

    override fun setUrlDetailVoucher(urlDetail: String) {
        mUrlDetailGame = urlDetail
    }

    override fun setUrlCaraRedeem(urlCaraRedeem: String) {
        mUrlCaraRedeem = urlCaraRedeem
    }

    override fun setUrlPayment(urlPayment: String) {
        mUrlPayment = urlPayment
    }

    override fun setUrlInquiry(urlInquiry: String) {
        mUrlInquiry = urlInquiry
    }

    override fun getDetailVoucher(voucherRequest: VoucherRequest) {
        if (isViewAttached) {

            val seqNum = brImoPrefRepository.seqNumber

            view!!.isHideSkeleton(false)

            compositeDisposable.add(
                apiSource.getData(mUrlDetailGame, voucherRequest, seqNum)
                    .subscribeOn(schedulerProvider.io())
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(type: String) {
                            getView()!!.isHideSkeleton(true)
                            getView()!!.onException(type)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            val voucherResponse = response.getData(
                                VoucherDetail::class.java
                            )
                            getView()!!.isHideSkeleton(true)
                            getView()!!.onSuccessGetDetail(voucherResponse)

                            if (!GeneralHelper.isProd()) {
                                GeneralHelper.responseChuck(response)
                            }
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value, ignoreCase = true)) {
                                getView()!!.onSessionEnd(restResponse.desc)
                            } else {
                                getView()!!.isHideSkeleton(true)
                                getView()!!.onException(restResponse.desc)
                            }
                        }
                    })
            )
        }
    }

    override fun getDataInquiry(voucherId: String, productCode: String, voucherType: String) {
        if (mUrlInquiry.isEmpty() || !isViewAttached) {
            return
        }

        view?.showProgress()

        val seqNum = brImoPrefRepository.seqNumber
        if (voucherType.equals(Constant.Voucher.GAME.name)) {
            request = VoucherRequest(voucherId.toInt(), productCode)
        } else if (voucherType.equals(Constant.Voucher.STREAMING.name)) {
            request = VoucherRequest(voucherId, productCode)
        }


        compositeDisposable.add(
            apiSource.getData(mUrlInquiry, request, seqNum).subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView()?.onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView()?.hideProgress()
                        val responseInquiry =
                            response.getData(GeneralConfirmationResponse::class.java)
                        getView()?.onSuccessGetInquiry(responseInquiry, mUrlPayment)

                        if (!GeneralHelper.isProd()) {
                            GeneralHelper.responseChuck(response)
                        }
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        onApiError(restResponse)
                        getView()?.onException(restResponse.desc)

                    }
                })
        )
    }

    override fun getCaraRedeemVoucherGame(voucherGameRequest: VoucherRequest) {
        if (isViewAttached) {

            val seqNum = brImoPrefRepository.seqNumber

            view!!.showProgress()

            compositeDisposable.add(
                apiSource.getData(mUrlCaraRedeem, voucherGameRequest, seqNum)
                    .subscribeOn(schedulerProvider.io())
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(type: String) {
                            getView()!!.hideProgress()
                            getView()!!.onException(type)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            val caraRedeemVoucherResponse = response.getData(
                                TutorialVoucherResponse::class.java
                            )
                            getView()!!.hideProgress()
                            getView()!!.onSuccessGetTutorial(caraRedeemVoucherResponse)

                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView()!!.hideProgress()
                            if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value, ignoreCase = true)) {
                                getView()!!.onSessionEnd(restResponse.desc)
                            } else {
                                getView()!!.onException(restResponse.desc)
                            }

                        }
                    })
            )
        }
    }

    override fun getCaraRedeemVoucherStreaming(streamingRequest: DetailVoucherStreamingRequest) {
        if (isViewAttached) {

            val seqNum = brImoPrefRepository.seqNumber

            view!!.showProgress()

            compositeDisposable.add(
                apiSource.getData(mUrlCaraRedeem, streamingRequest, seqNum)
                    .subscribeOn(schedulerProvider.io())
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(type: String) {
                            getView()!!.hideProgress()
                            getView()!!.onException(type)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            val caraRedeemVoucherResponse = response.getData(
                                TutorialVoucherResponse::class.java
                            )
                            getView()!!.hideProgress()
                            getView()!!.onSuccessGetTutorial(caraRedeemVoucherResponse)
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView()!!.hideProgress()
                            if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value, ignoreCase = true)) {
                                getView()!!.onSessionEnd(restResponse.desc)
                            } else {
                                getView()!!.onException(restResponse.desc)
                            }
                        }
                    })
            )
        }
    }

}