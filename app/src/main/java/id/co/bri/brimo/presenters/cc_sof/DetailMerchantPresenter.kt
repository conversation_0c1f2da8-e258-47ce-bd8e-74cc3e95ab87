package id.co.bri.brimo.presenters.cc_sof

import id.co.bri.brimo.contract.IPresenter.cc_sof.IDetailMerchantPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.cc_sof.IDetailMerchantView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.config.AppConfig
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.bindingcc.UnbindingRequest
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.Schedulers
import java.util.concurrent.TimeUnit

class DetailMerchantPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBRImoPrefRepository: BRImoPrefSource,
    apiSource: ApiSource,
    transaksiPfmSource: TransaksiPfmSource,
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    mBRImoPrefRepository,
    apiSource,
    transaksiPfmSource
), IDetailMerchantPresenter<V> where V : IMvpView, V : IDetailMerchantView {

    private var url = ""
    override fun setUnbindingUrl(url: String) {
        this.url = url
    }

    override fun submitUnbinding(unbindingRequest: UnbindingRequest) {
        if (url.isEmpty() || !isViewAttached) return

        view.showProgress()
        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable.add(
            apiSource.getData(url, unbindingRequest, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView().hideProgress()
                        getView().onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView().hideProgress()
                        getView().onSuccessUnbinding()
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView().hideProgress()
                        onApiError(restResponse)
                    }
                })
        )
    }
}