package id.co.bri.brimo.presenters.lonceng;

import java.util.concurrent.TimeUnit;

import id.co.bri.brimo.contract.IPresenter.lonceng.INotifikasiLoncengPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.lonceng.INotifikasiLoncengView;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.config.AppConfig;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.apimodel.request.DetailPromoRequest;
import id.co.bri.brimo.models.apimodel.request.ListNotifLoncengRequest;
import id.co.bri.brimo.models.apimodel.request.ListNotificationRequest;
import id.co.bri.brimo.models.apimodel.request.NotifReadsRequest;
import id.co.bri.brimo.models.apimodel.request.NotifUnreadsRequest;
import id.co.bri.brimo.models.apimodel.request.splitbill.NotifikasiSplitBillRequest;
import id.co.bri.brimo.models.apimodel.response.DataInfoResponse;
import id.co.bri.brimo.models.apimodel.response.GeneralInquiryResponse;
import id.co.bri.brimo.models.apimodel.response.lifestyle.ConfirmationLifestyleResponse;
import id.co.bri.brimo.models.apimodel.response.lifestyle.ekspedisi.KonfirmasiWebviewEkspedisiResponse;
import id.co.bri.brimo.models.apimodel.response.lifestyle.ekspedisi.NotifikasiMokirimRequest;
import id.co.bri.brimo.models.apimodel.response.notificationlonceng.ListNotificationResponse;
import id.co.bri.brimo.models.apimodel.response.NotifReadResponse;
import id.co.bri.brimo.models.apimodel.response.PromoResponse;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.models.apimodel.response.notificationlonceng.EmptyStateListNotificationResponse;
import id.co.bri.brimo.presenters.MvpPresenter;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.schedulers.Schedulers;

/**
 * Created by user on 19/01/2021
 */
public class NotifikasiLoncengPresenter<V extends IMvpView & INotifikasiLoncengView>
        extends MvpPresenter<V> implements INotifikasiLoncengPresenter<V> {

    private static final String TAG = "NotifikasiLoncengPresenter";
    protected String urlListNotif, urlListNotificationNew, urlReadAllNotification;
    protected String urlReadNotif;
    protected String urlPromo;

    protected boolean isLoadingItem = false;

    protected ListNotifLoncengRequest notifLoncengRequest;
    protected NotifReadsRequest notifReadRequest;
    protected DetailPromoRequest detailPromoRequest;
    protected String inquiryUrl;

    protected ListNotificationRequest listNotificationRequest;

    public NotifikasiLoncengPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable,
                                      BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource,
                                      TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void setUrlReadNotif(String urlReadNotif) {
        this.urlReadNotif = urlReadNotif;
    }

    @Override
    public void setUrlPromo(String urlPromo) {
        this.urlPromo = urlPromo;
    }

    @Override
    public void getReadNotif(String id) {
        if (isViewAttached()) {
            notifReadRequest = new NotifReadsRequest("android", id);
            String seqNum = getBRImoPrefRepository().getSeqNumber();

            getCompositeDisposable()
                    .add(getApiSource().getData(urlReadNotif, notifReadRequest, seqNum)
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .subscribeOn(getSchedulerProvider().single())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribeWith(new ApiObserver(getView(), seqNum) {
                                @Override
                                protected void onFailureHttp(String errorMessage) {
                                    getView().hideProgress();
                                    getView().onException(errorMessage);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    NotifReadResponse notifReadResponse = response.getData(NotifReadResponse.class);
                                    getView().getSuccessReadNotif(notifReadResponse.getStatusRes().isSuccess());
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                        getView().onSessionEnd(restResponse.getDesc());
                                    else
                                        getView().onException(restResponse.getDesc());

                                }
                            }));
        }
    }

    @Override
    public void getDetailPromo(String id) {
        if (isLoadingItem) {
            return;
        } else {

            if (isViewAttached()) {
                isLoadingItem = true;
                getView().showProgress();

                detailPromoRequest = new DetailPromoRequest(id);
                String seqNum = getBRImoPrefRepository().getSeqNumber();

                getCompositeDisposable().add(
                        getApiSource().getData(urlPromo, detailPromoRequest, seqNum)//function(param)
                                .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                                .subscribeOn(Schedulers.single())
                                .observeOn(AndroidSchedulers.mainThread())
                                .subscribeWith(new ApiObserver(getView(), seqNum) {

                                    @Override
                                    protected void onFailureHttp(String errorMessage) {
                                        isLoadingItem = false;
                                        getView().hideProgress();
                                        getView().onException(errorMessage);
                                    }

                                    @Override
                                    protected void onApiCallSuccess(RestResponse response) {
                                        getView().hideProgress();
                                        isLoadingItem = false;
                                        //TO-DO onSuccess
                                        PromoResponse promoResponse = response.getData(PromoResponse.class);
                                        getView().onSuccessDetailPromo(promoResponse);
                                    }

                                    @Override
                                    protected void onApiCallError(RestResponse restResponse) {
                                        getView().hideProgress();
                                        isLoadingItem = false;
                                        if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue())) {
                                            getView().onSessionEnd(restResponse.getDesc());
                                        } else
                                            getView().onException(restResponse.getDesc());
                                    }
                                })
                );
            }
        }
    }

    @Override
    public void getPromoProduct(String promoId, String blastId, String additional) {
        if (isLoadingItem) {
            return;
        } else {
            if (urlPromo != null || isViewAttached()) {
                getView().showProgress();
                isLoadingItem = true;
                detailPromoRequest = new DetailPromoRequest(promoId);
                String seqNum = getBRImoPrefRepository().getSeqNumber();
                getCompositeDisposable()
                        .add(getApiSource().getData(urlPromo, detailPromoRequest, seqNum)
                                .subscribeOn(Schedulers.io())
                                .observeOn(AndroidSchedulers.mainThread())
                                .subscribeWith(new ApiObserver(getView(), seqNum) {


                                    @Override
                                    protected void onFailureHttp(String errorMessage) {
                                        getView().hideProgress();
                                        getView().onException(errorMessage);
                                        isLoadingItem = false;
                                    }

                                    @Override
                                    protected void onApiCallSuccess(RestResponse response) {
                                        //TO-DO onSuccess
                                        getView().hideProgress();
                                        PromoResponse promoResponse = response.getData(PromoResponse.class);
                                        getView().onSuccessGetDetailPromoProduct(promoResponse, promoId, blastId, additional);
                                        isLoadingItem = false;
                                    }

                                    @Override
                                    protected void onApiCallError(RestResponse restResponse) {
                                        getView().hideProgress();
                                        isLoadingItem = false;
                                        if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                            getView().onSessionEnd(restResponse.getDesc());
                                        else
                                            getView().onException(restResponse.getDesc());
                                    }
                                }));

            }
        }
    }

    @Override
    public void getBlastId(String blastId) {
        int blast = Integer.parseInt(blastId);
        if (blast >= getBRImoPrefRepository().getBlastId()) {
            getBRImoPrefRepository().saveBlastId(blast);
        }
    }

    @Override
    public void getInquiryLonceng(String typeInquiry,String reqSeq, String urlKonfirmasi, String urlPayment, String title, String typeInquiryRevamp) {
        if (inquiryUrl == null || !isViewAttached()) {
            return;
        }

        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        getCompositeDisposable()
                .add(getApiSource().getDataNotif(inquiryUrl, reqSeq, seqNum)
                        .subscribeOn(Schedulers.io())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(new ApiObserver(getView(), seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                getView().hideProgress();
                                if  (typeInquiryRevamp != null && !typeInquiryRevamp.isEmpty()){
                                    getView().onSuccessGetInquiryRevamp(response, urlKonfirmasi,urlPayment, title,typeInquiry, typeInquiryRevamp, reqSeq);
                                } else if(typeInquiry.equalsIgnoreCase(Constant.PaymentReminderTypeInquiry.listrikPrepaid)){
                                    getView().onSuccessGetInquiryRevamp(response, urlKonfirmasi,urlPayment, title,typeInquiry, typeInquiryRevamp, reqSeq);
                                } else {
                                    GeneralInquiryResponse generalInquiryResponse = response.getData(GeneralInquiryResponse.class);
                                    getView().onSuccessGetInquiry(generalInquiryResponse, urlKonfirmasi,urlPayment, title,typeInquiry);
                                }
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                onApiError(restResponse);
                            }
                        }));


    }

    @Override
    public void setInquiryUrl(String url) {
        inquiryUrl = url;
    }

    @Override
    public void setUrlListNotification(String url) {
        this.urlListNotificationNew = url;
    }

    @Override
    public void getListNotification(String lastId, boolean isRefresh, String filter) {
        if (isViewAttached()) {
            listNotificationRequest = new ListNotificationRequest(
                    "10", lastId, "android", filter);

            String seqNum = getBRImoPrefRepository().getSeqNumber();

            getCompositeDisposable()
                    .add(getApiSource().getData(urlListNotificationNew, listNotificationRequest, seqNum)
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .subscribeOn(getSchedulerProvider().single())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribeWith(new ApiObserver(getView(), seqNum) {
                                @Override
                                protected void onFailureHttp(String type) {
                                    getView().hideProgress();
                                    getView().onExceptionNoConnect();
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    getView().hideProgress();
                                    if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SUCCESS.getValue())) {
                                        ListNotificationResponse listNotificationResponse = response.getData(ListNotificationResponse.class);
                                        getView().onSuccessGetListNotification(listNotificationResponse, isRefresh);
                                    } else if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_01.getValue()) ) {
                                        EmptyStateListNotificationResponse emptyStateResponse = response.getData(EmptyStateListNotificationResponse.class);
                                        getView().showEmptyState(emptyStateResponse);
                                    }
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().hideProgress();
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                        getView().onSessionEnd(restResponse.getDesc());
                                    else
                                        getView().onViewRobot();
                                }
                            }));
        }
    }

    @Override
    public void setUrlReadAllNotification(String url) {
        this.urlReadAllNotification = url;
    }

    @Override
    public void readAllNotification() {
        if (isViewAttached()) {
            NotifUnreadsRequest request = new NotifUnreadsRequest(
                    "android"
            );

            getView().showProgress();

            String seqNum = getBRImoPrefRepository().getSeqNumber();

            getCompositeDisposable()
                    .add(getApiSource().getData(urlReadAllNotification, request, seqNum)
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .subscribeOn(getSchedulerProvider().single())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribeWith(new ApiObserver(getView(), seqNum) {
                                @Override
                                protected void onFailureHttp(String type) {
                                    getView().hideProgress();
                                    getView().onExceptionNoConnect();
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    getView().hideProgress();
                                    if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SUCCESS.getValue())) {
                                        DataInfoResponse infoResponse = response.getData(DataInfoResponse.class);
                                        getView().onSuccessReadAllNotification(infoResponse.getMessage());
                                    } else if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_01.getValue())) {
                                        EmptyStateListNotificationResponse emptyStateResponse = response.getData(EmptyStateListNotificationResponse.class);
                                        getView().showEmptyState(emptyStateResponse);
                                    }
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().hideProgress();
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                        getView().onSessionEnd(restResponse.getDesc());
                                    else
                                        getView().onViewRobot();
                                }
                            }));
        }
    }

    @Override
    public void getDataNotif(String reqContent, String urlData, String typeNotif) {
        if (urlData == null || !isViewAttached()) {
            return;
        }

        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        getCompositeDisposable()
                .add(getApiSource().getDataNotif(urlData, reqContent, seqNum)
                        .subscribeOn(getSchedulerProvider().io())
                        .observeOn(getSchedulerProvider().mainThread())
                        .subscribeWith(new ApiObserver(getView(),seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().hideProgress();
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                getView().hideProgress();
                                getView().onSuccessGetDataNotif(response, typeNotif);
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                onApiError(restResponse);
                            }
                        }));
    }

    @Override
    public void getConfirmationMokirim(NotifikasiMokirimRequest notifikasiMokirimRequest, String urlPaymentMokirim) {
        if (inquiryUrl == null || !isViewAttached()) {
            return;
        }

        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        getCompositeDisposable()
                .add(getApiSource().getData(inquiryUrl, notifikasiMokirimRequest, seqNum)
                        .subscribeOn(getSchedulerProvider().io())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(new ApiObserver(getView(), seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().hideProgress();
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                getView().hideProgress();
                                KonfirmasiWebviewEkspedisiResponse konfirmasiWebviewEkspedisiResponse =
                                        response.getData(KonfirmasiWebviewEkspedisiResponse.class);

                                getView().onSuccessGetConfirmationMokirim(
                                        konfirmasiWebviewEkspedisiResponse,
                                        urlPaymentMokirim
                                        );
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                onApiError(restResponse);
                            }
                        }
                        )
                );
    }

    @Override
    public void getConfirmationSplitBill(NotifikasiSplitBillRequest notifikasiSplitBillRequest, String urlPaymentSplitBill) {
        if (inquiryUrl == null || !isViewAttached()) {
            return;
        }

        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        getCompositeDisposable()
                .add(getApiSource().getData(inquiryUrl, notifikasiSplitBillRequest, seqNum)
                        .subscribeOn(getSchedulerProvider().io())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(new ApiObserver(getView(), seqNum) {

                                           @Override
                                           protected void onFailureHttp(String errorMessage) {
                                               getView().hideProgress();
                                               getView().onException(errorMessage);
                                           }

                                           @Override
                                           protected void onApiCallSuccess(RestResponse response) {
                                               getView().hideProgress();
                                               ConfirmationLifestyleResponse confirmationLifestyleResponse =
                                                       response.getData(ConfirmationLifestyleResponse.class);

                                               getView().onSuccessGetConfirmationSplitBill(
                                                       confirmationLifestyleResponse,
                                                       urlPaymentSplitBill
                                               );
                                           }

                                           @Override
                                           protected void onApiCallError(RestResponse restResponse) {
                                               getView().hideProgress();
                                               onApiError(restResponse);
                                           }
                                       }
                        )
                );
    }
}