package id.co.bri.brimo.presenters.britamarencanarevamp

import android.content.Context
import id.co.bri.brimo.R
import id.co.bri.brimo.contract.IPresenter.britamarencanarevamp.IDetailRencanaRevampPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.britamarencanarevamp.IDetailRencanaRevampView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimo.data.repository.category.CategoryPfmSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.config.AppConfig
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.AccountRequest
import id.co.bri.brimo.models.apimodel.request.StatusKartuRequest
import id.co.bri.brimo.models.apimodel.request.ubahdetailrencana.ChangeDetailPlanRequest
import id.co.bri.brimo.models.apimodel.response.CertificateResponse
import id.co.bri.brimo.models.apimodel.response.GeneralConfirmationResponse
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.dashboardrecana.EditTargetResponse
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.Schedulers
import java.util.concurrent.TimeUnit

class DetailRencanaRevampPresenter<V>(schedulerProvider: SchedulerProvider,
                                      compositeDisposable: CompositeDisposable,
                                      mBRImoPrefRepository: BRImoPrefSource, apiSource: ApiSource,
                                      categoryPfmSource: CategoryPfmSource,
                                      transaksiPfmSource: TransaksiPfmSource,
                                      anggaranPfmSource: AnggaranPfmSource) : MvpPresenter<V>(
    schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource
),
        IDetailRencanaRevampPresenter<V> where V : IMvpView, V : IDetailRencanaRevampView {

    private var mUrlEditTarget = ""
    private var mUrlCertif = ""
    private var mUrlCloseRencana = ""

    override fun setUrlEditTarget(url: String) {
        mUrlEditTarget = url
    }

    override fun getDataEditTarget(request : AccountRequest) {
        if (isViewAttached) {
            view.showProgress()
            val seqNum = brImoPrefRepository.seqNumber
            val listConnectableObservable = apiSource.getData(
                    mUrlEditTarget,
                    request,
                    seqNum
            ).subscribeOn(schedulerProvider.io())
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .observeOn(schedulerProvider.mainThread())
                    .replay()

            compositeDisposable.add(listConnectableObservable
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(type: String) {
                            view.hideProgress()
                            getView().onException(type)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            view.hideProgress()
                            val response = response.getData(
                                    EditTargetResponse::class.java
                            )

                            getView().onSuccessEditTarget(response)
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            view.hideProgress()
                            if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value, ignoreCase = true)) {
                                getView().onSessionEnd(restResponse.desc)
                            } else {
                                getView().onException(restResponse.desc)
                            }
                        }

                        override fun onComplete() {
                            super.onComplete()
                        }
                    })
            )
            listConnectableObservable.connect()
        }
    }

    override fun setUrlCertif(urlCertif: String) {
        mUrlCertif = urlCertif
    }

    override fun getCertif(accountNumber: String?) {
        if (mUrlCertif == null || !isViewAttached) {
            return
        }
        view.showProgress()
        val statusKartuRequest = StatusKartuRequest(accountNumber)
        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable.add(
                apiSource.getData(mUrlCertif, statusKartuRequest, seqNum) //function(param)
                        .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                        .subscribeOn(schedulerProvider.single())
                        .observeOn(schedulerProvider.mainThread())
                        .subscribeWith(object : ApiObserver(view, seqNum) {
                            override fun onFailureHttp(errorMessage: String) {
                                getView().hideProgress()
                                getView().onException(errorMessage)
                            }

                            override fun onApiCallSuccess(response: RestResponse) {
                                getView().hideProgress()
                                val certificateResponse = response.getData(CertificateResponse::class.java)
                                getView().onSuccessGetCertif(certificateResponse)
                            }

                            override fun onApiCallError(restResponse: RestResponse) {
                                getView().hideProgress()
                                if (restResponse.code == RestResponse.ResponseCodeEnum.RC_SESSION_END.value)
                                    getView().onSessionEnd(restResponse.desc)
                                else
                                    getView().onException(restResponse.desc)
                            }
                        })
        )
    }

    override fun setUrlClosePlan(url: String) {
        mUrlCloseRencana = url
    }

    override fun getClosePlan(context: Context, changeDetailPlanRequest: ChangeDetailPlanRequest) {
        if (mUrlCloseRencana.isEmpty() || !isViewAttached) return

        view.showProgress()
        val seqNum = brImoPrefRepository.seqNumber
        val disposable = apiSource.getData(
            mUrlCloseRencana,
            changeDetailPlanRequest,
            seqNum
        )
            .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
            .subscribeOn(schedulerProvider.io())
            .observeOn(schedulerProvider.mainThread())
            .subscribeWith(object : ApiObserver(view, seqNum) {
                override fun onFailureHttp(type: String) {
                    getView().apply {
                        hideProgress()
                        onException(type)
                    }
                }

                override fun onApiCallSuccess(response: RestResponse) {
                    getView().apply {
                        hideProgress()
                        val responseData = response.getData(
                            GeneralConfirmationResponse::class.java
                        )
                        responseData.let { getView().onSuccessCloseRencanaResponse(it) }
                    }
                }

                override fun onApiCallError(restResponse: RestResponse) {
                    restResponse.let { response ->
                        getView().apply {
                            hideProgress()
                            when (response.code) {
                                context.getString(R.string.txt_error_code_05) -> this.onSessionEnd(response.desc.orEmpty())
                                else -> this.onException(response.desc.orEmpty())
                            }
                        }
                    }
                }
            })
        compositeDisposable.add(disposable)
    }
}