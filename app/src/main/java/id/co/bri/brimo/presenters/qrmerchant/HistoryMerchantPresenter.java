package id.co.bri.brimo.presenters.qrmerchant;

import id.co.bri.brimo.contract.IPresenter.qrmerchant.IHistoryMerchantPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.qrmerchant.IHistoryMerchantView;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.config.AppConfig;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.apimodel.request.HistoryMerchantRequest;
import id.co.bri.brimo.models.apimodel.response.ListHistoryMerchantResponse;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.presenters.MvpPresenter;

import java.util.concurrent.TimeUnit;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.schedulers.Schedulers;

public class HistoryMerchantPresenter <V extends IMvpView & IHistoryMerchantView> extends MvpPresenter<V> implements IHistoryMerchantPresenter<V> {

    private static final String TAG = "HistoryMerchantPresenter";

    protected String url;

    public HistoryMerchantPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void getHistoriMerchant(String tanggal) {
        getView().showProgress();

        HistoryMerchantRequest historyMerchantRequest = new HistoryMerchantRequest(tanggal);
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        getCompositeDisposable().add(
                getApiSource().getData(url, historyMerchantRequest, seqNum)//function(param)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(Schedulers.single())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(new ApiObserver(getView(),seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                getView().hideProgress();
                                if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SUCCESS.getValue())){
                                    ListHistoryMerchantResponse listHistoryMerchantResponse = response.getData(ListHistoryMerchantResponse.class);
                                    getView().onSuccessGetHistory(listHistoryMerchantResponse);
                                }
                                else if(response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_01.getValue())) getView().onSuccess01();
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                onApiError(restResponse);
                            }
                        })
        );
    }

    @Override
    public void setUrl(String url) {
        this.url = url;
    }
}