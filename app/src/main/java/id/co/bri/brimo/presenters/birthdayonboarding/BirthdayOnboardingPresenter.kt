package id.co.bri.brimo.presenters.birthdayonboarding

import id.co.bri.brimo.contract.IPresenter.birthdayonboarding.IBirthdayOnboardingPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.birthdayonboarding.IBirthdayOnboardingView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimo.data.repository.category.CategoryPfmSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.config.AppConfig
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.BirthdayOnboardingRequest
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.birthdayonboarding.BirthdayOnboardingResponse
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.disposables.CompositeDisposable
import java.util.concurrent.TimeUnit

class BirthdayOnboardingPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBRImoPrefRepository: BRImoPrefSource,
    apiSource: ApiSource,
    categoryPfmSource: CategoryPfmSource,
    transaksiPfmSource: TransaksiPfmSource,
    anggaranPfmSource: AnggaranPfmSource
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    mBRImoPrefRepository,
    apiSource,
    transaksiPfmSource
), IBirthdayOnboardingPresenter<V> where V : IMvpView, V : IBirthdayOnboardingView {

    private lateinit var urlGetBirthdayOnboarding: String

    override fun setUrlGetBirthdayOnboarding(url: String) {
        this.urlGetBirthdayOnboarding = url
    }

    override fun getBirthdayOnboarding(request: BirthdayOnboardingRequest) {
        if (urlGetBirthdayOnboarding.isEmpty() && !isViewAttached) return

        view.showProgress()
        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable
            .add(
                apiSource.getData(urlGetBirthdayOnboarding, request, seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(schedulerProvider.io())
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            view.hideProgress()
                            getView().onException(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            view.hideProgress()
                            val responseBirthday =
                                response.getData(BirthdayOnboardingResponse::class.java)
                            getView().onSuccessGetBirthdayOnboarding(responseBirthday)
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            view.hideProgress()
                            getView().onException(restResponse.desc)
                        }
                    })
            )
    }
}