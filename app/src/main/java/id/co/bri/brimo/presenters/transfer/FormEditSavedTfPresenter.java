package id.co.bri.brimo.presenters.transfer;


import androidx.annotation.Nullable;

import id.co.bri.brimo.contract.IPresenter.transfer.IFormEditSavedTfPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.transfer.IFormEditSavedTfView;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.config.enumconfig.JourneyType;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.apimodel.request.UpdateSavedTitleTfRequest;
import id.co.bri.brimo.models.apimodel.request.dplkrevamp.UpdateSavedTitleDplkRequest;
import id.co.bri.brimo.models.apimodel.request.emas.UpdateSavedTitleEmasRequest;
import id.co.bri.brimo.models.apimodel.request.revamptransfer.SavedDataRequest;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.presenters.MvpPresenter;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.schedulers.Schedulers;

public class FormEditSavedTfPresenter<V extends IMvpView & IFormEditSavedTfView> extends MvpPresenter<V> implements IFormEditSavedTfPresenter<V> {

    public FormEditSavedTfPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    private UpdateSavedTitleTfRequest updateSavedTitleTfRequest;
    private UpdateSavedTitleDplkRequest updateSavedTitleDplkRequest;

    private UpdateSavedTitleEmasRequest updateSavedTitleEmasRequest;

    @Override
    public void setUpdateTitle(String url, int position, String saveTitle, String saveId, String paymentType, String tfMethod) {
        if (url == null || !isViewAttached()) return;

        updateSavedTitleTfRequest = new UpdateSavedTitleTfRequest(saveId, paymentType, tfMethod, saveTitle);
        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        getCompositeDisposable().add(getApiSource().getData(url, updateSavedTitleTfRequest, seqNum)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(new ApiObserver(getView(), seqNum) {

                    @Override
                    protected void onFailureHttp(String errorMessage) {
                        getView().onException(errorMessage);
                    }

                    @Override
                    protected void onApiCallSuccess(RestResponse response) {
                        //TO-DO onSuccess
                        getView().hideProgress();
                        getView().onSuccessGetUpdateTitle(position, saveTitle);

                    }

                    @Override
                    protected void onApiCallError(RestResponse restResponse) {
                        getView().hideProgress();
                        onApiError(restResponse);
                    }
                }));
    }

    @Override
    public void setUpdateTitleBRIVA(String url, int position, String saveTitle, String saveId, String subtitle, @Nullable String mJourneyType) {
        if (url == null || !isViewAttached()) return;

        if (mJourneyType == null) updateSavedTitleTfRequest = new UpdateSavedTitleTfRequest(saveId, saveTitle, subtitle);
        else {
            if (mJourneyType.equals(JourneyType.JOURNEY_TYPE_REVAMP_PASCABAYAR.getType())) {
                updateSavedTitleTfRequest = new UpdateSavedTitleTfRequest(saveId, saveTitle);
            }
        }
        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        getCompositeDisposable().add(getApiSource().getData(url, updateSavedTitleTfRequest, seqNum)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(new ApiObserver(getView(), seqNum) {

                    @Override
                    protected void onFailureHttp(String errorMessage) {
                        getView().onException(errorMessage);
                    }

                    @Override
                    protected void onApiCallSuccess(RestResponse response) {
                        //TO-DO onSuccess
                        getView().hideProgress();
                        getView().onSuccessGetUpdateTitle(position, saveTitle);

                    }

                    @Override
                    protected void onApiCallError(RestResponse restResponse) {
                        getView().hideProgress();
                        if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue())) {
                            getView().onSessionEnd(restResponse.getDesc());
                        } else if (restResponse.getCode().equals(RestResponse.ResponseCodeEnum.RC_99.getValue())) {
                            getView().onException99(restResponse.getDesc());
                        } else
                            getView().onException(restResponse.getDesc());

                    }
                }));
    }

    @Override
    public void setUpdateTitleDplk(String url, int position, String saveTitle, String saveId) {
        if (url == null || !isViewAttached()) return;

        updateSavedTitleDplkRequest = new UpdateSavedTitleDplkRequest(saveTitle, saveId);
        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        getCompositeDisposable().add(getApiSource().getData(url, updateSavedTitleDplkRequest, seqNum)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(new ApiObserver(getView(), seqNum) {

                    @Override
                    protected void onFailureHttp(String errorMessage) {
                        getView().onException(errorMessage);
                    }

                    @Override
                    protected void onApiCallSuccess(RestResponse response) {
                        //TO-DO onSuccess
                        getView().hideProgress();
                        getView().onSuccessGetUpdateTitle(position, saveTitle);

                    }

                    @Override
                    protected void onApiCallError(RestResponse restResponse) {
                        getView().hideProgress();
                        onApiError(restResponse);
                    }
                }));
    }

    @Override
    public void setUpdateTitleEmas(String url, int position, String saveTitle, String saveId) {
        if (url == null || !isViewAttached()) return;

        updateSavedTitleEmasRequest = new UpdateSavedTitleEmasRequest(saveTitle, saveId);
        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        getCompositeDisposable().add(getApiSource().getData(url, updateSavedTitleEmasRequest, seqNum)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(new ApiObserver(getView(), seqNum) {

                    @Override
                    protected void onFailureHttp(String errorMessage) {
                        getView().onException(errorMessage);
                    }

                    @Override
                    protected void onApiCallSuccess(RestResponse response) {
                        //TO-DO onSuccess
                        getView().hideProgress();
                        getView().onSuccessGetUpdateTitle(position, saveTitle);

                    }

                    @Override
                    protected void onApiCallError(RestResponse restResponse) {
                        getView().hideProgress();
                        onApiError(restResponse);
                    }
                }));
    }

    @Override
    public void saveSavedData(String urlSave, SavedDataRequest savedDataRequest) {
        if (urlSave == null || !isViewAttached()) return;

        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        getCompositeDisposable()
                .add(getApiSource().getData(urlSave, savedDataRequest, seqNum)
                        .subscribeOn(getSchedulerProvider().io())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(new ApiObserver(getView(), seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().hideProgress();
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                getView().hideProgress();
                                getView().onSuccessSavedData();
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                onApiError(restResponse);
                            }
                        })
        );
    }

}