package id.co.bri.brimo.presenters.travel;

import java.util.concurrent.TimeUnit;

import id.co.bri.brimo.contract.IPresenter.travel.IHistoryTravelPayedPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.travel.IHistoryTravelPayedView;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.config.AppConfig;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.apimodel.request.DetailTiketRequest;
import id.co.bri.brimo.models.apimodel.request.StatusRequest;
import id.co.bri.brimo.models.apimodel.response.DetailHistoryResponse;
import id.co.bri.brimo.models.apimodel.response.HistoryTravelResponse;
import id.co.bri.brimo.models.apimodel.response.ReceiptRevampInboxResponse;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.presenters.MvpPresenter;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.disposables.Disposable;
import io.reactivex.schedulers.Schedulers;

public class HistoryTravelPayedPresenter<V extends IMvpView & IHistoryTravelPayedView> extends MvpPresenter<V> implements IHistoryTravelPayedPresenter<V> {

        protected Object inquiryRequest = null;

        protected String url, urlHistory, urlTicketRevamp;

        public HistoryTravelPayedPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
            super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
        }

        @Override
        public void getDetailTicket(String bookingId, HistoryTravelResponse.Transaction transaction) {
            DetailTiketRequest detailTiketRequest = new DetailTiketRequest(bookingId);
            inquiryRequest = detailTiketRequest;
            String seqNum = getBRImoPrefRepository().getSeqNumber();

            getView().showProgress();
            getCompositeDisposable()
                    .add(getApiSource().getData(url, inquiryRequest, seqNum)
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .subscribeOn(Schedulers.io())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribeWith(new ApiObserver(getView(),seqNum) {

                                @Override
                                protected void onFailureHttp(String errorMessage) {
                                    getView().hideProgress();
                                    getView().onException(errorMessage);
                                    onLoad = false;
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    //TO-DO onSuccess
                                    getView().hideProgress();
                                    DetailHistoryResponse detailHistoryResponse = response.getData(DetailHistoryResponse.class);
                                    getView().onSuccessGetDetailTicket(detailHistoryResponse, transaction);
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    onLoad = false;
                                    getView().hideProgress();
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                        getView().onSessionEnd(restResponse.getDesc());
                                    else
                                        getView().onException(restResponse.getDesc());

                                }
                            }));
        }

    @Override
    public void getHistory() {
        if (urlHistory == null || !isViewAttached()) {
//            Log.d(TAG, "getDataInquiry: view atau inquiry url null");
            return;
        }

        getView().isHideSkeleton(false);
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        Disposable disposable = getApiSource().getDataTanpaRequest(urlHistory,seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                .subscribeOn(getSchedulerProvider().io())
                .observeOn(getSchedulerProvider().mainThread())
                .subscribeWith(new ApiObserver(getView(),seqNum) {


                    @Override
                    protected void onFailureHttp(String errorMessage) {
                        getView().isHideSkeleton(true);
                        getView().onException(errorMessage);
                    }

                    @Override
                    protected void onApiCallSuccess(RestResponse response) {
                        getView().isHideSkeleton(true);
                        HistoryTravelResponse historyTravelResponse = response.getData(HistoryTravelResponse.class);
                        getView().onSuccessGetHistory(historyTravelResponse);
                    }

                    @Override
                    protected void onApiCallError(RestResponse restResponse) {
                        getView().isHideSkeleton(true);
                        if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                            getView().onSessionEnd(restResponse.getDesc());
                        else
                            getView().onException(restResponse.getDesc());
                    }
                });
        getCompositeDisposable().add(disposable);
    }


    @Override
    public void setUrl(String url) {
            this.url = url;
        }

    @Override
    public void setUrlHistory(String urlHistory) {
        this.urlHistory = urlHistory;
    }

    @Override
    public void setUrlDetailRevamp(String urlHistory) {
        this.urlTicketRevamp = urlHistory;
    }

    @Override
    public void getDetailRevamp(String refnum) {
        if (isViewAttached()) {
            getView().showProgress();

            StatusRequest statusRequest = new StatusRequest(refnum);
            String seqNum = getBRImoPrefRepository().getSeqNumber();

            getCompositeDisposable().add(
                    getApiSource().getData(urlTicketRevamp, statusRequest, seqNum)//function(param)
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .subscribeOn(getSchedulerProvider().io())
                            .observeOn(getSchedulerProvider().mainThread())
                            .subscribeWith(new ApiObserver(getView(), seqNum) {

                                @Override
                                protected void onFailureHttp(String errorMessage) {
                                    getView().hideProgress();
                                    getView().onException(errorMessage);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    getView().hideProgress();
                                    ReceiptRevampInboxResponse receiptRevampInboxResponse = response.getData(ReceiptRevampInboxResponse.class);
                                    getView().onSuccessGetTicketRevamp(receiptRevampInboxResponse);
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().hideProgress();
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue())) {
                                        getView().onSessionEnd(restResponse.getDesc());
                                    } else
                                        getView().onException(restResponse.getDesc());
                                }
                            })
            );
        }
    }

}