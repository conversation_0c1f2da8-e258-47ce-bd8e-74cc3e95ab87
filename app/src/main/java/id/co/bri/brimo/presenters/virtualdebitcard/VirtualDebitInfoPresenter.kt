package id.co.bri.brimo.presenters.virtualdebitcard

import id.co.bri.brimo.contract.IPresenter.virtualdebitcard.IVirtualDebitInfoPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.virtualdebitcard.IVirtualDebitInfoView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.InquiryBrizziRequest
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.disposables.CompositeDisposable

class VirtualDebitInfoPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBRImoPrefRepository: BRImoPrefSource,
    apiSource: ApiSource,
    transaksiPfmSource: TransaksiPfmSource,
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    mBRImoPrefRepository,
    apiSource,
    transaksiPfmSource
), IVirtualDebitInfoPresenter<V> where V : IMvpView, V : IVirtualDebitInfoView {

    private lateinit var urlGenerateCVV: String
    private lateinit var urlEnableDisableTransactionVDC: String
    private lateinit var urlGetDetailVirtualCard: String
    private lateinit var urlUpdateLabelVDC: String

    private lateinit var requestDetailVDC: InquiryBrizziRequest

    override fun getDetailVDC(cardNumber: String) {
    }
}