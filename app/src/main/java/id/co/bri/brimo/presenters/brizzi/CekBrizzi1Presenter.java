package id.co.bri.brimo.presenters.brizzi;

import id.co.bri.brimo.R;
import id.co.bri.brimo.contract.IPresenter.brizzi.ICekBrizii1Presenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.brizzi.ICekBrizzi1View;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.apimodel.request.FastInquiryBrizziTopUpRequest;
import id.co.bri.brimo.models.apimodel.request.InquiryBrizziTopUpRequest;
import id.co.bri.brimo.models.apimodel.response.InquiryBrizziResponse;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.presenters.MvpPresenter;
import id.co.bri.brizzi.Brizzi;
import id.co.bri.brizzi.CardData;
import id.co.bri.brizzi.callbacks.BrizziCallback;
import id.co.bri.brizzi.exception.BrizziException;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.schedulers.Schedulers;

public class CekBrizzi1Presenter <V extends IMvpView & ICekBrizzi1View> extends MvpPresenter<V> implements ICekBrizii1Presenter<V> {
    private static final String TAG = "CekBrizzi1Presenter";
    private String inquiryUrlNonUser = null;
    private String inquiryUrl = null;
    protected Object inquiryRequest = null;



    public CekBrizzi1Presenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    /**
     *  Method yang digunakan untuk get Random Host UPDATE SALDO BRIZZI
     * @param inquiryBrizziRequest
     * @param isFormFast
     */
    @Override
    public void getRandomHost(InquiryBrizziTopUpRequest inquiryBrizziRequest, boolean isFormFast, Brizzi brizzi) {
        getView().hideProgress();
        getView().showProgress();
        if (isViewAttached()) {

            if(isFormFast){
                inquiryRequest = new FastInquiryBrizziTopUpRequest(getFastMenuRequest(), inquiryBrizziRequest.getCardNumber(), inquiryBrizziRequest.getRandomString());
            }else {
                inquiryRequest = inquiryBrizziRequest;
            }

            String seqNum = getBRImoPrefRepository().getSeqNumber();

            getCompositeDisposable().add(getApiSource().getData(inquiryUrl, inquiryRequest, seqNum)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(new ApiObserver(getView(),seqNum) {
                        @Override
                        protected void onFailureHttp(String errorMessage) {
                            getView().hideProgress();
                            getView().onException(errorMessage);
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            InquiryBrizziResponse inquiryBrizziResponse = response.getData(InquiryBrizziResponse.class);
                            commitCheckBalancePres(brizzi, inquiryBrizziResponse);
//                            getView().onSuccessGetRandomHost(inquiryBrizziResponse);
                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                getView().onSessionEnd(restResponse.getDesc());
                            else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.getValue()))
                                getView().onException93(restResponse.getDesc());
                            else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_01.getValue()))
                                getView().onException01(restResponse.getDesc());
                            else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_12.getValue()))
                                getView().onException12(restResponse.getDesc());
                            else
                                getView().onException(restResponse.getDesc());

                            getView().hideProgress();
                        }
                    }));
        }
    }

//    @Override
//    public void onSetCheckBalance(InquiryBrizziTopUpRequest inquiryBrizziRequest, boolean isFormFast) {
//        if (isViewAttached()) {
//            getView().showProgress();
//
//            if(isFormFast){
//                inquiryRequest = new FastInquiryBrizziTopUpRequest(getFastMenuRequest(), inquiryBrizziRequest.getCardNumber(), inquiryBrizziRequest.getRandomString());
//            }else {
//                inquiryRequest = inquiryBrizziRequest;
//            }
//
//
//            getCompositeDisposable().add(getApiSource().getData(inquiryUrlNonUser, inquiryRequest)
//                    .subscribeOn(Schedulers.io())
//                    .observeOn(AndroidSchedulers.mainThread())
//                    .subscribeWith(new ApiObserver() {
//                        @Override
//                        protected void onFailureHttp(String errorMessage) {
//                            getView().onException(errorMessage);
//                        }
//
//                        @Override
//                        protected void onApiCallSuccess(RestResponse response) {
//                            getView().hideProgress();
//
//                            InquiryBrizziResponse inquiryBrizziResponse = response.getData(InquiryBrizziResponse.class);
//
//                            getView().onSuccesRandomHost(inquiryBrizziResponse);
//                        }
//
//                        @Override
//                        protected void onApiCallError(RestResponse restResponse) {
//                            getView().hideProgress();
//                            if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
//                                getView().onSessionEnd(restResponse.getDesc());
//                            else if (restResponse.getCode().equalsIgnoreCase("93"))
//                                getView().onException93(restResponse.getDesc());
//                            else if (restResponse.getCode().equalsIgnoreCase("01"))
//                                getView().onException01(restResponse.getDesc());
//                            else
//                                getView().onException(restResponse.getDesc());
//                        }
//                    }));
//        }
//    }


    public void setInquiryUrl(String inquiryUrl) {
        this.inquiryUrl = inquiryUrl;
    }

//    @Override
//    public void setinquiryUrlNonUser(String urlInformasi) {
//        this.inquiryUrlNonUser = urlInformasi;
//    }

//    step 1 initCheckBalance
    @Override
    public void checkBalancePres(Brizzi brizzi, boolean isFromFastMenu){
        getView().hideProgress();
        getView().showProgress();
        if (isViewAttached()) {
            brizzi.initCheckbalance(new BrizziCallback() {
                @Override
                public void onSuccess(CardData cardData) {
//              step 2, host service check balance
                    getRandomHost(new InquiryBrizziTopUpRequest(cardData.getCardNumber(), cardData.getRandomSAM()), isFromFastMenu, brizzi);
                }

                @Override
                public void onFailure(BrizziException e) {
                    getView().hideProgress();
                    getView().showErrorMessage("Kartu Tidak Terbaca, Ulangi");
                }
            });

        }
    }

    @Override
    public void commitCheckBalancePres(Brizzi brizzi, InquiryBrizziResponse inquiryBrizziResponse){
//        step 3
        getView().hideProgress();
        getView().showProgress();
        brizzi.commitCheckBalance(inquiryBrizziResponse.getKey() + inquiryBrizziResponse.getRcHost(), new BrizziCallback() {
            @Override
            public void onSuccess(CardData cardData) {
                getView().continueInfoSaldo(brizzi, inquiryBrizziResponse);
                getView().hideProgress();
            }

            @Override
            public void onFailure(BrizziException e) {
                getView().hideProgress();
                getView().showErrorMessage(GeneralHelper.getString(R.string.brizzi_kartu_tidak_terbaca));
            }
        });
    }
}