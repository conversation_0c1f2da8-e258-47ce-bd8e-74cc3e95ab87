package id.co.bri.brimo.presenters.bukaValas;

import android.util.Log;

import id.co.bri.brimo.contract.IPresenter.bukaValas.IPilihRekeningBukaValasPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.base.IBaseFormView;
import id.co.bri.brimo.contract.IView.bukaValas.IPilihRekeningValasView;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.api.observer.ApiObserveFormKonversiValas;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.apimodel.response.onExceptionWH;
import id.co.bri.brimo.models.apimodel.response.PilihRekeningValasResponse;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.presenters.base.BaseFormPresenter;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.disposables.Disposable;
import io.reactivex.schedulers.Schedulers;

public class PilihRekeningValasPresenter<V extends IMvpView & IBaseFormView & IPilihRekeningValasView> extends BaseFormPresenter<V> implements IPilihRekeningBukaValasPresenter<V> {

    private static final String TAG = "PilihRekeningValasPresenter";
    protected String konfirmasiUrl;
    protected String inquiryUrl;
    protected String paymentUrl;
    protected String formUrl;


    public PilihRekeningValasPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, categoryPfmSource, transaksiPfmSource, anggaranPfmSource);
    }

    @Override
    public void getData() {
        if (formUrl == null || !isViewAttached()) {
            Log.d(TAG, "getData: form null");
            return;
        }

        if (!isViewAttached())
            return;

        String seqNum = getBRImoPrefRepository().getSeqNumber();

        Disposable disposable = getApiSource().getDataForm(formUrl,seqNum)//function(param)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(new ApiObserveFormKonversiValas(seqNum) {

                    @Override
                    protected void onFailureHttp(String errorMessage) {
                        getView().onException(errorMessage);
                    }

                    @Override
                    protected void onApiCallSuccess(RestResponse response) {

                        if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SUCCESS.getValue())) {
                            PilihRekeningValasResponse response1 = response.getData(PilihRekeningValasResponse.class);
                            getView().onSuccesGetData(response1);
                        }
                        else if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_02.getValue())){
                            onExceptionWH onExceptionWH = response.getData(onExceptionWH.class);
                            getView().onException60(onExceptionWH);
                        }
                        else if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_03.getValue())){
                            onExceptionWH onExceptionWH = response.getData(onExceptionWH.class);
                            getView().onException03(onExceptionWH);
                        }

                    }

                    @Override
                    protected void onApiCallError(RestResponse restResponse) {

                        if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue())){
                            getView().onSessionEnd(restResponse.getDesc());

                        }
                        else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_12.getValue())){
                            getView().onException12Form(restResponse.getDesc());

                        }
                        else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.getValue())){
                            getView().onException93(restResponse.getDesc());

                        }
                        else {
                            getView().onException(restResponse.getDesc());
                        }

//                        GeneralHelper.responseChuck(restResponse);
                    }
                });

        getCompositeDisposable().add(disposable);
    }

    @Override
    public void setFormUrl(String formUrl) {
        this.formUrl = formUrl;
    }

    @Override
    public void setKonfirmasiUrl(String konfirmasiUrl) {
        this.konfirmasiUrl = konfirmasiUrl;
    }

    @Override
    public void setPaymentUrl(String setPaymentUrl) {
        this.paymentUrl = setPaymentUrl;
    }

}