package id.co.bri.brimo.presenters

import android.os.Build
import android.util.Log
import id.co.bri.brimo.R
import id.co.bri.brimo.contract.IPresenter.ISplashScreenPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.ISplashScreenView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefRepository
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimo.data.repository.category.CategoryPfmSource
import id.co.bri.brimo.data.repository.fastmenu.FastMenuSource
import id.co.bri.brimo.data.repository.fastmenudefault.FastMenuDefaultSource
import id.co.bri.brimo.data.repository.menudashboard.MenuDashboardSource
import id.co.bri.brimo.data.repository.menukategori.MenuKategoriSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.config.AppConfig
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.config.MenuConfig
import id.co.bri.brimo.domain.helpers.DeviceIDHelper
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.BaseSubscribeTopicRequest
import id.co.bri.brimo.models.apimodel.request.SubscribeTopicRequest
import id.co.bri.brimo.models.apimodel.response.MaintenanceAlert
import id.co.bri.brimo.models.apimodel.response.OnboardingBrimoResponse
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.daomodel.Category
import id.co.bri.brimo.models.daomodel.DashboardMenu.MenuDashboard
import id.co.bri.brimo.models.daomodel.DashboardMenu.MenuKategori
import id.co.bri.brimo.models.daomodel.FastMenu
import id.co.bri.brimo.models.daomodel.FastMenuDefault
import io.reactivex.Completable
import io.reactivex.CompletableObserver
import io.reactivex.Observable
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.disposables.Disposable
import io.reactivex.observers.DisposableCompletableObserver
import io.reactivex.observers.DisposableObserver
import java.security.NoSuchAlgorithmException
import java.util.concurrent.TimeUnit


class SplashScreenPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBRImoPrefRepository: BRImoPrefSource,
    apiSource: ApiSource,
    private val mCategoryPfmSource: CategoryPfmSource,
    transaksiPfmSource: TransaksiPfmSource,
    anggaranPfmSource: AnggaranPfmSource,
    private val fastMenuSource: FastMenuSource,
    private val fastMenuDefaultSource: FastMenuDefaultSource,
    private val menuDashboardSource: MenuDashboardSource,
    private val menuKategoriSource: MenuKategoriSource
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    mBRImoPrefRepository,
    apiSource,
    transaksiPfmSource
), ISplashScreenPresenter<V> where V : IMvpView, V : ISplashScreenView {

    private var isLoad = false
    private var isNfcAvailable = false
    private val menuTopString = mutableListOf<String>()
    private val menuBottomString = mutableListOf<String>()
    private var urlOnboard: String = ""


    override fun loadDataCategory() {
        compositeDisposable.add(mCategoryPfmSource.getCategoryTransaksi()
            .subscribeOn(schedulerProvider.single())
            .observeOn(schedulerProvider.mainThread())
            .subscribe({ categories: List<Category?>? ->
                if (categories != null)
                    checkDevice()
            }) { _: Throwable? -> checkDevice() })
    }

    override fun loadDataIncome() {
        compositeDisposable.add(mCategoryPfmSource.getCategoryIncome()
            .subscribeOn(schedulerProvider.single())
            .observeOn(schedulerProvider.mainThread())
            .subscribe({ }) { })
    }

    override fun loadDataFastMenu() {
        compositeDisposable.add(
            fastMenuSource.getFastMenu()
                .subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribe({ fastMenus: List<FastMenu>? ->
                    if (fastMenus != null) {
                        updateFastMenuDefaultUpgradeVersion(
                            compareInitialDatas(
                                fastMenus,
                                MenuConfig.fetchFastMenuWithNfcMenu(isNfcAvailable)
                            ), fastMenus
                        )
                    }
                    //load data fast menu
                    loadDataFastMenuDefault()
                    loadDataIncome()
                    loadDataCategory()
                }) { })
    }

    override fun loadDataFastMenuDefault() {
        compositeDisposable.add(
            fastMenuDefaultSource.getFastMenuDefault()
                .subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribe({  }) { })
    }

    override fun checkDevice() {
        if (isLoad) return
        isLoad = true
        if (brImoPrefRepository.isContains(Constant.USER_TYPE))
            brImoPrefRepository.saveUserExist(true)
        if (brImoPrefRepository.getDeviceId2().isEmpty())
            view.generateIdPersistent()
        if (!brImoPrefRepository.isContains(Constant.DEVICE_ID)
            && brImoPrefRepository.getTokenKey().isEmpty()
        ) {
            generateDeviceId()
        } else {
            if (brImoPrefRepository.getUserType() == Constant.IB_TYPE) {
                view.onLoadIbType()
            } else {
                view.onLoadAsk()
            }
        }

        //load Device ID
        view.onLoadDeviceId(brImoPrefRepository.getDeviceId())
    }

    override fun cekSavedFastMenu(menuAtas: List<FastMenu>, menuBawah: List<FastMenuDefault>) {
        try {
            if (menuAtas.isNotEmpty() && menuBawah.isNotEmpty()) {
                for (i in menuBawah.indices) {
                    try {
                        menuBottomString.add(menuBawah[i].kode)
                        for (j in menuAtas.indices) {
                            menuTopString.add(menuAtas[j].kode)
                        }
                    } catch (e: Exception) {
                        if (!GeneralHelper.isProd()) {
                            Log.e(TAG, "cekSavedFastMenu: ", e)
                        }
                    }
                }
                for (k in menuTopString.indices) {
                    try {
                        menuTopString.removeAll(menuBottomString)
                        getDeletedSavedMenu(menuTopString[k])
                    } catch (e: Exception) {
                        if (!GeneralHelper.isProd()) Log.e(TAG, "cekSavedFastMenu: ", e)
                    }
                }
            }
        } catch (e: Exception) {
            if (!GeneralHelper.isProd()) Log.e(TAG, "cekSavedFastMenu: ", e)
        }
    }

    /**
     * Variable ini digunakan untuk mengecek apakah versi aplikasi berubah
     *
     * @return true / false
     */
    private val isUpdateVersionApps: Boolean
        get() {
            val versionId = GeneralHelper.getLastAppVersionCode()
            return if (brImoPrefRepository.isContains(BRImoPrefRepository.VERSION_APP)) {
                val savedVersion = brImoPrefRepository.getVersionApp()
                brImoPrefRepository.saveVersionApp(versionId)
                versionId != savedVersion
            } else {
                brImoPrefRepository.saveVersionApp(versionId)
                true
            }
        }


    private fun deleteMenuKategoriAndDashboard(){
        val deleteMenuKategori = menuKategoriSource.deleteAllMenuKategori()
        val deleteMenuDashboard = menuDashboardSource.deleteAllMenuDashboard()
        val list = mutableListOf(deleteMenuKategori, deleteMenuDashboard)
        compositeDisposable.add(
            Observable.fromIterable(list)
                .flatMap { deleteObservable(it) }
                .subscribeOn(schedulerProvider.newThread())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : DisposableObserver<Completable>() {
                    override fun onNext(t: Completable) {
                        //do nothing
                    }

                    override fun onError(e: Throwable) {
                        //do nothing
                    }

                    override fun onComplete() {
                        initiateMenuKategori(MenuConfig.getDefaultMenuKategori(), isNfcAvailable)
                    }

                })

        )
    }



    private fun deleteObservable(task: Completable): Observable<Completable> {
        return task
            .doOnComplete {
                //do nothing
            }
            .subscribeOn(schedulerProvider.newThread())
            .observeOn(schedulerProvider.mainThread())
            .toObservable()
    }

    override fun getDeleteFastMenuDefault(fastMenuDefaults: List<FastMenuDefault>) {
        fastMenuDefaultSource.deleteAll()
            .subscribeOn(schedulerProvider.io())
            .observeOn(schedulerProvider.mainThread())
            .subscribe(object : CompletableObserver {
                override fun onSubscribe(d: Disposable) { /*do nothing*/
                }

                override fun onComplete() {
                    getUpdateFastMenuDefault(fastMenuDefaults)
                }

                override fun onError(e: Throwable) { /*do nothing*/
                }
            })
    }

    override fun deleteFastMenuUpgradeVersion(fastMenus: List<FastMenu>) {
        fastMenuSource.deleteAll()
            .subscribeOn(schedulerProvider.io())
            .observeOn(schedulerProvider.mainThread())
            .subscribe(object : CompletableObserver {
                override fun onSubscribe(d: Disposable) { /*do nothing*/
                }

                override fun onComplete() { /*do nothing*/
                }

                override fun onError(e: Throwable) { /*do nothing*/
                }
            })
    }

    override fun getDeleteFastMenu(fastMenu: List<FastMenu>) {
        fastMenuSource.deleteAll()
            .subscribeOn(schedulerProvider.io())
            .observeOn(schedulerProvider.mainThread())
            .subscribe(object : CompletableObserver {
                override fun onSubscribe(d: Disposable) { /*do nothing*/
                }

                override fun onComplete() {
                    getUpdateFastMenu(fastMenu)
                }

                override fun onError(e: Throwable) { /*do nothing*/
                }
            })
    }

    override fun getUpdateFastMenu(fastMenus: List<FastMenu>) {
        fastMenuSource.saveFastMenu(fastMenus)
            .subscribeOn(schedulerProvider.io())
            .observeOn(schedulerProvider.mainThread())
            .subscribe(object : CompletableObserver {
                override fun onSubscribe(d: Disposable) {
                    // do nothing
                }

                override fun onComplete() {
                    // do nothing
                }

                override fun onError(e: Throwable) {
                    // do nothing
                }
            })
    }

    override fun getDeletedSavedMenu(id: String) {
        fastMenuSource.deleteById(id)
            .subscribeOn(schedulerProvider.io())
            .observeOn(schedulerProvider.mainThread())
            .subscribe(object : CompletableObserver {
                override fun onSubscribe(d: Disposable) {
                    // do nothing
                }

                override fun onComplete() {
                    //  do nothing
                }

                override fun onError(e: Throwable) {
                    // do nothing
                }
            })
    }

    override fun updateTokenFirebase() {
        val request = BaseSubscribeTopicRequest(
            getBRImoPrefRepository().getUsername(),
            getBRImoPrefRepository().getTokenKey(),
            getBRImoPrefRepository().getFirebaseToken()
        )
        val seqNum = getBRImoPrefRepository().getSeqNumber()
        compositeDisposable.add(
            getApiSource().getUpdateTokenFirebase(request, seqNum)
                .subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(getView(), seqNum) {
                    override fun onFailureHttp(type: String) {}
                    override fun onApiCallSuccess(response: RestResponse) {}
                    override fun onApiCallError(restResponse: RestResponse) {}
                })
        )
    }

    override fun getOnboardingBrimo() {
        val seqNum = getBRImoPrefRepository().getSeqNumber()
        compositeDisposable.add(
            getApiSource().getData(urlOnboard, "", seqNum)
                .subscribeOn(schedulerProvider.io())
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(getView(), seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView().onDisconnet()
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        val responResend = response.getData(
                            OnboardingBrimoResponse::class.java
                        )
                        val isFreshInstall = true
                        updateFreshInstallFlag(isFreshInstall = true)
                        brImoPrefRepository.saveStatusUpdateBio(true)
                        getView().onSuccessOnboardingBrimo(responResend)
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        if (restResponse.code.equals(Constant.RE_ALERT_MAINTENANCE)) {
                            val alertResponse = restResponse.getData(
                                MaintenanceAlert::class.java
                            )
                            getView().onMaintenanceAlert(alertResponse)
                        } else {
                            getView().onDisconnet()
                        }
                    }
                })
        )
    }

    override fun setUrlOnboard(urlOnboard: String) {
        this.urlOnboard = urlOnboard
    }

    override fun saveIdPersistent(id: String) {
        getBRImoPrefRepository().saveDeviceId2(id)
    }

    override fun updateFreshInstallFlag(isFreshInstall: Boolean) {
        getBRImoPrefRepository().saveFreshInstallFlag(isFreshInstall)
    }

    override fun checkAvailabilityNfc(isNfcAvailable: Boolean) {
        this.isNfcAvailable = isNfcAvailable
    }

    override fun getUpdateFastMenuDefault(fastMenuDefaults: List<FastMenuDefault>) {
        fastMenuDefaultSource.saveFastMenu(fastMenuDefaults)
            .subscribeOn(schedulerProvider.io())
            .observeOn(schedulerProvider.mainThread())
            .subscribe(object : CompletableObserver {
                override fun onSubscribe(d: Disposable) {}
                override fun onComplete() {
                    loadDataFastMenu()
                }

                override fun onError(e: Throwable) {}
            })
    }

    override fun updateFastMenuDefaultUpgradeVersion(
        fastMenuDefaults: List<FastMenuDefault>,
        fastMenus: List<FastMenu>
    ) {
        fastMenuDefaultSource.saveFastMenu(fastMenuDefaults)
            .subscribeOn(schedulerProvider.io())
            .observeOn(schedulerProvider.mainThread())
            .subscribe(object : CompletableObserver {
                override fun onSubscribe(d: Disposable) {
                    // do nothing
                }

                override fun onComplete() {
                    deleteFastMenuUpgradeVersion(fastMenus)
                    view.onResultSuccess()
                }

                override fun onError(e: Throwable) {
                    // do nothing
                }
            })
    }

    /**
     * Method untuk mereset data cek point registrasi dan digital saving
     */
    override fun resetCheckPoint() {
        getBRImoPrefRepository().deleteCheckPoint()
    }

    override fun subscribeTopicAll(newToken: String) {
        if (getBRImoPrefRepository().getFirebaseToken().equals(newToken))
            return;

        if (isUpdateVersionApps ||
            !getBRImoPrefRepository().getUpdateTokenFirebase()
        ) {
            val subscribeTopicRequest = SubscribeTopicRequest(
                getBRImoPrefRepository().getUsername(),
                getBRImoPrefRepository().getTokenKey(),
                newToken,
                Constant.TOPIC_ALL
            )

            val seqNum = getBRImoPrefRepository().getSeqNumber()

            //subscribe topic ke WS
            compositeDisposable.add(
                getApiSource().getSubscribeTopicFirebase(subscribeTopicRequest, seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(schedulerProvider.io())
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserver(getView(), seqNum) {
                        override fun onFailureHttp(type: String) {}
                        override fun onApiCallSuccess(response: RestResponse) {
                            getBRImoPrefRepository().saveFirebaseToken(newToken)
                        }

                        override fun onApiCallError(restResponse: RestResponse) {}
                    })
            )
        }
    }

    override fun generateDeviceId() {
        try {
            getBRImoPrefRepository().saveDeviceId(DeviceIDHelper.setDeviceID())
            getBRImoPrefRepository().deleteSeqNumber()
            getView().onLoadAsk()
        } catch (e: NoSuchAlgorithmException) {
            if (!GeneralHelper.isProd()) Log.e(TAG, ": ", e)
        }
    }

    override fun start() {
        super.start()
        //open popup notif
        setDisablePopup(false)

        //apakah pernah update DB revamp (sementara di buka untuk hapus jadi merchant)
        if (!getBRImoPrefRepository().isSavedDbRevamp()) {
            initiateMenuKategori(MenuConfig.getDefaultMenuKategori(), isNfcAvailable)
        }

        // initiate data Income dan Category transaksi
        if (!isLoad) {
            //check version
            if (isUpdateVersionApps) {
                //cek update data menu kategori
//                initiateMenuKategori(MenuConfig.getDefaultMenuKategori(), isNfcAvailable)
//                initiateMenuDashboard(MenuConfig.fetchNewMenu(isNfcAvailable))

                deleteMenuKategoriAndDashboard()

                //update variabel untuk bottom biometric update
                brImoPrefRepository.saveStatusUpdateBio(true)
            } else {
                //load data fast menu
                loadDataFastMenuDefault()
                loadDataIncome()
                loadDataCategory()
            }
        } else {
            // do nothing
        }
    }

    private fun initiateMenuKategori(menuKategoriList: List<MenuKategori>, isNfcAvailable: Boolean) {
        compositeDisposable.add(
            menuKategoriSource.insertMenuKategori(menuKategoriList)
                .subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : DisposableCompletableObserver() {
                    override fun onComplete() {
                        val stringBio = String.format(
                            GeneralHelper.getString(R.string.login_fingerprint),
                            getBRImoPrefRepository().getBiometricType()
                        )
                        initiateMenuDashboard(
                            MenuConfig.onCheckBiometic(
                                MenuConfig.fetchAllMenuWithNfcMenu(isNfcAvailable),
                                stringBio
                            )
                        )
                    }

                    override fun onError(e: Throwable) {}
                })
        )
    }

    private fun initiateMenuDashboard(menuDashboardList: List<MenuDashboard?>) {
        //apakah pernah disimpan
        compositeDisposable.add(
            menuDashboardSource.insertMenuDashboard(menuDashboardList)
                .subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : DisposableCompletableObserver() {
                    override fun onComplete() {
                        getBRImoPrefRepository().saveDBMenuRevamp(true)
                        //load and cek fast menu
//                        getDeleteFastMenuDefault(MenuConfig.fetchFastMenuDefault())
                        getDeleteFastMenuDefault(MenuConfig.fetchFastMenuDefaultNewSkin())
                    }

                    override fun onError(e: Throwable) {}
                })
        )
    }

    private fun compareInitialDatas(
        mPreviousFastMenuList: List<FastMenu>,
        mConfigFastMenuList: List<FastMenuDefault>
    ): List<FastMenuDefault> {
        val previousFastMenuList: List<FastMenu> = java.util.ArrayList(mPreviousFastMenuList)
        val configFastMenuList: List<FastMenuDefault> = java.util.ArrayList(mConfigFastMenuList)
        val listResult: MutableList<FastMenuDefault> = java.util.ArrayList()

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            for (i in previousFastMenuList.indices) {
                for (j in configFastMenuList.indices) {
                    if (previousFastMenuList[i].kode.equals(
                            configFastMenuList[j].kode,
                            ignoreCase = true
                        )
                    ) {
                        listResult.add(
                            FastMenuDefault(
                                previousFastMenuList[i].id,
                                previousFastMenuList[i].kode,
                                configFastMenuList[j].menuName,
                                configFastMenuList[j].gambarMenu,
                                previousFastMenuList[i].menu,
                                previousFastMenuList[i].tag,
                                previousFastMenuList[i].isFlagNew,
                                i,
                                true,
                                true
                            )
                        )
                        break
                    }
                }
            }
        }

        return listResult
    }

    companion object {
        private const val TAG = "SplashScreenPresenter"
    }

    override fun deleteMenuDashboard(menuId: Int) {
        compositeDisposable.add(
            menuDashboardSource.deleteMenuDashboard(menuId)
                .subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : DisposableCompletableObserver() {
                    override fun onComplete() {
                        // do nothing
                    }

                    override fun onError(e: Throwable) {
                        // do nothing
                    }
                })
        )
    }
}
