package id.co.bri.brimo.presenters.dplkrevamp

import id.co.bri.brimo.contract.IPresenter.dplkrevamp.ISettingAutoPaymentPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.dplkrevamp.ISettingAutoPaymentView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimo.data.repository.category.CategoryPfmSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.config.AppConfig

import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.dplkrevamp.EditAutoPaymentRequest
import id.co.bri.brimo.models.apimodel.request.dplkrevamp.InquiryAutoPaymentRequest
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.dplkrevamp.InquirySettingAutoPaymentResponse
import id.co.bri.brimo.models.apimodel.response.dplkrevamp.SubmitAutoPaymentResponse
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.disposables.CompositeDisposable
import java.util.concurrent.TimeUnit

class SettingAutoPaymentPresenter<V>(schedulerProvider: SchedulerProvider,
                                     compositeDisposable: CompositeDisposable,
                                     mBRImoPrefRepository: BRImoPrefSource, apiSource: ApiSource,
                                     categoryPfmSource: CategoryPfmSource,
                                     transaksiPfmSource: TransaksiPfmSource,
                                     anggaranPfmSource: AnggaranPfmSource) : MvpPresenter<V>(
    schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource),
    ISettingAutoPaymentPresenter<V> where V : IMvpView, V : ISettingAutoPaymentView  {

    var mUrlEditAutoPayment : String? = null
    var mUrlInquiryAutoPayment: String? = null
    override fun setUrlInquiryAutoPayment(urlAddAutoPayment: String) {
        mUrlInquiryAutoPayment = urlAddAutoPayment
    }

    override fun setUrlEditAutoPayment(urlEditAutoPayment: String) {
        mUrlEditAutoPayment = urlEditAutoPayment
    }

    override fun editAutoPayment(request: EditAutoPaymentRequest) {
        if (mUrlEditAutoPayment == null || !isViewAttached) {
            return
        }

        view.showProgress()
        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable
            .add(
                apiSource.getData(mUrlEditAutoPayment,request, seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(schedulerProvider.io())
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView().hideProgress()
                            getView().onException(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            //TO-DO onSuccess
                            getView().hideProgress()
                            val data = response.getData(
                                SubmitAutoPaymentResponse::class.java
                            )
                            if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SUCCESS.value, ignoreCase = true)) {
                                getView().onSuccessInquiryEditAutoPayment(data)
                            }

                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView().hideProgress()
                            when(restResponse.code){
                                RestResponse.ResponseCodeEnum.RC_SESSION_END.value -> getView().onSessionEnd(restResponse.desc)
                                RestResponse.ResponseCodeEnum.RC_12.value -> getView().onException12(restResponse.desc)
                                RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.value -> getView().onExceptionTrxExpired(restResponse.desc)
                                else -> getView().onException(restResponse.desc)
                            }
                        }
                    })
            )
    }

    override fun getInquiryAutoPayment(request: InquiryAutoPaymentRequest) {
        if (mUrlInquiryAutoPayment == null || !isViewAttached) {
            return
        }

        view.showProgress()
        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable
            .add(
                apiSource.getData(mUrlInquiryAutoPayment, request, seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(schedulerProvider.io())
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView().hideProgress()
                            getView().onException(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            getView().hideProgress()
                            val data = response.getData(
                                InquirySettingAutoPaymentResponse::class.java
                            )
                            if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SUCCESS.value, ignoreCase = true)) {
                                getView().onSuccessInquiryAutoPayment(data)
                            }

                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView().hideProgress()
                            when (restResponse.code) {
                                RestResponse.ResponseCodeEnum.RC_SESSION_END.value -> getView().onSessionEnd(restResponse.desc)
                                RestResponse.ResponseCodeEnum.RC_12.value -> getView().onException12(restResponse.desc)
                                RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.value -> getView().onExceptionTrxExpired(restResponse.desc)
                                else -> getView().onException(restResponse.desc)
                            }
                        }
                    })
            )
    }

    fun getDefaultSaldo() {
        var saldo = 0.0
        val saldoText = brImoPrefRepository.saldoRekeningUtama
        if (saldoText != null && saldoText.isNotEmpty()) { // Add null check and isEmpty() check
            saldo = java.lang.Double.valueOf(saldoText.trim()) // Add trim() inside the null check
        }
        val defaultAcc = brImoPrefRepository.accountDefault
        val saldoString = brImoPrefRepository.saldoRekeningUtamaString
        val saldoHold = brImoPrefRepository.saldoHold
        view.setDefaultSaldo(saldo, saldoString, defaultAcc,saldoHold)
    }

    override fun start() {
        super.start()
        getDefaultSaldo()
    }
}