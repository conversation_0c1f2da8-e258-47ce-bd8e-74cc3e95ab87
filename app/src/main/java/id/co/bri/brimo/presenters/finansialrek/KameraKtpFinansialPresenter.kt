package id.co.bri.brimo.presenters.finansialrek

import android.util.Log
import com.google.gson.Gson
import id.co.bri.brimo.contract.IPresenter.finansialrek.IKameraKtpFinansialPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.finansialrek.IKameraKtpFinansialView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimo.data.repository.category.CategoryPfmSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.config.AppConfig
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.DeviceIdRequest
import id.co.bri.brimo.models.apimodel.response.OcrKtpErrorResponse
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.Schedulers
import java.util.concurrent.TimeUnit

class KameraKtpFinansialPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBRImoPrefRepository: BRImoPrefSource,
    apiSource: ApiSource,
    categoryPfmSource: CategoryPfmSource,
    transaksiPfmSource: TransaksiPfmSource,
    anggaranPfmSource: AnggaranPfmSource
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    mBRImoPrefRepository,
    apiSource,
    transaksiPfmSource
), IKameraKtpFinansialPresenter<V> where V : IMvpView, V : IKameraKtpFinansialView {

    private var urlKtp: String = ""

    override fun getDeviceId() {
        view.onDeviceId(brImoPrefRepository.deviceId)
    }

    override fun setUrlCheckKtp(url: String) {
      urlKtp = url
    }

    override fun sendCheckKtp() {
        if (urlKtp.isEmpty() || !isViewAttached) return
        val seqNum = brImoPrefRepository.seqNumber
        val request = DeviceIdRequest(brImoPrefRepository.deviceId)

        compositeDisposable.add(
            apiSource.getData(urlKtp, request, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView().hideProgress()
                        getView().onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView().hideProgress()
                        getView().onSuccessCheckKtp(Gson().toJson(response.data))
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView().hideProgress()
                        if (restResponse.code == Constant.RE_INVALID_KTP){
                            val ocrErrorResponse = restResponse.getData(OcrKtpErrorResponse::class.java)
                            getView().onExceptionInvalidKtp(ocrErrorResponse)
                        }
                        else getView().onExceptionRevamp(restResponse.desc)
                    }
                })
        )
    }


}