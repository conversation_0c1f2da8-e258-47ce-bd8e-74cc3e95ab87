package id.co.bri.brimo.presenters.virtualdebitcard

import id.co.bri.brimo.contract.IPresenter.virtualdebitcard.IInputLabelVDCPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.virtualdebitcard.IInputLabelVDCView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimo.data.repository.category.CategoryPfmSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.config.AppConfig

import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.virtualdebitcard.ConfirmationCreateVDCRequest
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.virtualdebitcard.ConfirmationCreateVDCResponse
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.disposables.CompositeDisposable
import java.util.concurrent.TimeUnit

class InputLabelVDCPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBRImoPrefRepository: BRImoPrefSource,
    apiSource: ApiSource,
    categoryPfmSource: CategoryPfmSource,
    transaksiPfmSource: TransaksiPfmSource,
    anggaranPfmSource: AnggaranPfmSource
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    mBRImoPrefRepository,
    apiSource,
    transaksiPfmSource
), IInputLabelVDCPresenter<V> where V : IMvpView, V : IInputLabelVDCView {

    private lateinit var urlConfirmationVDC: String

    private lateinit var request: ConfirmationCreateVDCRequest
    override fun setUrlConfirmationCreateVDC(url: String) {
        this.urlConfirmationVDC = url
    }

    override fun confirmationCreateVDC(
        labelCard: String,
        accountNumber: String,
        idCardType: String
    ) {
        if (urlConfirmationVDC.isEmpty() && !isViewAttached) return

        request = ConfirmationCreateVDCRequest(
            labelCard = labelCard,
            accountNumber = accountNumber,
            idCardType = idCardType
        )

        view.showProgress()

        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable
            .add(
                apiSource.getData(urlConfirmationVDC, request, seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(schedulerProvider.io())
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView().hideProgress()
                            getView().onException(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            getView().hideProgress()
                            val responseConfirmationVDC =
                                response.getData(ConfirmationCreateVDCResponse::class.java)
                            getView().onSuccessGetConfirmation(responseConfirmationVDC)
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView().hideProgress()
                            if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value))
                                getView().onSessionEnd(restResponse.desc)
                            else getView().onException(restResponse.desc)
                        }
                    })
            )
    }

}