package id.co.bri.brimo.presenters.cc_sof

import id.co.bri.brimo.contract.IPresenter.cc_sof.IDetailCcPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.cc_sof.IDetailCcView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimo.data.repository.category.CategoryPfmSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.config.AppConfig
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.CheckBlockRequest
import id.co.bri.brimo.models.apimodel.request.InquiryKreditRequest
import id.co.bri.brimo.models.apimodel.response.CheckBlockResponse
import id.co.bri.brimo.models.apimodel.response.GeneralInquiryResponse
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.disposables.CompositeDisposable
import java.util.concurrent.TimeUnit

class DetailCcPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBRImoPrefRepository: BRImoPrefSource,
    apiSource: ApiSource,
    categoryPfmSource: CategoryPfmSource,
    transaksiPfmSource: TransaksiPfmSource,
    anggaranPfmSource: AnggaranPfmSource
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    mBRImoPrefRepository,
    apiSource,
    transaksiPfmSource
), IDetailCcPresenter<V> where V : IMvpView, V : IDetailCcView {

    private var urlCheckBlock = ""
    private var urlInquiry = ""

    override fun setUrlCheckBlock(url: String) {
        urlCheckBlock = url
    }

    override fun setInquiryUrl(url: String) {
        urlInquiry = url
    }

    override fun getDataCheckBlock(request: CheckBlockRequest) {
        if (urlCheckBlock.isEmpty() || !isViewAttached) return

        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable.add(
            apiSource.getData(urlCheckBlock, request, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView().onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        val checkBlockResponse = response.getData(
                            CheckBlockResponse::class.java
                        )
                        getView().onSuccessCheckBlock(checkBlockResponse)
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        onApiError(restResponse)
                    }
                })
        )
    }

    override fun getDataInquiry(request: InquiryKreditRequest) {
        if (urlInquiry.isEmpty() || !isViewAttached) return

        val seqNum = brImoPrefRepository.seqNumber
        view.showProgress()

        compositeDisposable.add(
            apiSource.getData(urlInquiry, request, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView().hideProgress()
                        getView().onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView().hideProgress()
                        val responseKredit = response.getData(
                            GeneralInquiryResponse::class.java
                        )
                        if (responseKredit.openPayment == true) {
                            if (responseKredit.isBilling == true)
                                getView().gotoInquiryClose(responseKredit)
                            else getView().gotoInquiryOpen(responseKredit)
                        } else getView().gotoInquiryClose(responseKredit)
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        onApiError(restResponse)
                    }
                })
        )
    }
}