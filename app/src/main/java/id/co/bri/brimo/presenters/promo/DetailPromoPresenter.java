package id.co.bri.brimo.presenters.promo;

import androidx.annotation.NonNull;

import java.util.concurrent.TimeUnit;

import id.co.bri.brimo.contract.IPresenter.promo.IDetailPromoPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.promo.IDetailPromoView;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.config.AppConfig;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.apimodel.request.DetailPromoRequest;
import id.co.bri.brimo.models.apimodel.request.NotifReadFastMenuRequest;
import id.co.bri.brimo.models.apimodel.request.PartnerIdRequest;
import id.co.bri.brimo.models.apimodel.response.CityFormResponse;
import id.co.bri.brimo.models.apimodel.response.GeneralWebviewResponse;
import id.co.bri.brimo.models.apimodel.response.PromoResponse;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.models.apimodel.response.UrlWebViewResponse;
import id.co.bri.brimo.models.apimodel.response.lifestyle.EODLifestyleResponse;
import id.co.bri.brimo.presenters.MvpPresenter;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.schedulers.Schedulers;

public class DetailPromoPresenter<V extends IMvpView & IDetailPromoView> extends MvpPresenter<V> implements IDetailPromoPresenter<V> {

    private String url;
    private String urlRead;
    DetailPromoRequest detailPromoRequest;
    private String mUrlFormBus = "";
    private String mUrlKai = "";
    private String mUrlWebviewTugu = "";

    public DetailPromoPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void setDetailPromoUrl(String url) {
        this.url = url;
    }

    @Override
    public void getIdPromoNotifikasi(String idPromo) {
        if (url == null || !isViewAttached()) {
            return;
        }

        detailPromoRequest = new DetailPromoRequest(idPromo);
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        getCompositeDisposable()
                .add(getApiSource().getData(url, detailPromoRequest, seqNum)
                        .subscribeOn(Schedulers.io())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(new ApiObserver(getView(), seqNum) {


                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().hideProgress();
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                //TO-DO onSuccess
                                getView().hideProgress();
                                PromoResponse responsebriva = response.getData(PromoResponse.class);
                                getView().onSuccessGetPromo(responsebriva);
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                    getView().onSessionEnd(restResponse.getDesc());
                                else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_99.getValue()))
                                    getView().onException99(restResponse.getDesc());
                                else
                                    getView().onException(restResponse.getDesc());

                            }
                        }));


    }

    @Override
    public void setUrlReadNotif(String urlRead) {
        this.urlRead = urlRead;
    }

    @Override
    public void getReadNotifFastMenu(String blastId) {
        NotifReadFastMenuRequest notifRequest = new NotifReadFastMenuRequest("android",
                blastId,
                getBRImoPrefRepository().getTokenKey(),
                getBRImoPrefRepository().getUsername());

        String seqNum = getBRImoPrefRepository().getSeqNumber();
        getCompositeDisposable()
                .add(getApiSource().getData(urlRead, notifRequest, seqNum)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(getSchedulerProvider().single())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(new ApiObserver(getView(), seqNum) {
                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {

                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                    getView().onSessionEnd(restResponse.getDesc());
                                else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_99.getValue()))
                                    getView().onException99(restResponse.getDesc());
                                else
                                    getView().onException(restResponse.getDesc());
                            }
                        }));
    }

    @Override
    public void setUrlBusShuttle(@NonNull String urlFormBus) {
        this.mUrlFormBus = urlFormBus;
    }

    @Override
    public void setUrlKai(@NonNull String urlFormKai) {
        this.mUrlKai = urlFormKai;
    }

    @Override
    public void setUrlWebviewTugu(@NonNull String urlTugu) {
        this.mUrlWebviewTugu = urlTugu;
    }

    @Override
    public void getFormBus() {
        if (!isViewAttached()) {
            return;
        }

        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();

        getCompositeDisposable()
                .add(getApiSource().getDataForm(mUrlFormBus, seqNum)
                        .subscribeOn(getSchedulerProvider().io())
                        .observeOn(getSchedulerProvider().mainThread())
                        .subscribeWith(new ApiObserver(getView(), seqNum) {
                            @Override
                            public void onFailureHttp(String errorMessage) {
                                getView().hideProgress();
                                getView().onExceptionHttpErrorNotBack(errorMessage);
                            }

                            @Override
                            public void onApiCallSuccess(RestResponse response) {
                                getView().hideProgress();
                                CityFormResponse cityFormResponse = response.getData(CityFormResponse.class);
                                getView().onSuccessGetFormBus(cityFormResponse);
                            }

                            @Override
                            public void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                getView().onException(restResponse.getDesc());
                            }
                        }));
    }

    @Override
    public void getFormKai(String titleBar) {
        if (!isViewAttached()) {
            return;
        }

        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();

        getCompositeDisposable().add(
                getApiSource().getDataTanpaRequest(mUrlKai, seqNum)
                        .subscribeOn(getSchedulerProvider().io())
                        .observeOn(getSchedulerProvider().mainThread())
                        .subscribeWith(new ApiObserver(getView(), seqNum) {
                            @Override
                            public void onFailureHttp(String errorMessage) {
                                getView().hideProgress();
                                getView().onExceptionHttpErrorNotBack(errorMessage);
                            }

                            @Override
                            public void onApiCallSuccess(RestResponse response) {
                                getView().hideProgress();
                                UrlWebViewResponse urlWebViewResponse = response.getData(UrlWebViewResponse.class);
                                getView().onSuccessGetFormKai(urlWebViewResponse, titleBar);
                            }

                            @Override
                            public void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                getView().onException(restResponse.getDesc());
                            }
                        }));
    }

    @Override
    public void getWebViewTugu(PartnerIdRequest partnerIdRequest, String titleWebview, String codeMenu) {
        if (!isViewAttached()) {
            return;
        }

        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();

        getCompositeDisposable().add(
                getApiSource().getData(mUrlWebviewTugu, partnerIdRequest, seqNum)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(getSchedulerProvider().io())
                        .observeOn(getSchedulerProvider().mainThread())
                        .subscribeWith(new ApiObserver(getView(), seqNum) {
                            @Override
                            public void onFailureHttp(String errorMessage) {
                                if (getView() != null) {
                                    getView().hideProgress();
                                    getView().onExceptionHttpErrorNotBack(errorMessage);
                                }
                            }

                            @Override
                            public void onApiCallSuccess(RestResponse response) {
                                if (getView() != null) {
                                    getView().hideProgress();
                                    if (response.getCode().equals(Constant.RE_SUCCESS)) {
                                        GeneralWebviewResponse webviewResponse = response.getData(GeneralWebviewResponse.class);
                                        getView().onSuccessGetWebviewTugu(webviewResponse, titleWebview, codeMenu);
                                    } else if (response.getCode().equals(Constant.RE02)) {
                                        EODLifestyleResponse eodLifestyleResponse = response.getData(EODLifestyleResponse.class);
                                        getView().onMenuLifestyleEOD(eodLifestyleResponse);
                                    }
                                }
                            }

                            @Override
                            public void onApiCallError(RestResponse restResponse) {
                                if (getView() != null) {
                                    getView().hideProgress();
                                    if (Constant.RE_TRX_EXPIRED.equalsIgnoreCase(restResponse.getCode())) {
                                        getView().onExceptionTrxExpired(restResponse.getDesc());
                                    } else {
                                        getView().onException(restResponse.getDesc());
                                    }
                                }
                            }
                        }));
    }

    @Override
    public void getIndihomeRegistrationData(PartnerIdRequest partnerIdRequest, String titleWebview, String codeMenu) {
        if (Boolean.TRUE.equals(getBRImoPrefRepository().isIndihomeFirstClick())) {
            getWebViewTugu(new PartnerIdRequest(partnerIdRequest.getPartnerId()), titleWebview, codeMenu);
        } else {
            getView().showIndihomeConfirmation(new PartnerIdRequest(partnerIdRequest.getPartnerId()), titleWebview, codeMenu);
        }
    }

    @Override
    public void confirmIndihomeRegistration(PartnerIdRequest partnerIdRequest, String titleWebview, String codeMenu) {
        getBRImoPrefRepository().saveIndihomeFirstClick(true);
        getWebViewTugu(new PartnerIdRequest(partnerIdRequest.getPartnerId()), titleWebview, codeMenu);
    }
}