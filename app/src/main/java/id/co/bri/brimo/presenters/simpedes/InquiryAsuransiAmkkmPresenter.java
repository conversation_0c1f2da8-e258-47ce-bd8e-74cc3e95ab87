package id.co.bri.brimo.presenters.simpedes;

import java.util.concurrent.TimeUnit;

import javax.inject.Inject;

import id.co.bri.brimo.contract.IPresenter.simpedes.IInquiryAsuransiAmkkmPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.simpedes.IInquiryAsuransiAmkkmView;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.config.AppConfig;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.AccountModel;
import id.co.bri.brimo.models.apimodel.request.KonfirmasiAmkkmRequest;
import id.co.bri.brimo.models.apimodel.response.KonfirmasiAmkkmResponse;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.models.apimodel.response.SaldoReponse;
import id.co.bri.brimo.presenters.MvpPresenter;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.schedulers.Schedulers;

public class InquiryAsuransiAmkkmPresenter<V extends IMvpView & IInquiryAsuransiAmkkmView> extends MvpPresenter<V> implements IInquiryAsuransiAmkkmPresenter<V> {

    private String url;

    @Inject
    public InquiryAsuransiAmkkmPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }


    @Override
    public void setUrlKonfirmasiAsuransiAmkkm(String url) {
        this.url = url;
    }

    @Override
    public void getKonfirmasi() {
        getView().showProgress();
        KonfirmasiAmkkmRequest konfirmasiAmkkmRequest = new KonfirmasiAmkkmRequest(
                getView().getAmountAsuransi(),
                getView().getRefNumber(),
                getView().getAccNumber(),
                getView().getSaveAs());

        String seq = getBRImoPrefRepository().getSeqNumber();

        getCompositeDisposable().add(getApiSource().getData(url, konfirmasiAmkkmRequest, seq)
                .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(new ApiObserver(getView(), seq) {
                    @Override
                    protected void onFailureHttp(String type) {
                        getView().hideProgress();
                        getView().onException(type);
                    }

                    @Override
                    protected void onApiCallSuccess(RestResponse response) {
                        getView().hideProgress();
                        KonfirmasiAmkkmResponse konfirmasiAmkkmResponse = response.getData(KonfirmasiAmkkmResponse.class);
                        getView().getKonfirmasiResponse(konfirmasiAmkkmResponse);
                    }

                    @Override
                    protected void onApiCallError(RestResponse restResponse) {
                        getView().hideProgress();
                        if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                            getView().onSessionEnd(restResponse.getDesc());
                        else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_12.getValue())) {
                            getView().onException(restResponse.getDesc());
                        } else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_99.getValue()))
                            getView().onException99(restResponse.getDesc());
                        else
                            getView().onException(restResponse.getDesc());
                    }
                }));
    }

    @Override
    public void getSaldoSimpedes(AccountModel account) {
        String seq = getBRImoPrefRepository().getSeqNumber();
        getApiSource().getSaldoNormal(account.getAcoount(), seq)
                .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                .subscribeOn(getSchedulerProvider().newThread())
                .observeOn(getSchedulerProvider().mainThread())
                .subscribeWith(new ApiObserver(getView(), seq) {
                    @Override
                    protected void onFailureHttp(String errorMessage) {
                        getView().onException(errorMessage);
                    }

                    @Override
                    protected void onApiCallSuccess(RestResponse response) {
                        SaldoReponse saldoReponse;
                        if (restResponse.getDesc().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_01.getValue())) {
                            saldoReponse = new SaldoReponse();
                            saldoReponse.setBalanceString(restResponse.getDesc());
                        } else {
                            saldoReponse = restResponse.getData(SaldoReponse.class);
                            account.setAlias(saldoReponse.getAlias());
                        }
                        getView().setSaldoSimpedes(saldoReponse);
                    }

                    @Override
                    protected void onApiCallError(RestResponse restResponse) {
                        if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                            getView().onSessionEnd(restResponse.getDesc());
                        else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_12.getValue())) {
                            getView().onException(restResponse.getDesc());
                        } else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_99.getValue()))
                            getView().onException99(restResponse.getDesc());
                        else
                            getView().onException(restResponse.getDesc());

                    }
                });

    }

    @Override
    public void start() {
        super.start();
        getDefaultSaldo();
    }


    /**
     * Method yang digunakan untuk Set Default Saldo
     */
    protected void getDefaultSaldo() {
        double saldo = 0.0;
        String saldoText = getBRImoPrefRepository().getSaldoRekeningUtama();
        if (!saldoText.equals("")) {
            saldo = Double.valueOf(saldoText);
        }

        String defaultAcc = getBRImoPrefRepository().getAccountDefault();

        String saldoString = getBRImoPrefRepository().getSaldoRekeningUtamaString();
        boolean saldoHold = getBRImoPrefRepository().getSaldoHold();

        getView().setDefaultSaldo(saldo, saldoString, defaultAcc, saldoHold);
    }


}