package id.co.bri.brimo.presenters.onboardingnewskin

import id.co.bri.brimo.contract.IPresenter.newskinonboarding.ICheckPassPresenter
import id.co.bri.brimo.contract.IView.newskinonboarding.IChangePassView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.domain.config.AppConfig
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.onboardingrevamp.PassChangeRequest
import id.co.bri.brimo.models.apimodel.response.onboardingrevamp.PassCheckResponse
import id.co.bri.brimo.models.apimodel.response.onboardingrevamp.PinCheckSuccessData
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.Schedulers
import java.util.concurrent.TimeUnit

class ChangePassPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    private val brImoPrefRepository: BRImoPrefSource,
    private val apiSource: ApiSource
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    brImoPrefRepository,
    apiSource,
    null
), ICheckPassPresenter<V> where V : IChangePassView {

    private var urlValidatePass = ""
    override fun setValidatePassUrl(url: String) {
        urlValidatePass = url
    }

    override fun onCheckPassSubmit(pass: String) {
        if (!isViewAttached || urlValidatePass.isEmpty()) return

        view.showProgress()

        val seqNum = brImoPrefRepository.seqNumber

        val requestBody = PassChangeRequest(password = pass)

        compositeDisposable.add(
            apiSource.getData(urlValidatePass, requestBody, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onApiCallSuccess(response: RestResponse) {
                        view.hideProgress()
                        val checkResponse = response.getData(PinCheckSuccessData::class.java)
                        getView().onPassValid(checkResponse.referenceNumber, code = response.code)

                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        view.hideProgress()
                        val res = restResponse.getData(PassCheckResponse::class.java)
                        getView().onPassCheckFailed(res, restResponse.code)
                    }

                    override fun onFailureHttp(errorMessage: String) {
                        view.hideProgress()
                        getView().onFailure(true)
                    }
                })
        )
    }
}