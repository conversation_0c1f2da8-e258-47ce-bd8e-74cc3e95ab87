package id.co.bri.brimo.presenters.listrikrevamp

import id.co.bri.brimo.contract.IPresenter.listrikrevamp.IFormListrikRevampPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.base.IBaseFormRevampView
import id.co.bri.brimo.contract.IView.listrikrevamp.IFormListrikRevampView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.config.AppConfig
import id.co.bri.brimo.domain.config.enumconfig.JourneyType
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.InquiryPlnRequest
import id.co.bri.brimo.models.apimodel.request.UpdateSavedRequest
import id.co.bri.brimo.models.apimodel.response.ExceptionResponse
import id.co.bri.brimo.models.apimodel.response.InquiryBrivaRevampResponse
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.SavedResponse
import id.co.bri.brimo.presenters.base.BaseFormRevampPresenter
import io.reactivex.disposables.CompositeDisposable
import java.util.concurrent.TimeUnit

class FormListrikRevampPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBRImoPrefRepository: BRImoPrefSource,
    apiSource: ApiSource,
    transaksiPfmSource: TransaksiPfmSource
) : BaseFormRevampPresenter<V>(
    schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource
),
    IFormListrikRevampPresenter<V> where V : IMvpView, V : IBaseFormRevampView, V : IFormListrikRevampView {

    private var saveId = ""
    private var purchaseType = ""
    override fun setUpdateItem(
        url: String,
        savedResponse: SavedResponse,
        position: Int,
        type: Int,
        journeyType: JourneyType?
    ) {
        if (url.isEmpty() || !isViewAttached || onLoad) {
            return
        }
        onLoad = true
        val s = savedResponse.value
        val str1 = s.split("\\|".toRegex()).dropLastWhile { it.isEmpty() }
            .toTypedArray()

        if (str1.size > 1) {
            saveId = str1[0]
            purchaseType = str1[1]
        } else {
            saveId = str1[0]
        }

        val updateSavedRequest = UpdateSavedRequest(saveId, purchaseType)
        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable.add(apiSource.getData(url, updateSavedRequest, seqNum)
            .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
            .subscribeOn(schedulerProvider.io())
            .observeOn(schedulerProvider.mainThread())
            .subscribeWith(object : ApiObserver(view, seqNum) {
                override fun onFailureHttp(errorMessage: String) {
                    onLoad = false
                    getView().onException(errorMessage)
                }

                override fun onApiCallSuccess(response: RestResponse) {
                    onLoad = false
                    getView().hideProgress()
                    getView().onSuccessUpdate(savedResponse, position, type)
                }

                override fun onApiCallError(restResponse: RestResponse) {
                    onApiError(restResponse)
                }
            })
        )
    }

    override fun getDataInquiry(request: InquiryPlnRequest, nominal: String) {
        if (mUrlInquiry.isEmpty() || !isViewAttached) {
            return
        }
        view?.showProgress()
        val seqNum = brImoPrefRepository.seqNumber
        compositeDisposable
            .add(
                apiSource.getData(mUrlInquiry, request, seqNum)
                    .subscribeOn(schedulerProvider.io())
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView()?.onException(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            //TO-DO onSuccess
                            getView()?.hideProgress()
                            val responPln = response.getData(InquiryBrivaRevampResponse::class.java)
                            getView()?.onSuccessGetInquiry(responPln, mUrlConfirm, mUrlPayment, request.plnTypeCode, nominal)
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView().hideProgress()
                            if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value, ignoreCase = true)){
                                getView().onSessionEnd(restResponse.desc)
                            } else if (restResponse.code.equals("008", ignoreCase = true)){
                                val exceptionResponse : ExceptionResponse = restResponse.getData(ExceptionResponse::class.java)
                                getView().onException008(exceptionResponse)
                            } else if (restResponse.code.equals("088", ignoreCase = true)){
                                val exceptionResponse : ExceptionResponse = restResponse.getData(ExceptionResponse::class.java)
                                getView().onException088(exceptionResponse)
                            } else{
                                getView().onException(restResponse.desc)
                            }
                        }
                    })
            )
    }

    override fun setInquiryUrl(url: String) {
        mUrlInquiry = url
    }
}