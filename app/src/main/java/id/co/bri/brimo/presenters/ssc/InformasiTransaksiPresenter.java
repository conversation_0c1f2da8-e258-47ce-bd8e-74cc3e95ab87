package id.co.bri.brimo.presenters.ssc;


import id.co.bri.brimo.contract.IPresenter.ssc.IInformasiTransaksiPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.ssc.IInformasiTransaksiView;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.apimodel.request.CekNoRekRequest;
import id.co.bri.brimo.models.apimodel.response.ssc.ComplaintTicketCreateRes;
import id.co.bri.brimo.models.apimodel.response.DestinationNameRes;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.presenters.MvpPresenter;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.schedulers.Schedulers;

public class InformasiTransaksiPresenter<V extends IMvpView & IInformasiTransaksiView>
        extends MvpPresenter<V> implements IInformasiTransaksiPresenter<V> {

    protected String urlTicketCreate;
    protected String urlCekNoRek;

    public InformasiTransaksiPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void setUrlCreateTicket(String url) {
        this.urlTicketCreate = url;
    }

    @Override
    public void setUrlCheckAccount(String url) {
        this.urlCekNoRek = url;
    }

    @Override
    public void sendCreateTicket(Object object) {
        if (urlTicketCreate.isEmpty() || !isViewAttached()) return;

        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();

        getCompositeDisposable()
                .add(getApiSource().getData(urlTicketCreate, object, seqNum)
                        .subscribeOn(Schedulers.io())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(new ApiObserver(getView(), seqNum) {
                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().hideProgress();
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                getView().hideProgress();
                                ComplaintTicketCreateRes ticketCreateRes = response.getData(ComplaintTicketCreateRes.class);
                                getView().onSuccessCreateTicket(ticketCreateRes);
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                    getView().onSessionEnd(restResponse.getDesc());
                                else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.getValue()))
                                    getView().onException93(restResponse.getDesc());
                                else
                                    getView().onException(restResponse.getDesc());

                            }
                        }));
    }

    @Override
    public void getDataAccount(CekNoRekRequest request) {
        if (urlCekNoRek.isEmpty() || !isViewAttached()) return;

        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();

        getCompositeDisposable()
                .add(getApiSource().getData(urlCekNoRek, request, seqNum)
                        .subscribeOn(Schedulers.io())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(new ApiObserver(getView(), seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().hideProgress();
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                getView().hideProgress();
                                DestinationNameRes destinationRes = response.getData(DestinationNameRes.class);
                                getView().onSuccessAccount(destinationRes.getDestinationName());
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                    getView().onSessionEnd(restResponse.getDesc());
                                else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.getValue()))
                                    getView().onException93(restResponse.getDesc());
                                else
                                    getView().onExceptionAccount(restResponse.getDesc());
                            }
                        }));
    }
}