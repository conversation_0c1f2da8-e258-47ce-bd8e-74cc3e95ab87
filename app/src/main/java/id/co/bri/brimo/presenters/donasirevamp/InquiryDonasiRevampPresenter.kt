package id.co.bri.brimo.presenters.donasirevamp

import id.co.bri.brimo.contract.IPresenter.donasirevamp.IInquiryDonasiRevampPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.donasirevamp.IInquiryDonasiRevampView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimo.data.repository.category.CategoryPfmSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource

import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.donasirevamp.ConfirmationDonasiRevampRequest
import id.co.bri.brimo.models.apimodel.response.GeneralConfirmationResponse
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.disposables.CompositeDisposable

class InquiryDonasiRevampPresenter<V>(
    schedulerProvider: SchedulerProvider?,
    compositeDisposable: CompositeDisposable?,
    mBRImoPrefRepository: BRImoPrefSource?,
    apiSource: ApiSource?,
    categoryPfmSource: CategoryPfmSource?,
    transaksiPfmSource: TransaksiPfmSource?,
    anggaranPfmSource: AnggaranPfmSource?
) :
    MvpPresenter<V>(
        schedulerProvider,
        compositeDisposable,
        mBRImoPrefRepository,
        apiSource,
        transaksiPfmSource
    ),

    IInquiryDonasiRevampPresenter<V> where V : IMvpView, V : IInquiryDonasiRevampView {
    var mConfirmationUrl: String? = null

    override fun getDataConfirmation(request: ConfirmationDonasiRevampRequest) {
        if (mConfirmationUrl == null || !isViewAttached) {
            return
        }
        view?.showProgress()
        val seqNum = brImoPrefRepository.seqNumber
        compositeDisposable
            .add(
                apiSource.getData(mConfirmationUrl, request, seqNum)
                    .subscribeOn(schedulerProvider.io())
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView()?.onException(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            getView()?.hideProgress()
                            val responPln =
                                response.getData(GeneralConfirmationResponse::class.java)

                            //detaildataview tuker dengan amountDataView dam masukin transactiondataview
                            //untuk di general confirmation
                            val bodyUp = responPln.detailDataView
                            val bodyDown = responPln.amountDataView
                            responPln.detailDataView = bodyDown
                            responPln.transactionDataView = bodyUp
                            getView()?.onSuccessConfirmation(responPln)
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView().hideProgress()
                            if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value, ignoreCase = true))
                                getView().onSessionEnd(restResponse.desc)
                            else if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.value, ignoreCase = true))
                                getView().onExceptionTrxExpired(restResponse.desc)
                            else if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_99.value, true)) {
                                getView().onException99(restResponse.desc)
                            } else getView().onException(restResponse.desc)
                        }
                    })
            )
    }

    override fun start() {
        super.start()
        getDefaultSaldo()
    }

    override fun setUrlConfirmation(url: String?) {
        mConfirmationUrl = url
    }

    override fun getAccountDefault(): String {
        return brImoPrefRepository.accountDefault
    }

    override fun getSaldoRekeningUtama(): String {
        return brImoPrefRepository.saldoRekeningUtama
    }

    protected fun getDefaultSaldo() {
        var saldo = 0.0
        val saldoText = brImoPrefRepository.saldoRekeningUtamaString
        if (saldoText != "") {
            val cleanString = saldoText.replace("[,.]".toRegex(), "")
            try {
                saldo = cleanString.toDouble() / 100
            } catch (e: Exception) {
                // do nothing
            }
        }
        val saldoHold = brImoPrefRepository.saldoHold

        val defaultAcc = brImoPrefRepository.accountDefault

        val saldoString = brImoPrefRepository.saldoRekeningUtamaString
        view.setDefaultSaldo(saldo, saldoString, defaultAcc, saldoHold)
    }
}