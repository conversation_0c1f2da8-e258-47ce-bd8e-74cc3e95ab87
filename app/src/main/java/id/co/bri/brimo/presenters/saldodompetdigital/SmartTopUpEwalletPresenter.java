package id.co.bri.brimo.presenters.saldodompetdigital;

import id.co.bri.brimo.contract.IPresenter.saldodompetdigital.ISmartTopupEwalletPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.saldodompetdigital.ISmartTopUpEwalletView;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.presenters.MvpPresenter;

import id.co.bri.brimo.ui.activities.saldodompetdigital.SmartTopUpEwalletActivity;
import io.reactivex.disposables.CompositeDisposable;

public class SmartTopUpEwalletPresenter<V extends IMvpView & ISmartTopUpEwalletView> extends MvpPresenter<V> implements ISmartTopupEwalletPresenter<V> {

    private static final String TAG = "HubungkanDompetDigitalPresenter";
    protected String formUrl;
    protected String fiturUrl;


    boolean isLoading = false;

    public SmartTopUpEwalletPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }


    @Override
    public void getInfoSmartTopUp() {
        SmartTopUpEwalletActivity.SmartTopUpModel smartTopUpModel = new SmartTopUpEwalletActivity.SmartTopUpModel("Agung Harsono", "Gopay - 08xxxxxxx321", "Aktif", true, "Rp 10.000", "Rp 200.000", "Rp 1.000.000");
//        SmartTopUpEwalletActivity.SmartTopUpModel smartTopUpModel = new SmartTopUpEwalletActivity.SmartTopUpModel("Agung Harsono", "LinkAja - 08xxxxxxx321", "Nonaktif", false, "Rp 10.000", "Rp 200.000", "Rp 1.000.000");
        getView().onSuccessGetSmartTopUpInfo(smartTopUpModel);
//        if (isViewAttached()) {
//            String seqNum = getBRImoPrefRepository().getSeqNumber();
//            getView().showProgress();
//            getCompositeDisposable().add(
//                    getApiSource().getData(url, request, seqNum)
//                            .subscribeOn(getSchedulerProvider().single())
//                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
//                            .observeOn(getSchedulerProvider().mainThread())
//                            .subscribeWith(new ApiObserver(seqNum) {
//                                @Override
//                                protected void onFailureHttp(String type) {
//                                    getView().hideProgress();
//                                    getView().onException(type);
//                                }
//
//                                @Override
//                                protected void onApiCallSuccess(RestResponse response) {
//                                    getView().hideProgress();
//
//                                    //update login flag
//                                    updateLoginFlag(true);
//
//                                    getView().onSuccessEdit();
//                                }
//
//                                @Override
//                                protected void onApiCallError(RestResponse restResponse) {
//                                    getView().hideProgress();
//                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue())) {
//                                        getView().onSessionEnd(restResponse.getDesc());
//                                    } else
//                                        getView().onException(restResponse.getDesc());
//                                }
//                            })
//            );
//        }

    }

//    @Override
//    public void setFormUrl(String formUrl) {
//        this.formUrl = formUrl;
//    }


    @Override
    public void start() {
        super.start();

    }


}