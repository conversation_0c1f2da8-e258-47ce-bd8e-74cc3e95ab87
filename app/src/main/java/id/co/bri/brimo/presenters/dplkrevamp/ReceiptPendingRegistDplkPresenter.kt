package id.co.bri.brimo.presenters.dplkrevamp

import id.co.bri.brimo.contract.IPresenter.dplkrevamp.IReceiptPendingRegistDplkPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.dplkrevamp.IReceiptPendingRegistDplkView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.config.AppConfig
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.dplkrevamp.CheckStatusDplkRequest
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.dplkrevamp.MessageErrorCheckStatusResponse
import id.co.bri.brimo.models.apimodel.response.dplkrevamp.PaymentFtuDplkResponse
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.Schedulers
import java.util.concurrent.TimeUnit

class ReceiptPendingRegistDplkPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBRImoPrefRepository: BRImoPrefSource,
    apiSource: ApiSource,
    transaksiPfmSource: TransaksiPfmSource,
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    mBRImoPrefRepository,
    apiSource,
    transaksiPfmSource,
), IReceiptPendingRegistDplkPresenter<V> where V : IMvpView, V : IReceiptPendingRegistDplkView {
    private var mUrlCheckStatusDplk = ""
    override fun setUrlCheckStatusDplk(url: String) {
        mUrlCheckStatusDplk = url
    }

    override fun getCheckStatusDplk(request: CheckStatusDplkRequest) {
        if (!isViewAttached) {
            return
        }
        view.showProgress()
        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable
            .add(
                apiSource.getData(mUrlCheckStatusDplk, request, seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView().hideProgress()
                            getView().onException(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            getView().hideProgress()
                            val data = response.getData(PaymentFtuDplkResponse::class.java)
                            when (restResponse.code) {
                                Constant.RE_HAS_INFO_STATUS -> {
                                    getView().onException(data.message)
                                }

                                Constant.RE_REDIRECT_CHECK_STATUS -> {
                                    getView().onResetCheckStatus(data.message)
                                }

                                else -> {
                                    getView().onSuccesCheckStatus(data)
                                }
                            }
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView().hideProgress()
                            val data =
                                restResponse.getData(MessageErrorCheckStatusResponse::class.java)
                            when (restResponse.code) {
                                Constant.RE_HAS_INFO_STATUS -> {
                                    getView().onException(data.message)
                                }

                                Constant.RE_REDIRECT_CHECK_STATUS -> {
                                    getView().onResetCheckStatus(data.message)
                                }

                                else -> {
                                    getView().onException(restResponse.desc)
                                }
                            }
                        }
                    })
            )
    }
}