package id.co.bri.brimo.presenters.login

import id.co.bri.brimo.contract.IPresenter.login.IKeamananPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.login.IKeamananView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimo.data.repository.category.CategoryPfmSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.config.AppConfig

import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.login.AktivasiBiometricRequest
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.biometric.EnrollBiometricResponse
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.Schedulers
import java.util.concurrent.TimeUnit

class KeamananPresenter<V>(schedulerProvider: SchedulerProvider?,
                           compositeDisposable: CompositeDisposable?,
                           mBRImoPrefRepository: BRImoPrefSource?, apiSource: ApiSource?,
                           categoryPfmSource: CategoryPfmSource?,
                           transaksiPfmSource: TransaksiPfmSource?,
                           anggaranPfmSource: AnggaranPfmSource?) : MvpPresenter<V>(
    schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource
),
    IKeamananPresenter<V> where V : IMvpView?, V : IKeamananView? {

    private var urlBiometric: String? = null
    private var urlRemoveBio: String? = null

    override fun setUrlEnrollBiometric(urlBio: String?) {
        urlBiometric = urlBio
    }

    override fun setUrlRemoveBiometric(urlRemove: String?) {
        urlRemoveBio = urlRemove
    }

    override fun getDataBiometric(pin: String?, checkSum: String?) {
        if (urlBiometric == null || !isViewAttached) {
            return
        }

        view!!.showProgress()
        val aktivasiBiometricRequest = AktivasiBiometricRequest(pin, checkSum)

        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable
            .add(
                apiSource.getData(urlBiometric, aktivasiBiometricRequest, seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView()!!.hideProgress()
                            getView()!!.onException(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            //TO-DO onSuccess
                            getView()!!.hideProgress()
                            if (response.code.equals(RestResponse.ResponseCodeEnum.RC_SUCCESS.value, ignoreCase = true)) {
                                val enrollBiometricResponse = response.getData(
                                    EnrollBiometricResponse::class.java)
                                getView()?.onSuccessAktifBiometric(enrollBiometricResponse)
                            }
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView()!!.hideProgress()
                            if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value, ignoreCase = true)) {
                                getView()!!.onSessionEnd(restResponse.desc)
                            } else if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_12.value, ignoreCase = true)) {
                                getView()!!.onException12Bio(restResponse.desc)
                            } else
                                getView()!!.onExceptionBio(restResponse.desc)
                        }
                    })
            )
    }

    override fun getRemoveBiometric(pin: String?, checkSum: String?) {
        if (urlRemoveBio == null || !isViewAttached) {
            return
        }

        view!!.showProgress()
        val aktivasiBiometricRequest = AktivasiBiometricRequest(pin, checkSum)

        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable
            .add(
                apiSource.getData(urlRemoveBio, aktivasiBiometricRequest, seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView()!!.hideProgress()
                            getView()!!.onException(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            //TO-DO onSuccess
                            getView()!!.hideProgress()
                            if (response.code.equals(RestResponse.ResponseCodeEnum.RC_SUCCESS.value, ignoreCase = true)) {
                                getView()?.onSuccessRemoveBiometric()
                            }
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView()!!.hideProgress()
                            if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value, ignoreCase = true)) {
                                getView()!!.onSessionEnd(restResponse.desc)
                            } else if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_12.value, ignoreCase = true)) {
                                getView()!!.onException12Bio(restResponse.desc)
                            } else
                                getView()!!.onExceptionBio(restResponse.desc)
                        }
                    })
            )
    }

    override fun getStatusAktivasi(): Boolean {
        return brImoPrefRepository.statusAktivasi
    }

    override fun getValueKeyBiometric(): String {
        return brImoPrefRepository.valueKeyBiometric
    }

    override fun updateStatusAktivasi(statusAktivasi: Boolean) {
        brImoPrefRepository.saveStatusAktivasi(statusAktivasi)
    }

    override fun updateBioChanged(bioChanged: Boolean) {
        brImoPrefRepository.saveStatusBioChange(bioChanged)
    }

    override fun updateStatusUpdateBio(statusUpdate: Boolean) {
        brImoPrefRepository.saveStatusUpdateBio(statusUpdate)
    }

    override fun getBiometricType(): String {
        return brImoPrefRepository.biometricType
    }

    override fun getBioChanged(): Boolean {
        return brImoPrefRepository.statusBioChange
    }

}