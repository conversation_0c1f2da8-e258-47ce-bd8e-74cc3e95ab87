package id.co.bri.brimo.presenters.login

import id.co.bri.brimo.contract.IPresenter.newskinonboarding.IVerifyNoHpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.newskinonboarding.IVerifyNoHpView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.config.AppConfig
import id.co.bri.brimo.domain.config.OnboardingConfig
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.SendPhoneRequest
import id.co.bri.brimo.models.apimodel.request.onboardingrevamp.SendNoHpRequest
import id.co.bri.brimo.models.apimodel.response.Country
import id.co.bri.brimo.models.apimodel.response.OnboardingResponse
import id.co.bri.brimo.models.apimodel.response.OtpPhoneResponse
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.newskinonboarding.OnboardingErrorResponse
import id.co.bri.brimo.models.apimodel.response.onboardingrevamp.CodeValueIcon
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.Schedulers
import java.util.concurrent.TimeUnit

class VerifyNoHpPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    private val mBRImoPrefRepository: BRImoPrefSource,
    apiSource: ApiSource,
    transaksiPfmSource: TransaksiPfmSource,
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    mBRImoPrefRepository,
    apiSource,
    transaksiPfmSource
), IVerifyNoHpPresenter<V> where V : IMvpView, V : IVerifyNoHpView {

    private var urlVerifyNoHp = ""

    override fun setUrlVerifyNoHp(url: String) {
        urlVerifyNoHp = url
    }

    override fun sendVerifyNoHp(countryCode: String, phoneNumber: String, method: String) {
        if (urlVerifyNoHp.isEmpty() || !isViewAttached) return
        view.showProgress()

        val request = SendNoHpRequest(
            countryCode = countryCode,
            phone = phoneNumber,
            method = method
        )
        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable.add(
            apiSource.getData(urlVerifyNoHp, request, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView().hideProgress()
                        getView().onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView().hideProgress()
                        val onboardRes = response.getData(OnboardingResponse::class.java)
                        if (onboardRes.status == OnboardingConfig.StatusNewOnboarding.READY_TO_LOGIN.name) {
                            val errorResponse =
                                response.getData(OnboardingErrorResponse::class.java)
                            getView().onReadyToLogin(errorResponse)
                        } else {
                            val otpResponse = response.getData(OtpPhoneResponse::class.java)
                            getView().onSuccessVerifyNoHp(otpResponse)
                        }
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView().hideProgress()
                        getView().onException(restResponse.desc)
                    }
                })
        )
    }
}