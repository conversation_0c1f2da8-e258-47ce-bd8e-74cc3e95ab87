package id.co.bri.brimo.presenters.ssc;

import java.util.concurrent.TimeUnit;

import id.co.bri.brimo.contract.IPresenter.ssc.IPengaduanTransaksiPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.ssc.IPengaduanTransaksiView;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.config.AppConfig;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.apimodel.request.InformasiSscSkipMutasiReq;
import id.co.bri.brimo.models.apimodel.response.ssc.ComplaintGroupResponse;
import id.co.bri.brimo.models.apimodel.response.ssc.ComplaintInformasiResponse;
import id.co.bri.brimo.models.apimodel.response.ssc.ComplaintProdukLainRes;
import id.co.bri.brimo.models.apimodel.response.ListRekeningResponse;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.presenters.MvpPresenter;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.schedulers.Schedulers;

public class PengaduanTransaksiPresenter<V extends IMvpView & IPengaduanTransaksiView>
        extends MvpPresenter<V> implements IPengaduanTransaksiPresenter<V> {

    protected String urlGroup;
    protected String urlProdukLain;
    protected String urlAccount;
    protected String urlInformasinonMutasi;
    protected String urlPinjamanListForm;

    public PengaduanTransaksiPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable,
                                       BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource,
                                       TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void setUrlGroup(String urlGroup) {
        this.urlGroup = urlGroup;
    }

    @Override
    public void setUrlProdukLain(String urlProdukLain) {
        this.urlProdukLain = urlProdukLain;
    }

    @Override
    public void setUrlAccount(String urlAccount) {
        this.urlAccount = urlAccount;
    }

    @Override
    public void setUrlInformasiNoMutasi(String urlInformasinonMutasi) {
        this.urlInformasinonMutasi = urlInformasinonMutasi;
    }

    @Override
    public void setUrlPinjamanListForm(String urlPinjamanListForm) {
        this.urlPinjamanListForm = urlPinjamanListForm;
    }

    @Override
    public void onGetBrigunaListForm(ComplaintInformasiResponse complaintInformasiResponse, String toolbar) {
        if (isViewAttached()) {

            String seqNum = getBRImoPrefRepository().getSeqNumber();
            getCompositeDisposable()
                    .add(getApiSource().getDataTanpaRequest(urlPinjamanListForm, seqNum)
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .subscribeOn(Schedulers.io())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribeWith(new ApiObserver(getView(), seqNum) {

                                @Override
                                protected void onFailureHttp(String errorMessage) {
                                    getView().onException(errorMessage);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    ComplaintGroupResponse complaintResponse = response.getData(ComplaintGroupResponse.class);
                                    getView().getDataSuccessBrigunaList(complaintResponse.getAccount(), complaintInformasiResponse, toolbar);
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().hideProgress();
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                        getView().onSessionEnd(restResponse.getDesc());
                                    else
                                        getView().onException(restResponse.getDesc());

                                }
                            }));
        }
    }


    @Override
    public void onSendDataGrouup() {
        if (isViewAttached()) {

            String seqNum = getBRImoPrefRepository().getSeqNumber();
            getCompositeDisposable()
                    .add(getApiSource().getDataTanpaRequest(urlGroup, seqNum)
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .subscribeOn(Schedulers.io())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribeWith(new ApiObserver(getView(), seqNum) {

                                @Override
                                protected void onFailureHttp(String errorMessage) {
                                    getView().onException(errorMessage);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    ComplaintGroupResponse complaintResponse = response.getData(ComplaintGroupResponse.class);
                                    getView().getDataSuccessGroup(complaintResponse);
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().hideProgress();
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                        getView().onSessionEnd(restResponse.getDesc());
                                    else
                                        getView().onException(restResponse.getDesc());

                                }
                            }));
        }
    }

    @Override
    public void onSendDataProdukLain() {
        if (isViewAttached()) {

            String seqNum = getBRImoPrefRepository().getSeqNumber();
            getCompositeDisposable()
                    .add(getApiSource().getDataTanpaRequest(urlProdukLain, seqNum)
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .subscribeOn(Schedulers.io())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribeWith(new ApiObserver(getView(), seqNum) {

                                @Override
                                protected void onFailureHttp(String errorMessage) {
                                    getView().onException(errorMessage);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    ComplaintProdukLainRes produkLainRes = response.getData(ComplaintProdukLainRes.class);
                                    getView().getDataSuccessProdukLain(produkLainRes);

                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().hideProgress();
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                        getView().onSessionEnd(restResponse.getDesc());
                                    else
                                        getView().onException(restResponse.getDesc());

                                }
                            }));
        }
    }

    @Override
    public void onGetDataAccount(String toolbar) {
        if (isViewAttached()) {

            getView().showProgress();
            String seqNum = getBRImoPrefRepository().getSeqNumber();
            getCompositeDisposable()
                    .add(getApiSource().getDataTanpaRequest(urlAccount, seqNum)
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .subscribeOn(Schedulers.io())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribeWith(new ApiObserver(getView(), seqNum) {

                                @Override
                                protected void onFailureHttp(String errorMessage) {
                                    getView().hideProgress();
                                    getView().onException(errorMessage);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    ListRekeningResponse listRekeningResponse = response.getData(ListRekeningResponse.class);
                                    getView().getDataSuccessAccount(listRekeningResponse.getAccount(), toolbar);

                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().hideProgress();
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                        getView().onSessionEnd(restResponse.getDesc());
                                    else
                                        getView().onException(restResponse.getDesc());

                                }
                            }));
        }
    }

    @Override
    public void onSendDataInfoNoMutasi(InformasiSscSkipMutasiReq infoSscSkipMutasiReq, String toolbar) {
        if (isViewAttached()) {
            String seq = getBRImoPrefRepository().getSeqNumber();

            getCompositeDisposable().add(
                    getApiSource().getData(urlInformasinonMutasi, infoSscSkipMutasiReq, seq)
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .subscribeOn(Schedulers.single())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribeWith(new ApiObserver(getView(), seq) {

                                @Override
                                protected void onFailureHttp(String errorMessage) {
                                    getView().hideProgress();
                                    getView().onException(errorMessage);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    getView().hideProgress();
                                    ComplaintInformasiResponse complaintInformasiResponse = response.getData(ComplaintInformasiResponse.class);
                                    getView().onSuccessGetInfoNoMutasi(complaintInformasiResponse, toolbar);
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().hideProgress();
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                        getView().onSessionEnd(restResponse.getDesc());
                                    else
                                        getView().onException(restResponse.getDesc());
                                }
                            })
            );
        }
    }
}