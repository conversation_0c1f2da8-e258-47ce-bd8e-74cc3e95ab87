package id.co.bri.brimo.presenters.bifast;

import java.util.concurrent.TimeUnit;

import id.co.bri.brimo.contract.IPresenter.bifast.IBiFastPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.base.IBaseFormView;
import id.co.bri.brimo.contract.IView.bifast.IBiFastView;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.config.AppConfig;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.apimodel.request.BiFastUpdateRequest;
import id.co.bri.brimo.models.apimodel.response.AliasBiFastResponse;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.presenters.base.BaseFormPresenter;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.disposables.Disposable;

public class BiFastPresenter<V extends IMvpView & IBaseFormView & IBiFastView> extends BaseFormPresenter<V> implements IBiFastPresenter<V> {

    String formServisUrl;

    public BiFastPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, categoryPfmSource, transaksiPfmSource, anggaranPfmSource);
    }

    @Override
    public void getUpdateBiFast(BiFastUpdateRequest biFastUpdateRequest) {
        if (formServisUrl == null || !isViewAttached()) {
//            Log.d(TAG, "getDataInquiry: view atau inquiry url null");
            return;
        }

        String seqNum = getBRImoPrefRepository().getSeqNumber();
        getView().showProgress();
        Disposable disposable = getApiSource().getData(formServisUrl, biFastUpdateRequest, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                .subscribeOn(getSchedulerProvider().io())
                .observeOn(getSchedulerProvider().mainThread())
                .subscribeWith(new ApiObserver(getView(), seqNum) {
                    @Override
                    public void addOnPropertyChangedCallback(OnPropertyChangedCallback callback) {

                    }

                    @Override
                    public void removeOnPropertyChangedCallback(OnPropertyChangedCallback callback) {

                    }

                    @Override
                    protected void onFailureHttp(String errorMessage) {
                        getView().hideProgress();
                        getView().onException(errorMessage);
                    }

                    @Override
                    protected void onApiCallSuccess(RestResponse response) {
                        getView().hideProgress();
                        String message = response.getDesc();
                        if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SUCCESS.getValue())) {
                            AliasBiFastResponse aliasBiFastResponse = response.getData(AliasBiFastResponse.class);
                            getView().onSuccessGetUpdate(aliasBiFastResponse, message, biFastUpdateRequest);
                        } else if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_01.getValue())) {
                            getView().onFailedSetUpdate(response.getDesc(), biFastUpdateRequest);
                        }

                    }

                    @Override
                    protected void onApiCallError(RestResponse restResponse) {
                        getView().hideProgress();
                        if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                            getView().onSessionEnd(restResponse.getDesc());
                        else
                            getView().onFailedSetUpdate(restResponse.getDesc(), biFastUpdateRequest);
//                            getView().onException(restResponse.getDesc());
                    }
                });
        getCompositeDisposable().add(disposable);
    }

    @Override
    public void setUrl(String formUrl) {
        this.formServisUrl = formUrl;
    }
}