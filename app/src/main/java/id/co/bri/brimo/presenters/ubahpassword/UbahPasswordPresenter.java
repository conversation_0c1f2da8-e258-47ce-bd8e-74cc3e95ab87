package id.co.bri.brimo.presenters.ubahpassword;

import java.util.concurrent.TimeUnit;

import javax.inject.Inject;

import id.co.bri.brimo.contract.IPresenter.ubahpassword.IUbahPasswordPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.ubahpassword.IUbahPasswordView;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.config.AppConfig;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.apimodel.request.UbahPasswordRequest;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.models.apimodel.response.UbahPasswordResponse;
import id.co.bri.brimo.presenters.MvpPresenter;
import io.reactivex.disposables.CompositeDisposable;

public class UbahPasswordPresenter<V extends IMvpView & IUbahPasswordView> extends MvpPresenter<V> implements IUbahPasswordPresenter<V> {

    private String urlCheckPassKunci = "";

    @Inject
    public UbahPasswordPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void onUbahPasswordSubmit() {
        if (getView() == null) {
            return;
        }
        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        UbahPasswordRequest ubahPasswordRequest = new UbahPasswordRequest(getView().getPasswordLama());
        if (isViewAttached()) {
            getCompositeDisposable().add(
                    getApiSource().getData(urlCheckPassKunci, ubahPasswordRequest, seqNum)
                            .subscribeOn(getSchedulerProvider().single())
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .observeOn(getSchedulerProvider().mainThread())
                            .subscribeWith(new ApiObserver(getView(),seqNum) {
                                @Override
                                protected void onFailureHttp(String type) {
                                    getView().hideProgress();
                                    getView().onException(type);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    getView().hideProgress();
                                    UbahPasswordResponse ubahPasswordResponse = response.getData(UbahPasswordResponse.class);
                                    getView().onSubmitSuccess(ubahPasswordResponse.getReference_number());
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().hideProgress();
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue())) {
                                        getView().onSessionEnd(restResponse.getDesc());
                                    }
                                    else if (restResponse.getCode().equalsIgnoreCase("50")){
                                        getView().onException50(restResponse.getDesc());
                                    }
                                    else if (restResponse.getCode().equalsIgnoreCase("12")) {
                                        getView().onException12();
                                    }
                                    else if (restResponse.getCode().equalsIgnoreCase("A1")) {
                                        getView().onErrorA1(restResponse.getDesc());
                                    } else if (restResponse.getCode().equalsIgnoreCase("UB")) {
                                        getView().onMaxPass(restResponse.getDesc());
                                    } else
                                        getView().onException(restResponse.getDesc());
                                }
                            })
            );
        }

    }

    @Override
    public void setUrlValidatePassKunci(String urlUbahPassword) {
        this.urlCheckPassKunci = urlUbahPassword;
    }

}