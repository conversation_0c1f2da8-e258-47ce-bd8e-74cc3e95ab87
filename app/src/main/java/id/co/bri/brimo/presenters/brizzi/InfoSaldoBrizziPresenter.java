package id.co.bri.brimo.presenters.brizzi;

import id.co.bri.brimo.contract.IPresenter.brizzi.IInforSaldoBrizziPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.base.IBaseFormView;
import id.co.bri.brimo.contract.IView.brizzi.IInfoSaldoBrizziView;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.apimodel.request.FastMenuInquiryTopUpOnline;
import id.co.bri.brimo.models.apimodel.request.InquiryBrizziRequest;
import id.co.bri.brimo.models.apimodel.response.GeneralInquiryResponse;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.presenters.base.BaseFormPresenter;


import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.schedulers.Schedulers;

public class InfoSaldoBrizziPresenter<V extends IMvpView  & IBaseFormView & IInfoSaldoBrizziView> extends BaseFormPresenter<V> implements IInforSaldoBrizziPresenter<V> {

    private String inquiryUrl = null;
 ;

    private String konfirmasiUrl = null;

    private String paymentUrl = null;

    public InfoSaldoBrizziPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, categoryPfmSource, transaksiPfmSource, anggaranPfmSource);
    }

    /**
     * @param card
     * @param isFromFast
     */
    @Override
    public void getDataInquiryTopUp(String card, boolean isFromFast) {
        if (isViewAttached()) {
            getView().showProgress();

            if (isFromFast)
                inquiryRequest = new FastMenuInquiryTopUpOnline(getFastMenuRequest(), card);
            else
                inquiryRequest = new InquiryBrizziRequest(card);

            String seqNum = getBRImoPrefRepository().getSeqNumber();

            getCompositeDisposable().add(getApiSource().getData(inquiryUrl, inquiryRequest, seqNum)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(new ApiObserver(getView(),seqNum) {
                        @Override
                        protected void onFailureHttp(String errorMessage) {
                            getView().onException(errorMessage);
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            getView().hideProgress();

                            GeneralInquiryResponse responsebrizzi = response.getData(GeneralInquiryResponse.class);
                            getView().onSuccessGetInquiryTopUp(responsebrizzi, konfirmasiUrl, paymentUrl);
                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            getView().hideProgress();
                            if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                getView().onSessionEnd(restResponse.getDesc());
                            else
                                getView().onException(restResponse.getDesc());
                        }
                    }));
        }
    }


    public String getInquiryUrl() {
        return inquiryUrl;
    }

    public void setInquiryUrl(String inquiryUrl) {
        this.inquiryUrl = inquiryUrl;
    }

    @Override
    public void setKonfirmasiUrl(String url) {
        this.konfirmasiUrl = url;
    }

    @Override
    public void setPaymentUrl(String url) {
        this.paymentUrl = url;
    }
}