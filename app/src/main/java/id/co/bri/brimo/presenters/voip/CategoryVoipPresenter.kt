package id.co.bri.brimo.presenters.voip

import id.co.bri.brimo.contract.IPresenter.voip.ICategoryVoipPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.voip.ICategoryVoipView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimo.data.repository.category.CategoryPfmSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.config.AppConfig
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.RevokeSessionRequest
import id.co.bri.brimo.models.apimodel.request.voip.ListVoipFm
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.TokenVoipResponse
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.Schedulers
import java.util.concurrent.TimeUnit

class CategoryVoipPresenter<V>(
    schedulerProvider: SchedulerProvider?,
    compositeDisposable: CompositeDisposable?,
    mBRImoPrefRepository: BRImoPrefSource?,
    apiSource: ApiSource?,
    categoryPfmSource: CategoryPfmSource?,
    transaksiPfmSource: TransaksiPfmSource?,
    anggaranPfmSource: AnggaranPfmSource?
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    mBRImoPrefRepository,
    apiSource,
    transaksiPfmSource
),
    ICategoryVoipPresenter<V> where V : IMvpView, V : ICategoryVoipView {

    private var urlVoip = ""
    private var urlRevoke = ""
    private var urlFmVoip = ""

    override fun setUrlVoip(urlVoip: String) {
        this.urlVoip = urlVoip
    }

    override fun getVoip() {
        if (urlVoip.isEmpty() && !isViewAttached()) return

        getView().showProgress()
        val seqNum: String = getBRImoPrefRepository().getSeqNumber()

        getCompositeDisposable().add(
            getApiSource().getDataTanpaRequest(urlVoip, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(schedulerProvider.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(
                    object : ApiObserver(getView(), seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView().hideProgress()
                            getView().onException(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            //TO-DO onSuccess
                            getView().hideProgress()
                            val voipResponse = response.getData(TokenVoipResponse::class.java)
                            getView().onSuccessVoip(voipResponse)
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView().hideProgress()
                            getView().onException(restResponse.desc)
                        }
                    })
        )
    }

    override fun getInitiateResource() {
        getView().onInitiateResourceSuccess(
            getBRImoPrefRepository().getUsername(),
            getBRImoPrefRepository().getTokenKey()
        )
    }

    override fun setUrlRevoke(urlRevoke: String) {
        this.urlRevoke = urlRevoke
    }

    override fun revokeSession(request: RevokeSessionRequest) {
        if (urlRevoke.isEmpty() || !isViewAttached()) return
        getView().showProgress()
        val seqNum = getBRImoPrefRepository().getSeqNumber()
        getCompositeDisposable().add(
            getApiSource().getData(urlRevoke, request, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(object : ApiObserver(getView(), seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView().hideProgress()
                        getView().onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView().hideProgress()
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView().hideProgress()
                        getView().onException(restResponse.desc)

                    }
                })
        )
    }

    override fun setUrlFmVoip(urlFmVoip: String) {
        this.urlFmVoip = urlFmVoip
    }

    override fun getFmVoip() {
        if (urlFmVoip.isEmpty() && !isViewAttached()) return

        getView().showProgress()
        val seqNum: String = getBRImoPrefRepository().getSeqNumber()
        val request = ListVoipFm(
                getBRImoPrefRepository().getUsername(), getBRImoPrefRepository().getTokenKey())

        getCompositeDisposable().add(
                getApiSource().getData(urlFmVoip, request, seqNum)
                        .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                        .subscribeOn(schedulerProvider.io())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(
                                object : ApiObserver(getView(), seqNum) {
                                    override fun onFailureHttp(errorMessage: String) {
                                        getView().hideProgress()
                                        getView().onException(errorMessage)
                                    }

                                    override fun onApiCallSuccess(response: RestResponse) {
                                        //TO-DO onSuccess
                                        getView().hideProgress()
                                        val voipResponse = response.getData(TokenVoipResponse::class.java)
                                        getView().onSuccessVoip(voipResponse)
                                    }

                                    override fun onApiCallError(restResponse: RestResponse) {
                                        getView().hideProgress()
                                        getView().onException(restResponse.desc)
                                    }
                                })
        )
    }
}