package id.co.bri.brimo.presenters.activationdebit

import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IPresenter.activationdebit.IActivationDebitConfirmationPresenter
import id.co.bri.brimo.contract.IView.activationdebit.IActivationDebitConfirmationView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.activationdebit.response.SubmitActivationDebitResponse
import id.co.bri.brimo.models.apimodel.request.activationdebit.SubmitActivationDebitRequest
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.util.toRestResponseCodeEnum
import id.co.bri.brimo.presenters.MvpPresenter
import id.co.bri.brimo.util.extension.getDataWithOrWithoutRequest
import io.reactivex.disposables.CompositeDisposable

class ActivationDebitSubmitPresenter<V>(
    schedulerProvider: SchedulerProvider?,
    compositeDisposable: CompositeDisposable?,
    mBRImoPrefRepository: BRImoPrefSource?,
    apiSource: ApiSource?,
    transaksiPfmSource: TransaksiPfmSource?
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    mBRImoPrefRepository,
    apiSource,
    transaksiPfmSource
), IActivationDebitConfirmationPresenter<V> where V : IMvpView, V : IActivationDebitConfirmationView {
    override fun getConfirmationActivationDebit(
        urlConfirmation: String,
        request: SubmitActivationDebitRequest
    ) {
        view.getDataWithOrWithoutRequest(
            urlConfirmation,
            brImoPrefRepository,
            compositeDisposable,
            apiSource,
            schedulerProvider,
            true,
            request,
            onApiCallError = { restResponse ->
                val responseCode = restResponse.code
                val responseMessage = restResponse.desc
                when (responseCode.toRestResponseCodeEnum()) {
                    RestResponse.ResponseCodeEnum.RC_NR,
                    RestResponse.ResponseCodeEnum.RC_UV -> view.onErrorSubmitActivationDebit(responseMessage)
                    else -> view.onException(responseMessage)
                }
            }
        ) {
            view.onSuccessSubmitActivationDebit(it.getData(SubmitActivationDebitResponse::class.java))
        }
    }
}