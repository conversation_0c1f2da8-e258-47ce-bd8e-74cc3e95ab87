package id.co.bri.brimo.presenters.pulsa;

import android.util.Log;

import id.co.bri.brimo.contract.IPresenter.pulsa.IFormPulsaPaketPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.base.IBaseFormNosavedView;
import id.co.bri.brimo.contract.IView.pulsa.IFormPulsaPaketView;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.apimodel.request.FastInquiryPulsaRequest;
import id.co.bri.brimo.models.apimodel.request.InquiryPulsaPaketRequest;
import id.co.bri.brimo.models.apimodel.response.GeneralInquiryResponse;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.presenters.base.BaseFormNosavedPresenter;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.schedulers.Schedulers;

public class FormPulsaPaketPresenter<V extends IMvpView & IBaseFormNosavedView & IFormPulsaPaketView> extends BaseFormNosavedPresenter<V> implements IFormPulsaPaketPresenter<V> {

    private static final String TAG = "FormPulsaPaketPresenter";

    public FormPulsaPaketPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, categoryPfmSource, transaksiPfmSource, anggaranPfmSource);
    }

    /**
     * @param phoneNumber
     * @param providerId
     * @param amount
     * @param fromFastMenu
     */
    @Override
    public void getDataInquiryPulsa(String phoneNumber, String providerId, String amount, boolean fromFastMenu) {
        if (inquiryUrl == null || !isViewAttached()) {
            Log.d(TAG, "FormPulsaPaketPresenter: view atau inquiry urlInformasi null");
            return;
        }

        getView().showProgress();

        if (fromFastMenu)
            inquiryRequest = new FastInquiryPulsaRequest(getFastMenuRequest(), phoneNumber, providerId, amount);
        else
            inquiryRequest = new InquiryPulsaPaketRequest(phoneNumber, providerId, amount);
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        getCompositeDisposable()
                .add(getApiSource().getData(inquiryUrl, inquiryRequest, seqNum)
                        .subscribeOn(Schedulers.io())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(new ApiObserver(getView(),seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                //TO-DO onSuccess
                                getView().hideProgress();
                                GeneralInquiryResponse responsebriva = response.getData(GeneralInquiryResponse.class);

                                if (inquiryUrl != null && konfirmasiUrl != null)
                                    getView().onSuccessGetInquiry(responsebriva, konfirmasiUrl, paymentUrl, fromFastMenu);
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                    getView().onSessionEnd(restResponse.getDesc());
                                else
                                    getView().onException(restResponse.getDesc());
                            }
                        }));
    }


    /**
     * method untuk memproses restResponse jika berhasil mendapatkan balikan dari getDataInquiryPulsa()
     */
    @Override
    public void onApiSuccess(RestResponse response) {
        super.onApiSuccess(response);
        if (!isViewAttached())
            return;
        getView().onSuccessGetRestResponse(response);
    }
}