package id.co.bri.brimo.presenters.dplkrevamp

import id.co.bri.brimo.contract.IPresenter.dplkrevamp.IFormPilihBrifineRevampPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.dplkrevamp.IFormPilihBrifineRevampView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimo.data.repository.category.CategoryPfmSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource

import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.dplkrevamp.ConfirmationRegisDplkRequest
import id.co.bri.brimo.models.apimodel.response.GeneralConfirmationResponse
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.disposables.Disposable

class FormPilihBrifineRevampPresenter<V>(schedulerProvider: SchedulerProvider,
                                         compositeDisposable: CompositeDisposable,
                                         mBRImoPrefRepository: BRImoPrefSource, apiSource: ApiSource,
                                         categoryPfmSource: CategoryPfmSource,
                                         transaksiPfmSource: TransaksiPfmSource,
                                         anggaranPfmSource: AnggaranPfmSource) : MvpPresenter<V>(
        schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource),
        IFormPilihBrifineRevampPresenter<V> where V : IMvpView, V : IFormPilihBrifineRevampView  {

    private var mUrlConfirmation : String = ""
    override fun setUrlConfirm(urlConfirmation: String) {
        mUrlConfirmation= urlConfirmation
    }

    override fun getDataConfirmation(confirmationRequest: ConfirmationRegisDplkRequest) {
        if (mUrlConfirmation.isEmpty() || !isViewAttached) return

        view.showProgress()

        val seqNum = brImoPrefRepository.seqNumber
        val disposable: Disposable =
                apiSource.getData(mUrlConfirmation, confirmationRequest, seqNum) //function(param)
                        .subscribeOn(schedulerProvider.io())
                        .observeOn(schedulerProvider.mainThread())
                        .subscribeWith(object : ApiObserver(view, seqNum) {
                            override fun onFailureHttp(errorMessage: String) {
                                getView().hideProgress()
                                getView().onException(errorMessage)
                            }

                            override fun onApiCallSuccess(response: RestResponse) {
                                getView().hideProgress()
                                val generalConfirmationResponse = response.getData(
                                        GeneralConfirmationResponse::class.java
                                )
                                getView().onGetDataConfirmation(generalConfirmationResponse)
                            }

                            override fun onApiCallError(restResponse: RestResponse) {
                                getView().hideProgress()
                                if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value)) {
                                    getView().onSessionEnd(restResponse.desc)
                                } else if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.value)) {
                                    getView().onExceptionTrxExpired(restResponse.desc)
                                } else {
                                    getView().onException(restResponse.desc)
                                }
                            }
                        })
        compositeDisposable.add(disposable)
    }

    override fun getDefaultSaldo() {
        var saldo = 0.0
        val saldoText = brImoPrefRepository.saldoRekeningUtama
        if (saldoText != "") {
            saldo = java.lang.Double.valueOf(saldoText)
        }
        val defaultAcc = brImoPrefRepository.accountDefault
        val saldoString = brImoPrefRepository.saldoRekeningUtamaString
        val saldoHold = brImoPrefRepository.saldoHold

        view.setDefaultSaldo(saldo, saldoString, defaultAcc, saldoHold)
    }
}