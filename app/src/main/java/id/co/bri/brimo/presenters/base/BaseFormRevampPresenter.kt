package id.co.bri.brimo.presenters.base

import id.co.bri.brimo.contract.IPresenter.base.IBaseFormRevampPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.base.IBaseFormRevampView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.config.AppConfig
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.response.BaseFormResponse
import id.co.bri.brimo.models.apimodel.response.EmptyStateResponse
import id.co.bri.brimo.models.apimodel.response.GeneralFormResponse
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.disposables.CompositeDisposable
import java.util.concurrent.TimeUnit

abstract class BaseFormRevampPresenter<V>(
        schedulerProvider: SchedulerProvider,
        compositeDisposable: CompositeDisposable,
        mBRImoPrefRepository: BRImoPrefSource,
        apiSource: ApiSource,
        transaksiPfmSource: TransaksiPfmSource
) : MvpPresenter<V>(
    schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource
),
        IBaseFormRevampPresenter<V> where V : IMvpView, V : IBaseFormRevampView {

    lateinit var mUrlForm: String
    lateinit var mUrlInquiry: String
    lateinit var mUrlConfirm: String
    lateinit var mUrlPayment: String

    private lateinit var baseFormResponse: BaseFormResponse
    private var isFirstTime = true

    override fun setUrlForm(urlForm: String) {
        mUrlForm = urlForm
    }

    override fun setUrlInquiry(urlInquiry: String) {
        mUrlInquiry = urlInquiry
    }

    override fun setUrlConfirm(urlConfirm: String) {
        mUrlConfirm = urlConfirm
    }

    override fun setUrlPayment(urlPayment: String) {
        mUrlPayment = urlPayment
    }

     override fun getUrlInquiry(): String = if (this::mUrlInquiry.isInitialized) mUrlInquiry.ifEmpty { "" } else ""

     override fun getUrlConfirm(): String = if (this::mUrlConfirm.isInitialized) mUrlConfirm.ifEmpty { "" } else ""

     override fun getUrlPayment(): String = if (this::mUrlPayment.isInitialized) mUrlPayment.ifEmpty { "" } else ""

    override fun getDataForm() {
        if (mUrlForm.isEmpty() || !isViewAttached) return

        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable.add(
                apiSource.getDataForm(mUrlForm, seqNum)
                        .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                        .subscribeOn(schedulerProvider.io())
                        .observeOn(schedulerProvider.mainThread())
                        .subscribeWith(object : ApiObserver(view, seqNum) {
                            override fun onFailureHttp(errorMessage: String) {
                                getView()?.onException(errorMessage)
                            }

                            override fun onApiCallSuccess(response: RestResponse) {
                                if (response.data != null) onApiSuccess(response)
                            }

                            override fun onApiCallError(restResponse: RestResponse) {
                                if (restResponse.code == RestResponse.ResponseCodeEnum.RC_FO.value) {
                                    val response = restResponse.getData(EmptyStateResponse::class.java)
                                    view.onExceptionFO(response)
                                } else {
                                    onApiError(restResponse)
                                }
                            }
                        })
        )
    }

    override fun getDataFormFastMenu() {
        if (mUrlForm.isEmpty() || !isViewAttached) return

        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable.add(
                apiSource.getData(mUrlForm, getFastMenuRequest(), seqNum)
                        .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                        .subscribeOn(schedulerProvider.single())
                        .observeOn(schedulerProvider.mainThread())
                        .subscribeWith(object : ApiObserver(view, seqNum) {
                            override fun onFailureHttp(errorMessage: String) {
                                getView()?.onException(errorMessage)
                            }

                            override fun onApiCallSuccess(response: RestResponse) {
                                if (response.data != null) onApiSuccess(response)
                            }

                            override fun onApiCallError(restResponse: RestResponse) {
                                onApiError(restResponse)
                            }
                        })
        )
    }

    override fun onApiSuccess(response: RestResponse) {
        super.onApiSuccess(response)

        if (!isViewAttached) return

        setBaseFormResponse(response.getData(BaseFormResponse::class.java))

        view.hideSkeleton(true)
        view.onSuccessGetHistoryForm(getBaseFormResponse().history)
        view.onSuccessGetRestResponse(response)
        val generalFormResponse = response.getData(
                GeneralFormResponse::class.java
        )
        view.onSuccessGetSavedForm(generalFormResponse.saved)
        if (isFirstTime){
            view.checkDataHistorySavedList()
            isFirstTime = false
        }
        if (getBaseFormResponse().history.isEmpty() && generalFormResponse.saved.isEmpty()) {
            view.onHistorySavedEmpty()
        }
    }

    private fun getBaseFormResponse(): BaseFormResponse {
        return baseFormResponse
    }

    private fun setBaseFormResponse(baseFormResponse: BaseFormResponse) {
        this.baseFormResponse = baseFormResponse
    }


}