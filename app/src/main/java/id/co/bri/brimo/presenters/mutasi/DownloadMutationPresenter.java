package id.co.bri.brimo.presenters.mutasi;

import java.util.concurrent.TimeUnit;

import id.co.bri.brimo.contract.IPresenter.mutasi.IDownloadMutationPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.mutasi.IDownloadMutationView;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.config.AppConfig;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.apimodel.request.PinRequest;
import id.co.bri.brimo.models.apimodel.response.EmptyStateResponse;
import id.co.bri.brimo.models.apimodel.response.FormRequestEStatementResponse;
import id.co.bri.brimo.models.apimodel.response.ListEStatementResponse;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.presenters.MvpPresenter;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.schedulers.Schedulers;

public class DownloadMutationPresenter<V extends IMvpView & IDownloadMutationView> extends MvpPresenter<V> implements IDownloadMutationPresenter<V> {

    protected String urlGetList, urlFormRequest, urlEditEmail;

    public DownloadMutationPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void setUrlForGetList(String url) {
        this.urlGetList = url;
    }

    @Override
    public void getEStatementList() {
        if (!isViewAttached() || urlGetList == null)
            return;

        getView().isHideSkeleton(false);

        String seq = getBRImoPrefRepository().getSeqNumber();

        getCompositeDisposable().add(
                getApiSource().getData(urlGetList, "", seq)//function(param)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(Schedulers.single())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(new ApiObserver(getView(), seq) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().isHideSkeleton(true);
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                getView().isHideSkeleton(true);
                                if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SUCCESS.getValue())) {
                                    ListEStatementResponse listEStatementResponse = response.getData(ListEStatementResponse.class);
                                    getView().onSuccessGetListEStatement(listEStatementResponse);
                                } else if (response.getCode().equalsIgnoreCase("NF")) {
                                    EmptyStateResponse emptyMutationResponse = response.getData(EmptyStateResponse.class);
                                    getView().showEmptyState(emptyMutationResponse);
                                }
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().isHideSkeleton(true);
                                onApiError(restResponse);
                            }
                        })
        );
    }

    @Override
    public void setUrlForGetFormRequest(String url) {
        this.urlFormRequest = url;
    }

    @Override
    public void getFormRequestEStatement() {
        if (!isViewAttached() || urlFormRequest == null)
            return;

        getView().showProgress();

        String seq = getBRImoPrefRepository().getSeqNumber();

        getCompositeDisposable().add(
                getApiSource().getData(urlFormRequest, "", seq)//function(param)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(Schedulers.single())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(new ApiObserver(getView(), seq) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().hideProgress();
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                getView().hideProgress();
                                FormRequestEStatementResponse formRequestEStatementResponse = response.getData(FormRequestEStatementResponse.class);
                                getView().onSuccessGetFormRequestEStatement(formRequestEStatementResponse);
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                onApiError(restResponse);
                            }
                        })
        );
    }

    @Override
    public void setUrlEditEmail(String urlEditEmail) {
        this.urlEditEmail = urlEditEmail;
    }

    @Override
    public void onLoadEditEmail(String pin) {
        if (isViewAttached()) {

            getView().showProgress();

            PinRequest pinRequest = new PinRequest(pin);
            String seqNum = getBRImoPrefRepository().getSeqNumber();

            getCompositeDisposable().add(
                    getApiSource().getData(urlEditEmail, pinRequest, seqNum)
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .subscribeOn(Schedulers.io())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribeWith(new ApiObserver(getView(), seqNum) {

                                @Override
                                protected void onFailureHttp(String errorMessage) {
                                    getView().hideProgress();
                                    getView().onException(errorMessage);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    getView().hideProgress();
                                    getView().onSuccessEditEmail();
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().hideProgress();
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                        getView().onSessionEnd(restResponse.getDesc());
                                    else
                                        getView().onException(restResponse.getDesc());
                                }
                            })
            );
        }
    }

    @Override
    public void onApiError(RestResponse restResponse) {
        getView().hideProgress();

        if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_12.getValue())) {
            getView().onException12(restResponse.getDesc());
        } else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.getValue())) {
            getView().onException93(restResponse.getDesc());
        } else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_99.getValue())) {
            getView().onException99(restResponse.getDesc());
        } else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_02.getValue())) {
            EmptyStateResponse emptyMutationResponse = restResponse.getData(EmptyStateResponse.class);
            getView().showEmptyState(emptyMutationResponse);
        } else if (restResponse.getCode().equalsIgnoreCase("FO")) {
            EmptyStateResponse response = restResponse.getData(EmptyStateResponse.class);
            getView().onExceptionFO(response);
        } else
            super.onApiError(restResponse);
    }
}