package id.co.bri.brimo.presenters.pfmPickCategory;

import id.co.bri.brimo.contract.IPresenter.pickCategory.IPickIncomePresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.pickCategory.IPickIncomeView;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.presenters.MvpPresenter;

import javax.inject.Inject;

import io.reactivex.disposables.CompositeDisposable;

public class PickIncomeFragmentPresenter<V extends IMvpView & IPickIncomeView> extends MvpPresenter<V> implements IPickIncomePresenter<V> {

    private static final String TAG = "PickIncomeFragmentPresenter";
    private CategoryPfmSource mCategoryPfmSource;

    @Inject
    public PickIncomeFragmentPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
        mCategoryPfmSource = categoryPfmSource;
    }


    @Override
    public void start() {
        super.start();
        loadDataIncome();
    }

    @Override
    public void loadDataIncome() {
        //Rx Observer
        getCompositeDisposable().add(mCategoryPfmSource.getCategoryIncome()
                .subscribeOn(getSchedulerProvider().io())
                .observeOn(getSchedulerProvider().mainThread())
                .subscribe(categories -> {
                    if (categories != null) {
                        getView().onLoadData(categories);
                    }
                }, throwable -> {
                    getView().onException(throwable.getMessage().toString());
                }));
    }


}


