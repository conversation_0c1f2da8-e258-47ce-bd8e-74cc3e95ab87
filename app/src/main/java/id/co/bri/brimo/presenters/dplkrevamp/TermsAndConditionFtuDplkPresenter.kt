package id.co.bri.brimo.presenters.dplkrevamp

import id.co.bri.brimo.contract.IPresenter.dplkrevamp.ITermsAndConditionFtuDplkPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.dplkrevamp.ITermsAndConditionFtuDplkView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.config.AppConfig
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.dplkrevamp.PaymentFtuDplkRequest
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.dplkrevamp.PaymentFtuDplkResponse
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.disposables.CompositeDisposable
import java.util.concurrent.TimeUnit

class TermsAndConditionFtuDplkPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBRImoPrefRepository: BRImoPrefSource,
    apiSource: ApiSource,
    transaksiPfmSource: TransaksiPfmSource,
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    mBRImoPrefRepository,
    apiSource,
    transaksiPfmSource,
), ITermsAndConditionFtuDplkPresenter<V> where V : IMvpView, V : ITermsAndConditionFtuDplkView {

    private var mUrlPayment = ""
    override fun setUrlPaymentFtuDplk(url: String) {
        mUrlPayment = url
    }

    override fun getDataUrlPaymentFtuDplk(request: PaymentFtuDplkRequest) {
        if (isViewAttached) {
            view?.showProgress()

            val seqNum = brImoPrefRepository.seqNumber
            val listConnectableObservable =
                apiSource.getData(mUrlPayment, request, seqNum).subscribeOn(
                    schedulerProvider.io()
                ).timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .observeOn(schedulerProvider.mainThread())
                    .replay()
            compositeDisposable.add(
                listConnectableObservable
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(schedulerProvider.io())
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(type: String) {
                            getView().hideProgress()
                            getView().onException(type)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            getView().hideProgress()
                            if (response.code == Constant.RE01) {
                                getView().onExceptionTrxFailed(restResponse.desc)
                            } else {
                                val data = response.getData(PaymentFtuDplkResponse::class.java)
                                getView().onSuccessPaymentFtuDplk(data)
                            }
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView().hideProgress()
                            when (restResponse.code) {
                                Constant.RE_TRX_EXPIRED -> {
                                    getView().onExceptionTrxExpired(restResponse.desc)
                                }

                                Constant.RE01 -> {
                                    getView().onExceptionTrxFailed(restResponse.desc)
                                }

                                else -> {
                                    getView().onException(restResponse.desc)
                                }
                            }
                        }
                    })
            )
            listConnectableObservable.connect()
        }
    }
}