package id.co.bri.brimo.presenters.onboardingrevamp

import android.content.Context
import android.os.Build
import android.util.Base64
import android.util.Log
import id.co.bri.brimo.contract.IPresenter.newskinonboarding.IVerifyEKYCPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.newskinonboarding.IVerifyEKYCView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.MinioSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.api.observer.MinioApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.config.AppConfig
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.newonboardingrevamp.InitializeZolozRequest
import id.co.bri.brimo.models.apimodel.request.newonboardingrevamp.SendKycRequest
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.newskinonboarding.CheckStatusKycRes
import id.co.bri.brimo.models.apimodel.response.newskinonboarding.InitializeZolozResponse
import id.co.bri.brimo.models.apimodel.response.newskinonboarding.PresignUrlLivenessResponse
import id.co.bri.brimo.presenters.MvpPresenter
//import id.vida.liveness.dto.VidaLivenessResponse
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.Schedulers
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody.Companion.asRequestBody
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.util.concurrent.TimeUnit

class VerifyEKYCPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    private val mBRImoPrefRepository: BRImoPrefSource,
    apiSource: ApiSource,
    private val minioSource: MinioSource,
    transaksiPfmSource: TransaksiPfmSource
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    mBRImoPrefRepository,
    apiSource,
    transaksiPfmSource
), IVerifyEKYCPresenter<V> where V : IMvpView, V : IVerifyEKYCView {

    private var urlPreSigned = ""
    private var urlInitializeZoloz = ""
    private var urlSendKyc = ""
    private var transactionId = ""
    private var image1Success = false
    private var image2Success = false
    private var videoSuccess = false
    private var uploadMinioCompleted = 0

    override fun setUrlPreSigned(url: String) {
        urlPreSigned = url
    }

    override fun setUrlInitializeZoloz(url: String) {
        urlInitializeZoloz = url
    }

    override fun setUrlSendKyc(url: String) {
        urlSendKyc = url
    }

    override fun getPreSigned() {
        if (urlPreSigned.isEmpty() || !isViewAttached) return

        view.showProgress()
        val seqNum = mBRImoPrefRepository.seqNumber

        compositeDisposable.add(
            apiSource.getDataTanpaRequest(urlPreSigned, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(type: String) {
                        view.hideProgress()
//                        getView().onExceptionNewRevamp(type)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        val preSignedResponse =
                            response.getData(PresignUrlLivenessResponse::class.java)
                        getView().onSuccessGetPreSigned(preSignedResponse)
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        view.hideProgress()
                        when (restResponse.code) {
//                            Constant.RE_EXCEED_LIMIT -> {
//                                val limitResponse =
//                                    restResponse.getData(OnboardingErrorResponse::class.java)
//                                getView().onExceedLimit(limitResponse)
//                            }
//
//                            Constant.RE_STATUS_NOT_MATCH -> getView().onStatusNotMatch()
//                            else -> getView().onException(restResponse.desc)
                        }
                    }
                })
        )
    }

    override fun putMinioData(
        transactionId: String,
        image1Url: String,
        image2Url: String,
        videoUrl: String,
        image1: String,
        image2: String,
        video: String,
        context: Context
    ) {
        view.showProgress()
        this.transactionId = transactionId
        uploadMinioCompleted = 0

        if (!image1Success) {
            uploadImageToMinio(
                image1Url,
                Constant.IMAGE_NAME_1,
                image1,
                Constant.LivenessMinioDataType.IMAGE_1,
                context
            )
        } else {
            uploadMinioCompleted++
        }

        if (!image2Success && image2.isNotEmpty()) {
            uploadImageToMinio(
                image2Url,
                Constant.IMAGE_NAME_2,
                image2,
                Constant.LivenessMinioDataType.IMAGE_2,
                context
            )
        } else {
            uploadMinioCompleted++
        }

        if (!videoSuccess && video.isNotEmpty()) {
            uploadVideoToMinio(
                videoUrl,
                Constant.VIDEO_NAME,
                video,
                context
            )
        } else {
            uploadMinioCompleted++
        }
    }

    private fun uploadImageToMinio(
        url: String,
        imageName: String,
        imageData: String,
        type: Constant.LivenessMinioDataType,
        context: Context
    ) {
        sendDataToMinio(url, imageName, imageData, Constant.IMAGE_TYPE, type, context)
    }

    private fun uploadVideoToMinio(
        url: String,
        videoName: String,
        videoData: String,
        context: Context
    ) {
        sendDataToMinio(
            url,
            videoName,
            videoData,
            Constant.VIDEO_TYPE,
            Constant.LivenessMinioDataType.VIDEO,
            context
        )
    }

    private fun sendDataToMinio(
        url: String,
        name: String,
        result: String,
        contentType: String,
        type: Constant.LivenessMinioDataType,
        context: Context
    ) {
        val savedFile: File = createTempFile(name, context)
        decodeAndSaveData(result, savedFile)
        uploadPutMinioData(url, savedFile.path, contentType, type)
    }

    private fun createTempFile(fileName: String, context: Context): File {
        val tempDir = File(context.cacheDir, Constant.TAG_START_NAME)
        if (!tempDir.exists()) {
            tempDir.mkdirs()
        }

        return File(tempDir, fileName)
    }

    private fun decodeAndSaveData(result: String, savedFile: File) {
        val decodedBytes: ByteArray = Base64.decode(result, Base64.DEFAULT)

        try {
            FileOutputStream(savedFile.path).use { outputStream ->
                outputStream.write(decodedBytes)
                outputStream.flush()
            }
        } catch (e: IOException) {
            if (!GeneralHelper.isProd()) {
                Log.d("TestDecode", "decodeBit64: $e")
            }
        }
    }

    private fun uploadPutMinioData(
        url: String,
        path: String,
        contentType: String,
        type: Constant.LivenessMinioDataType
    ) {
        val file = File(path)
        val requestImageFile =
            file.asRequestBody(contentType.toMediaTypeOrNull())
        val disposable = minioSource.putMinioData(url, requestImageFile)
            .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribeWith(object : MinioApiObserver() {
                override fun onFailureHttp(errorMessage: String) {
                    view.hideProgress()
                }

                override fun onApiCallSuccess() {
                    updateSuccessFlags(true, type)
                    checkUploadStatus()
                }

                override fun onApiCallError(errorMessage: String, responseCode: Int) {
                    if (responseCode == 403) {
                        updateSuccessFlags(false, type)
                        checkUploadStatus()
                    } else {
                        view.onExceptionRevamp(errorMessage)
                    }
                }
            })

        compositeDisposable.add(disposable)
    }

    private fun updateSuccessFlags(isSuccess: Boolean, type: Constant.LivenessMinioDataType) {
        when (type) {
            Constant.LivenessMinioDataType.IMAGE_1 -> image1Success = isSuccess
            Constant.LivenessMinioDataType.IMAGE_2 -> image2Success = isSuccess
            Constant.LivenessMinioDataType.VIDEO -> videoSuccess = isSuccess
        }
        uploadMinioCompleted++
    }

    private fun checkUploadStatus() {
        if (uploadMinioCompleted == 3) {
            view.onSuccessUploadMinio()
        }
    }

    /* override fun checkAdditionalImages(
         context: Context,
         image1Url: String,
         image2Url: String,
         videoUrl: String,
         vidaResponse: VidaLivenessResponse
     ) {
         val additionalImages = vidaResponse.additionalImages
         var image1 = encodeToBase64(vidaResponse.imageBytes!!)
         var image2 = ""
         if (!additionalImages.isNullOrEmpty()) {
             additionalImages.forEach { image ->
                 image2 = encodeToBase64(image)
             }
         }

         putMinioData(
             vidaResponse.transactionId!!,
             image1Url,
             image2Url,
             "",
             image1,
             image2,
             "",
             context
         )
     }*/

    private fun encodeToBase64(data: ByteArray): String {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            java.util.Base64.getEncoder().encodeToString(data)
        } else {
            Base64.encodeToString(data, Base64.DEFAULT)
        }
    }

    override fun getInitializeZoloz(metaInfo: String) {
        if (urlInitializeZoloz.isEmpty() || !isViewAttached) return

        val request = InitializeZolozRequest(metaInfo)
        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable.add(
            apiSource.getData(urlInitializeZoloz, request, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView().hideProgress()
//                        getView().onExceptionNewRevamp(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        view.hideProgress()
                        val initializeZolozResponse =
                            response.getData(InitializeZolozResponse::class.java)
                        transactionId = initializeZolozResponse.transactionId.toString()
                        getView().onSuccessStartZoloz(initializeZolozResponse)
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        view.hideProgress()
                        view.onException(restResponse.desc)
                    }
                })
        )
    }

    override fun sendKyc(partnerId: String, result: String) {
        if (urlSendKyc.isEmpty() || !isViewAttached) return

        view.showProgress()

        image1Success = false
        image2Success = false
        videoSuccess = false
        uploadMinioCompleted = 0

        val request = SendKycRequest(
            partnerId,
            transactionId,
            result
        )
        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable.add(
            apiSource.getData(urlSendKyc, request, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView().hideProgress()
//                        getView().onExceptionNewRevamp(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        view.hideProgress()
                        val checkStatusKycResponse =
                            response.getData(CheckStatusKycRes::class.java)
                        getView().onSuccessSendKyc(checkStatusKycResponse)
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        view.hideProgress()
//                        if (restResponse.desc.contains(Constant.RE_ZOLOZ_FAILED))
//                            getView().onExceptionZoloz()
//                        else {
//                            when (restResponse.code) {
//                                Constant.RE_EXCEED_LIMIT -> {
//                                    val limitResponse =
//                                        restResponse.getData(OnboardingErrorResponse::class.java)
//                                    getView().onExceedLimit(limitResponse)
//                                }
//
//                                Constant.RE_STATUS_NOT_MATCH -> getView().onStatusNotMatch()
//                                else -> getView().onException(restResponse.desc)
//                            }
//                        }
                    }
                })
        )
    }
}