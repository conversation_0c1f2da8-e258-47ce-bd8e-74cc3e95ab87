package id.co.bri.brimo.presenters.cc_sof

import id.co.bri.brimo.contract.IPresenter.cc_sof.ISyaratKetentuanCcPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.cc_sof.ISyaratKetentuanCcView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimo.data.repository.category.CategoryPfmSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource

import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.bindingcc.BindingCcRequest
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.bindingcc.CcOtpResponse
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.Schedulers

class SyaratKetentuanCcPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBRImoPrefRepository: BRImoPrefSource,
    apiSource: ApiSource,
    categoryPfmSource: CategoryPfmSource,
    transaksiPfmSource: TransaksiPfmSource,
    anggaranPfmSource: AnggaranPfmSource
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    mBRImoPrefRepository,
    apiSource,
    transaksiPfmSource
), ISyaratKetentuanCcPresenter<V> where V : IMvpView, V : ISyaratKetentuanCcView {

    private lateinit var urlForm: String

    override fun setUrlBindingCC(url: String) {
        urlForm = url
    }

    override fun setRequestBindingCc(bindingCcRequest: BindingCcRequest) {
        if (urlForm.isNotEmpty() && isViewAttached) {
            view.showProgress()

            val seqNum = brImoPrefRepository.seqNumber

            compositeDisposable
                .add(
                    apiSource.getData(urlForm, bindingCcRequest, seqNum)
                        .subscribeOn(Schedulers.io())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(object : ApiObserver(view, seqNum) {
                            override fun onFailureHttp(errorMessage: String) {
                                getView().hideProgress()
                                getView().onException(errorMessage)
                            }

                            override fun onApiCallSuccess(response: RestResponse) {
                                getView().hideProgress()
                                val otpResponse = response.getData(CcOtpResponse::class.java)
                                getView().getDataSuccess(otpResponse)
                            }

                            override fun onApiCallError(restResponse: RestResponse) {
                                getView().hideProgress()
                                if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value)) {
                                    getView().onSessionEnd(restResponse.desc)
                                } else {
                                    getView().onException(restResponse.desc)
                                }
                            }
                        })
                )
        }
    }
}