package id.co.bri.brimo.presenters.finansialrek;

import id.co.bri.brimo.contract.IPresenter.finansialrek.IWaitingFinansialPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.finansialrek.IWaitingFinansialView;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.apimodel.request.CodeRequest;
import id.co.bri.brimo.models.apimodel.request.ResendOtpRequest;
import id.co.bri.brimo.models.apimodel.response.RefnumResponse;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.models.apimodel.response.WaitingFinansialResponse;
import id.co.bri.brimo.presenters.MvpPresenter;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.disposables.Disposable;
import io.reactivex.schedulers.Schedulers;

public class WaitingFinansialPresenter<V extends IMvpView & IWaitingFinansialView>
        extends MvpPresenter<V> implements IWaitingFinansialPresenter<V> {

    protected String urlSend;
    protected String urlResend;

    public WaitingFinansialPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable,
                                     BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource,
                                     TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }


    @Override
    public void setUrlSend(String urlSend) {
        this.urlSend = urlSend;
    }

    @Override
    public void setUrlResend(String urlResend) {
        this.urlResend = urlResend;
    }

    @Override
    public void onConfirmWaiting(String code) {
        if (getView() != null) {

            getView().showProgress();
            String seq = getBRImoPrefRepository().getSeqNumber();
            CodeRequest codeRequest = new CodeRequest(code);

            Disposable disposable = getApiSource().getData(urlSend, codeRequest, seq)
                    .subscribeOn(getSchedulerProvider().io())
                    .observeOn(getSchedulerProvider().mainThread())
                    .subscribeWith(new ApiObserver(getView(), seq) {

                        @Override
                        protected void onFailureHttp(String errorMessage) {
                            getView().hideProgress();
                            getView().onException(errorMessage);
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            getView().hideProgress();
                            RefnumResponse refnumResponse = response.getData(RefnumResponse.class);
                            getView().onSuccess(refnumResponse);
                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            getView().hideProgress();
                            getView().onException12(restResponse.getDesc());
                        }
                    });

            getCompositeDisposable().add(disposable);
        }
    }

    @Override
    public void onResendWaiting(ResendOtpRequest resendOtpRequest) {
        if (getView() != null) {
            getView().showProgress();
            getView().setDisableClick(true);

            String seqNum = getBRImoPrefRepository().getSeqNumber();
            Disposable disposable = getApiSource().getData(urlResend, resendOtpRequest, seqNum)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(new ApiObserver(getView(), seqNum) {

                        @Override
                        protected void onFailureHttp(String errorMessage) {
                            getView().hideProgress();
                            getView().onException(errorMessage);
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            getView().hideProgress();
                            WaitingFinansialResponse resendResponse = response.getData(WaitingFinansialResponse.class);
                            getView().onResendOtp(resendResponse);
                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            getView().hideProgress();
                            getView().onException12(restResponse.getDesc());
                        }
                    });

            getCompositeDisposable().add(disposable);
        }
    }
}
