package id.co.bri.brimo.presenters.rdnrevamp

import id.co.bri.brimo.contract.IPresenter.rdnrevamp.IOnboardingRdnRevampPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.rdnrevamp.IOnboardingRdnRevampView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimo.data.repository.category.CategoryPfmSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.config.AppConfig
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.OnboardingRDNSBNRequest
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.rdn.RdnOnBoardingResponse
import id.co.bri.brimo.models.apimodel.response.rdn.RdnOnCheckpointResponse
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.Schedulers
import java.util.concurrent.TimeUnit

class OnboardingRdnRevampPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBRImoPrefRepository: BRImoPrefSource,
    apiSource: ApiSource,
    transaksiPfmSource: TransaksiPfmSource
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    mBRImoPrefRepository,
    apiSource,
    transaksiPfmSource
), IOnboardingRdnRevampPresenter<V> where V : IMvpView, V : IOnboardingRdnRevampView {
    private var urlRegisterRdn: String? = null

    override fun setUrlRegisterRdn(urlRegisterRdn: String) {
        this.urlRegisterRdn = urlRegisterRdn
    }

    override fun getDataRegisterRdn() {
        urlRegisterRdn?.let {
            view.showProgress()
            val request = OnboardingRDNSBNRequest(brImoPrefRepository.firstRdn)
            val seqNum = brImoPrefRepository.seqNumber
            compositeDisposable.add(apiSource.getData(it, request, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView().hideProgress()
                        getView().onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        if (response.code.equals(Constant.RE_SUCCESS, ignoreCase = true)) {
                            val rdnOnBoardingResponse = response.getData(RdnOnBoardingResponse::class.java)
                            getView().onSuccessOnBoardingRegisterRdn(rdnOnBoardingResponse)
                            getView().hideProgress()
                        } else if (response.code.equals(Constant.RE01, ignoreCase = true)) {
                            val rdnOnBoardingResponse = response.getData(RdnOnCheckpointResponse::class.java)
                            getView().onSuccessCheckPointRegisterRdn(rdnOnBoardingResponse)
                            getView().hideProgress()
                        }
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView().hideProgress()
                        getView().onException(restResponse.desc)
                    }
                }))
        }
    }

}