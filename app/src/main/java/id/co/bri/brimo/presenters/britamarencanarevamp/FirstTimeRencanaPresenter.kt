package id.co.bri.brimo.presenters.britamarencanarevamp

import id.co.bri.brimo.contract.IPresenter.britamarencanarevamp.IFirstTimeRencanaPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.britamarencanarevamp.IFirstTimeRencanaView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimo.data.repository.category.CategoryPfmSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.UbahPinRequest
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.dashboardrecana.UpdateRekeningRencanaResponse
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.disposables.CompositeDisposable

class FirstTimeRencanaPresenter<V>(schedulerProvider: SchedulerProvider,
                                   compositeDisposable: CompositeDisposable,
                                   mBRImoPrefRepository: BRImoPrefSource, apiSource: ApiSource?,
                                   categoryPfmSource: CategoryPfmSource,
                                   transaksiPfmSource: TransaksiPfmSource,
                                   anggaranPfmSource: AnggaranPfmSource) : MvpPresenter<V>(
    schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource
),
        IFirstTimeRencanaPresenter<V> where V : IMvpView?, V : IFirstTimeRencanaView {

    lateinit var urlString : String

    override fun setUrlUpdateRekening(url: String) {
        urlString = url
    }

    override fun getUpdateRekening(request: UbahPinRequest) {
        if (urlString.isEmpty() || !isViewAttached) {
            return
        }
        view.showProgress()
        val seqNum = brImoPrefRepository.seqNumber
        compositeDisposable
                .add(
                    apiSource.getData(urlString, request, seqNum)
                            .subscribeOn(schedulerProvider.io())
                            .observeOn(schedulerProvider.mainThread())
                            .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(type: String) {
                            getView().onException(type)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            getView().hideProgress()
                            val data = response.getData(
                                    UpdateRekeningRencanaResponse::class.java
                            )

                            if (!data.account.isNullOrEmpty()) {
                                getView().onSuccessUpdate(data)
                            } else {
                                getView().onNoNewAccounts(data)
                            }
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView().hideProgress()
                            if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value, ignoreCase = true)) {
                                getView().onSessionEnd(restResponse.desc)
                            } else {
                                getView().onException(restResponse.desc)
                            }
                        }

                        override fun onComplete() {
                            super.onComplete()
                        }
                    })
            )
    }
}