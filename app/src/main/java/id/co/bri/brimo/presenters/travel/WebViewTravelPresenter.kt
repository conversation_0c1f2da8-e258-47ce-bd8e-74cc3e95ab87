package id.co.bri.brimo.presenters.travel

import id.co.bri.brimo.contract.IPresenter.travel.IWebViewTravelPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.travel.IWebViewTravelView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimo.data.repository.category.CategoryPfmSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.config.AppConfig

import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.InquiryKaiObjectRequest
import id.co.bri.brimo.models.apimodel.request.RevokeSessionRequest
import id.co.bri.brimo.models.apimodel.request.travel.TravelFlightRequest
import id.co.bri.brimo.models.apimodel.request.travel.TravelKcicRequest
import id.co.bri.brimo.models.apimodel.response.*
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.Schedulers
import java.util.concurrent.TimeUnit

class WebViewTravelPresenter<V>(
    schedulerProvider: SchedulerProvider?,
    compositeDisposable: CompositeDisposable?,
    mBRImoPrefRepository: BRImoPrefSource?,
    apiSource: ApiSource?,
    categoryPfmSource: CategoryPfmSource?,
    transaksiPfmSource: TransaksiPfmSource?,
    anggaranPfmSource: AnggaranPfmSource?
) : MvpPresenter<V>(
    schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource
), IWebViewTravelPresenter<V> where V : IMvpView?, V : IWebViewTravelView {

    private lateinit var updateStatusRequest: Any

    private lateinit var revokeRequest: Any

    private lateinit var urlInquiryTrain: String

    private lateinit var urlRevokeSession: String

    private lateinit var urlConfirmKcic: String

    private lateinit var confirmKcicRequest: Any

    private lateinit var urlPaymentKcic: String

    private lateinit var urlConfirmFlight: String

    private lateinit var confirmFlightRequest : Any

    private lateinit var urlPaymentFlight: String

    override fun setUrlInquiryKai(urlInquiry: String) {
        urlInquiryTrain = urlInquiry
    }

    override fun setUrlRevokeSession(urlRevokeSession: String) {
        this.urlRevokeSession = urlRevokeSession
    }

    override fun setUrlConfirmationKcic(urlConfirmationKcic: String) {
        urlConfirmKcic = urlConfirmationKcic
    }

    override fun setUrlPaymentKcic(urlPaymentKcic: String) {
        this.urlPaymentKcic = urlPaymentKcic
    }

    override fun setUrlConfirmationFlight(urlConfirmationFlight: String) {
        this.urlConfirmFlight = urlConfirmationFlight
    }

    override fun setUrlPaymentFlight(urlPaymentFlight: String) {
        this.urlPaymentFlight = urlPaymentFlight
    }

    override fun getInquiryTravelKai(accNumber: String) {
        view!!.showProgress()
        if (urlInquiryTrain.isEmpty() || !isViewAttached) {
            return
        }
        val seqNum = brImoPrefRepository.seqNumber
        updateStatusRequest = InquiryKaiObjectRequest(accNumber, "")
        compositeDisposable.add(
            apiSource.getData(urlInquiryTrain, updateStatusRequest, seqNum) //function(param)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView()!!.hideProgress()
                        getView()!!.onException(errorMessage)
                        onLoad = false
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView()!!.hideProgress()
                        val inquiryTrainResponse = response.getData(
                            InquiryTrainResponse::class.java
                        )
                        getView()!!.onSuccessInquiry(inquiryTrainResponse)
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        onLoad = false
                        getView()!!.hideProgress()
                        if (restResponse.code.equals(
                                RestResponse.ResponseCodeEnum.RC_SESSION_END.value,
                                ignoreCase = true
                            )
                        ) getView()!!.onSessionEnd(restResponse.desc) else if (restResponse.code.equals(
                                RestResponse.ResponseCodeEnum.RC_12.value,
                                ignoreCase = true
                            )
                        ) getView()!!.onException12(restResponse.desc) else getView()!!.onException(
                            restResponse.desc
                        )
                    }
                })
        )
    }

    override fun getRevokeSession(sessionId: String, isInquiry: Boolean) {
        view!!.showProgress()

        if (urlRevokeSession.isEmpty() || !isViewAttached) {
            return
        }

        val seqNum = brImoPrefRepository.seqNumber
        val username = brImoPrefRepository.username
        val tokenKey = brImoPrefRepository.tokenKey

        revokeRequest = RevokeSessionRequest(username, tokenKey, sessionId)

        compositeDisposable.add(
            apiSource.getData(urlRevokeSession, revokeRequest, seqNum) //function(param)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView()!!.hideProgress()
                        getView()!!.onException(errorMessage)
                        onLoad = false
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        onLoad = false
                        getView()!!.hideProgress()
                        getView()!!.onSuccessRevokeSession(isInquiry)
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        onLoad = false
                        getView()!!.hideProgress()
                        getView()!!.onSuccessRevokeSession(isInquiry)
                    }
                })
        )
    }

    override fun getConfirmationTravelKcic(brivaNumber: String, corpCode: String, qtyTicket: String,
                                           expiredDate: String, departureDate: String, invoiceNumber: String) {
        view!!.showProgress()

        if (urlConfirmKcic.isEmpty() || !isViewAttached) {
            return
        }

        val seqNum = brImoPrefRepository.seqNumber

        confirmKcicRequest = TravelKcicRequest(brivaNumber, corpCode, qtyTicket, expiredDate,
            departureDate, invoiceNumber)

        compositeDisposable.add(
            apiSource.getData(urlConfirmKcic, confirmKcicRequest, seqNum) //function(param)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(Schedulers.single())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView()!!.hideProgress()
                        getView()!!.onException(errorMessage)
                        onLoad = false
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView()!!.hideProgress()
                        val inquiryBrivaRevampResponse = response.getData(
                            InquiryBrivaRevampResponse::class.java
                        )
                        getView()!!.onSuccessConfirmationkcic(
                            inquiryBrivaRevampResponse,
                            urlPaymentKcic
                        )
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        onLoad = false
                        getView()!!.hideProgress()
                        if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value, ignoreCase = true))
                            getView()!!.onSessionEnd(restResponse.desc)
                        else if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_12.value, ignoreCase = true))
                            getView()!!.onException12(restResponse.desc)
                        else if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_94.value, ignoreCase = true))
                            getView()!!.onException94(restResponse.desc)
                        else
                            getView()!!.onException(restResponse.desc)
                    }
                })
        )
    }

    override fun getConfirmationTravelFlight(bookingId: String) {
        view!!.showProgress()

        if (urlConfirmKcic.isEmpty() || !isViewAttached) {
            return
        }

        val seqNum = brImoPrefRepository.seqNumber

        confirmFlightRequest = TravelFlightRequest(bookingId)

        compositeDisposable.add(
            apiSource.getData(urlConfirmFlight, confirmFlightRequest, seqNum) //function(param)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(Schedulers.single())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView()!!.hideProgress()
                        getView()!!.onException(errorMessage)
                        onLoad = false
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView()!!.hideProgress()
                        val inquiryBrivaRevampResponse = response.getData(
                            InquiryBrivaRevampResponse::class.java
                        )
                        getView()!!.onSuccessConfirmationFlight(
                            inquiryBrivaRevampResponse,
                            urlPaymentFlight
                        )
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        onLoad = false
                        getView()!!.hideProgress()
                        if (restResponse.code.equals(
                                RestResponse.ResponseCodeEnum.RC_SESSION_END.value,
                                ignoreCase = true
                            )
                        ) getView()!!.onSessionEnd(restResponse.desc) else if (restResponse.code.equals(
                                RestResponse.ResponseCodeEnum.RC_12.value,
                                ignoreCase = true
                            )
                        ) getView()!!.onException12(restResponse.desc) else getView()!!.onException(
                            restResponse.desc
                        )
                    }
                })
        )
    }

}