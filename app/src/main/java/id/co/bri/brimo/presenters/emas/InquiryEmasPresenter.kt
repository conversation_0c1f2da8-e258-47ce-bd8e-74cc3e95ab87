package id.co.bri.brimo.presenters.emas

import id.co.bri.brimo.contract.IPresenter.emas.IInquiryEmasPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.emas.IInquiryEmasView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.api.observer.ApiObserverKonfirmasi
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimo.data.repository.category.CategoryPfmSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.config.AppConfig
import id.co.bri.brimo.domain.config.Constant

import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.emas.ConfirmationOldGoldRequest
import id.co.bri.brimo.models.apimodel.request.emas.ConfirmationOpenEmasRequest
import id.co.bri.brimo.models.apimodel.request.emas.GrafikEmasRequest
import id.co.bri.brimo.models.apimodel.response.GeneralConfirmationResponse
import id.co.bri.brimo.models.apimodel.response.GeneralResponse
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.emas.DetailEmasResponse
import id.co.bri.brimo.models.apimodel.response.emas.GeneralConfirmationChecklistResponse
import id.co.bri.brimo.models.apimodel.response.emas.GrafikEmasResponse
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.disposables.Disposable
import io.reactivex.schedulers.Schedulers
import java.util.concurrent.TimeUnit

class InquiryEmasPresenter<V>(schedulerProvider: SchedulerProvider?,
                              compositeDisposable: CompositeDisposable?,
                              mBRImoPrefRepository: BRImoPrefSource?, apiSource: ApiSource?,
                              categoryPfmSource: CategoryPfmSource?,
                              transaksiPfmSource: TransaksiPfmSource?,
                              anggaranPfmSource: AnggaranPfmSource?) : MvpPresenter<V>(
    schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource
),
        IInquiryEmasPresenter<V> where V : IMvpView?, V : IInquiryEmasView {

    lateinit var urlData : String
    lateinit var urlDetail : String
    lateinit var urlGrafik : String
    lateinit var mUrlOldConfirmation : String

    override fun setUrlInquiry(url: String) {
        this.urlData = url
    }

    override fun setUrlDetailRekEmas(url: String) {
        this.urlDetail = url
    }

    override fun getDataConfirmationOpen(request: ConfirmationOpenEmasRequest) {
        view.showProgress()
        val seqNum = brImoPrefRepository.seqNumber
        val disposable: Disposable =
                apiSource.getData(urlData, request, seqNum) //function(param)
                        .subscribeOn(schedulerProvider.io())
                        .observeOn(schedulerProvider.mainThread())
                        .subscribeWith(object : ApiObserverKonfirmasi(view, seqNum) {
                            override fun onFailureHttp(errorMessage: String) {
                                getView().onException(errorMessage)
                            }

                            override fun onApiCallSuccess(response: RestResponse) {
                                getView().hideProgress()

                                val response = response.getData(
                                        GeneralConfirmationChecklistResponse::class.java
                                )

                                getView().onSuccessGetDataConfirmation(response)

                            }

                            override fun onApiCallError(restResponse: RestResponse) {
                                getView().hideProgress()
                                if (restResponse.code.equals(
                                                RestResponse.ResponseCodeEnum.RC_SESSION_END.value,
                                                ignoreCase = true
                                        )
                                ) getView().onSessionEnd(restResponse.desc) else getView().onException(
                                        restResponse.desc
                                )
                            }
                        })
        compositeDisposable.add(disposable)
    }

    override fun getDataConfirmationBeli(request: ConfirmationOpenEmasRequest) {
        view.showProgress()
        val seqNum = brImoPrefRepository.seqNumber
        val disposable: Disposable =
                apiSource.getData(urlData, request, seqNum) //function(param)
                        .subscribeOn(Schedulers.io())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(object : ApiObserverKonfirmasi(view, seqNum) {
                            override fun onFailureHttp(errorMessage: String) {
                                getView().onException(errorMessage)
                            }

                            override fun onApiCallSuccess(response: RestResponse) {
                                getView().hideProgress()

                                val response = response.getData(
                                        GeneralConfirmationResponse::class.java
                                )

                                getView().onSuccessGetBeliConfirmation(response)

                            }

                            override fun onApiCallError(restResponse: RestResponse) {
                                getView().hideProgress()
                                if (restResponse.code.equals(
                                                RestResponse.ResponseCodeEnum.RC_SESSION_END.value,
                                                ignoreCase = true
                                        )
                                ) {
                                    getView().onSessionEnd(restResponse.desc)
                                } else if(restResponse.code.equals(
                                                RestResponse.ResponseCodeEnum.RC_01.value,
                                                ignoreCase = true
                                        )
                                ){
                                    getView().onException01(restResponse.desc)
                                } else if(restResponse.code.equals(
                                        RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.value,
                                        ignoreCase = true
                                    )
                                ) {
                                    getView().onExceptionTrxExpired(restResponse.desc)
                                } else if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_LIMIT_EXCEEDED.value))
                                    getView().onExceptionLimitExceed(
                                        restResponse.getData(
                                            GeneralResponse::class.java
                                        )
                                    )

                                else getView().onException(
                                        restResponse.desc
                                )
                            }
                        })
        compositeDisposable.add(disposable)
    }

    override fun getDetailRekEmas() {
        if (urlDetail == null || !isViewAttached) {
            return
        }

        view!!.showProgress()
        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable
                .add(
                        apiSource.getDataTanpaRequest(urlDetail, seqNum)
                                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                                .subscribeOn(Schedulers.io())
                                .observeOn(AndroidSchedulers.mainThread())
                                .subscribeWith(object : ApiObserver(view, seqNum) {
                                    override fun onFailureHttp(errorMessage: String) {
                                        getView()!!.hideProgress()
                                        getView()!!.onException(errorMessage)
                                    }

                                    override fun onApiCallSuccess(response: RestResponse) {
                                        //TO-DO onSuccess
                                        getView()!!.hideProgress()
                                        GeneralHelper.responseChuck(response)
                                        val detailEmasResponse = response.getData(DetailEmasResponse::class.java)
                                        getView()!!.onSuccessDetailEmas(detailEmasResponse)
                                    }

                                    override fun onApiCallError(restResponse: RestResponse) {
                                        getView()!!.hideProgress()
                                        if (restResponse.code.equals(
                                                        RestResponse.ResponseCodeEnum.RC_SESSION_END.value,
                                                        ignoreCase = true
                                                )
                                        ) getView()!!.onSessionEnd(restResponse.desc) else getView()!!.onException(
                                                restResponse.desc
                                        )
                                    }
                                })
                )
    }

    override fun setUrlGrafikJual(url: String) {
        urlGrafik = url
    }

    override fun getDataGrafikJual(request : GrafikEmasRequest) {
        view.showProgress()
        val seqNum = brImoPrefRepository.seqNumber
        val disposable: Disposable =
                apiSource.getData(urlGrafik, request, seqNum) //function(param)
                        .subscribeOn(Schedulers.io())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(object : ApiObserverKonfirmasi(view, seqNum) {
                            override fun onFailureHttp(errorMessage: String) {
                                getView().onException(errorMessage)
                            }

                            override fun onApiCallSuccess(response: RestResponse) {
                                getView().hideProgress()

                                val response = response.getData(
                                        GrafikEmasResponse::class.java
                                )

                                getView().onSuccessGrafikEmas(response)

                            }

                            override fun onApiCallError(restResponse: RestResponse) {
                                getView().hideProgress()
                                if (restResponse.code.equals(
                                                RestResponse.ResponseCodeEnum.RC_SESSION_END.value,
                                                ignoreCase = true
                                        )
                                ) getView().onSessionEnd(restResponse.desc) else getView().onException(
                                        restResponse.desc
                                )
                            }
                        })
        compositeDisposable.add(disposable)
    }

    override fun setUrlOldConfirmation(urlOldConfirmation: String) {
        mUrlOldConfirmation = urlOldConfirmation
    }

    override fun getDataConfirmationOldBuy(request: ConfirmationOldGoldRequest) {
        view.showProgress()
        val seqNum = brImoPrefRepository.seqNumber
        val disposable: Disposable =
            apiSource.getData(mUrlOldConfirmation, request, seqNum) //function(param)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(object : ApiObserverKonfirmasi(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView().onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView().hideProgress()

                        val response = response.getData(
                            GeneralConfirmationResponse::class.java
                        )

                        getView().onSuccessGetBeliConfirmation(response)

                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView().hideProgress()
                        if(restResponse.code.equals(
                                Constant.RE01,
                                ignoreCase = true
                            )
                        ){
                            getView().onException01(restResponse.desc)
                        }
                        else if(restResponse.code.equals(
                                Constant.RE93,
                                ignoreCase = true
                            )
                        ){
                            getView().onExceptionTrxExpired(restResponse.desc)
                        } else if (restResponse.code == Constant.RE_LIMIT_EXCEED)
                            getView().onExceptionLimitExceed(
                                restResponse.getData(
                                    GeneralResponse::class.java
                                )
                            )

                        else getView().onException(
                            restResponse.desc
                        )
                    }
                })
        compositeDisposable.add(disposable)
    }


    private fun getDefaultSaldo() {
        var saldo = 0.0
        val saldoText = brImoPrefRepository.saldoRekeningUtama
        if (saldoText != "") {
            saldo = java.lang.Double.valueOf(saldoText)
        }
        val defaultAcc = brImoPrefRepository.accountDefault
        val saldoString = brImoPrefRepository.saldoRekeningUtamaString
        val saldoHold = brImoPrefRepository.saldoHold
        view!!.setDefaultSaldo(saldo, saldoString, defaultAcc,saldoHold)
    }

    override fun start() {
        super.start()
        getDefaultSaldo()
    }
}