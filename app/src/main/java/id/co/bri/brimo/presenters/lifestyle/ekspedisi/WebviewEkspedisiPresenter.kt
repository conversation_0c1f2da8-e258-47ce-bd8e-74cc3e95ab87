package id.co.bri.brimo.presenters.lifestyle.ekspedisi

import id.co.bri.brimo.contract.IPresenter.lifestyle.ekspedisi.IWebviewEkspedisiPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.lifestyle.ekspedisi.IWebviewEkspedisiView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimo.data.repository.category.CategoryPfmSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.config.AppConfig
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.RevokeSessionRequest
import id.co.bri.brimo.models.apimodel.request.lifestyle.LifestyleRequest
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.lifestyle.ekspedisi.KonfirmasiWebviewEkspedisiResponse
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.disposables.CompositeDisposable
import java.util.concurrent.TimeUnit

class WebviewEkspedisiPresenter<V>(
    schedulerProvider: SchedulerProvider?,
    compositeDisposable: CompositeDisposable?,
    mBRImoPrefRepository: BRImoPrefSource?,
    apiSource: ApiSource?,
    categoryPfmSource: CategoryPfmSource?,
    transaksiPfmSource: TransaksiPfmSource?,
    anggaranPfmSource: AnggaranPfmSource?
) :
    MvpPresenter<V>(
        schedulerProvider,
        compositeDisposable,
        mBRImoPrefRepository,
        apiSource,
        transaksiPfmSource
    ),
    IWebviewEkspedisiPresenter<V> where V : IMvpView?, V : IWebviewEkspedisiView? {

    private lateinit var requestEkspedisi : Any

    lateinit var urlRevokeSessionMokirim : String

    lateinit var urlConfirmationMokirim : String

    lateinit var urlPaymentMokirim : String

    override fun setUrlRevokeSession(urlRevokeSession: String) {
        urlRevokeSessionMokirim = urlRevokeSession
    }

    override fun setUrlConfirmationKirimBarang(urlConfirm: String) {
        urlConfirmationMokirim = urlConfirm
    }

    override fun setUrlPaymentKirimBarang(urlPayment: String) {
        urlPaymentMokirim = urlPayment
    }

    override fun getRevokeSession(sessionId: String, isInquiry: Boolean) {
        view!!.showProgress()

        if (urlRevokeSessionMokirim.isEmpty() || !isViewAttached) {
            return
        }

        val seqNum = brImoPrefRepository.seqNumber
        val username = brImoPrefRepository.username
        val tokenKey = brImoPrefRepository.tokenKey

        requestEkspedisi = RevokeSessionRequest(username, tokenKey, sessionId)

        compositeDisposable.add(
            apiSource.getData(urlRevokeSessionMokirim, requestEkspedisi, seqNum) //function(param)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(schedulerProvider.single())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView()!!.hideProgress()
                        getView()!!.onException(errorMessage)
                        onLoad = false
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        onLoad = false
                        getView()!!.hideProgress()
                        getView()!!.onSuccessRevokeSession(isInquiry)
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        onLoad = false
                        getView()!!.hideProgress()
                        getView()!!.onSuccessRevokeSession(isInquiry)
                    }
                })
        )
    }

    override fun getConfirmationKirimBarang(orderId: String, billNumber: String) {
        view!!.showProgress()

        if (urlConfirmationMokirim.isEmpty() || !isViewAttached) {
            return
        }

        val seqNum = brImoPrefRepository.seqNumber

        requestEkspedisi = LifestyleRequest(orderId, billNumber)

        compositeDisposable.add(
            apiSource.getData(urlConfirmationMokirim, requestEkspedisi, seqNum) //function(param)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(schedulerProvider.single())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView()!!.hideProgress()
                        getView()!!.onException(errorMessage)
                        onLoad = false
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView()!!.hideProgress()
                        val konfirmasiWebviewEkspedisiResponse = response.getData(
                            KonfirmasiWebviewEkspedisiResponse::class.java
                        )

                        getView()!!.onSuccessConfirmationEkspedisi(
                            konfirmasiWebviewEkspedisiResponse,
                            urlPaymentMokirim
                        )
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        onLoad = false
                        getView()!!.hideProgress()
                        if (restResponse.code.equals("12", ignoreCase = true))
                            getView()!!.onExceptionMicrosite(restResponse.desc)
                        else
                            getView()!!.onException(restResponse.desc)
                    }
                })
        )
    }

}