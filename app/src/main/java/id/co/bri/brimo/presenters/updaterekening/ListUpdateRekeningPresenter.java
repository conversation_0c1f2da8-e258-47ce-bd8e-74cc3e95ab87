package id.co.bri.brimo.presenters.updaterekening;

import java.util.concurrent.TimeUnit;

import id.co.bri.brimo.contract.IPresenter.updaterekening.IListUpdateRekeningPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.updaterekening.IListUpdateRekeningView;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.config.AppConfig;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.apimodel.request.ListUpdateRekeningRequest;
import id.co.bri.brimo.models.apimodel.response.EmptyRekeningResponse;
import id.co.bri.brimo.models.apimodel.response.ListUpdateRekeningResponse;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.presenters.MvpPresenter;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.schedulers.Schedulers;

public class ListUpdateRekeningPresenter<V extends IMvpView & IListUpdateRekeningView> extends MvpPresenter<V> implements IListUpdateRekeningPresenter<V> {
    public ListUpdateRekeningPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    String cUrl;
    ListUpdateRekeningRequest request;

    @Override
    public void setUrl(String url) {
        cUrl = url;
    }

    @Override
    public void getListRekening(String type, String pin) {
        String seqNum = getBRImoPrefRepository().getSeqNumber();

        if (isViewAttached()) {
            request = new ListUpdateRekeningRequest(type, pin);

            getCompositeDisposable().add(
                    getApiSource().getData(cUrl, request, seqNum)
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .subscribeOn(Schedulers.single())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribeWith(new ApiObserver(getView(), seqNum) {

                                @Override
                                protected void onFailureHttp(String errorMessage) {
                                    getView().onException(errorMessage);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SUCCESS.getValue())) {
                                        ListUpdateRekeningResponse listUpdateRekeningResponse = response.getData(ListUpdateRekeningResponse.class);
                                        getView().getListRekeningSuccess(listUpdateRekeningResponse, true, response.getDesc());
                                    } else if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_02.getValue())) {
                                        ListUpdateRekeningResponse listUpdateRekeningResponse = response.getData(ListUpdateRekeningResponse.class);
                                        getView().getListRekeningSuccess(listUpdateRekeningResponse, false, response.getDesc());
                                    } else if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_01.getValue())) {
                                        EmptyRekeningResponse emptyRekeningResponse = response.getData(EmptyRekeningResponse.class);
                                        getView().listRekeningEmpty(emptyRekeningResponse, type);
                                    }
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().pinError(restResponse.getDesc());
                                }
                            })
            );
        }
    }
}