package id.co.bri.brimo.presenters.dompetdigitalreskin

import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.config.AppConfig
import id.co.bri.brimo.domain.config.enumconfig.JourneyType
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.UpdateSavedRequest
import id.co.bri.brimo.models.apimodel.request.dompetdigitalrevamp.InquiryDompetDigitalRequest
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.SavedResponse
import id.co.bri.brimo.models.apimodel.response.dompetdigitalrevamp.InquiryDompetDigitalResponse
import id.co.bri.brimo.presenters.MvpPresenter
import id.co.bri.brimo.contract.IView.dompetdigitalreskin.ISearchSavedHistoryDompetDigitalView
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.Schedulers
import java.util.concurrent.TimeUnit

class SearchSavedHistoryDompetDigitalPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBRImoPrefRepository: BRImoPrefSource,
    apiSource: ApiSource,
    transaksiPfmSource: TransaksiPfmSource,
) : MvpPresenter<V>(
    schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource
),
    ISearchSavedHistoryDompetDigitalPresenter<V> where V : IMvpView, V : ISearchSavedHistoryDompetDigitalView {
    lateinit var mUrlInquiry: String
    lateinit var mUrlConfirm: String
    lateinit var mUrlPayment: String

    override fun setUrlInquiry(urlInquiry: String) {
        mUrlInquiry = urlInquiry
    }

    override fun setUrlConfirm(urlConfirm: String) {
        mUrlConfirm = urlConfirm
    }

    override fun setUrlPayment(urlPayment: String) {
        mUrlPayment = urlPayment
    }

    override fun setUpdateItem(
        url: String,
        savedResponse: SavedResponse,
        position: Int,
        type: Int,
        journeyType: JourneyType?
    ) {
        if (url.isEmpty() || !isViewAttached || onLoad) {
            return
        }
        view?.showProgress()
        onLoad = true
        val s = savedResponse.value
        val str1 = s.split("\\|".toRegex()).dropLastWhile { it.isEmpty() }
            .toTypedArray()
        val saveId = str1[0]
        val productId = str1[1]
        val updateSavedRequest = UpdateSavedRequest(saveId, productId)
        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable.add(
            apiSource.getData(url, updateSavedRequest, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        onLoad = false
                        getView().onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        onLoad = false
                        getView().hideProgress()
                        getView().onSuccessUpdate(savedResponse, position, type)
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        onLoad = false
                        getView().hideProgress()
                        onApiError(restResponse)
                    }
                })
        )
    }

    override fun getDataInquirySaved(
        isFromFastMenu: Boolean,
        walletCode: String,
        corpCode: String,
        purchaseNumber: String
    ) {
        view?.showProgress()

        val inquiryRequest = if (isFromFastMenu)
            InquiryDompetDigitalRequest(
                brImoPrefRepository.username,
                brImoPrefRepository.tokenKey,
                walletCode,
                corpCode,
                purchaseNumber
            )
        else
            InquiryDompetDigitalRequest(
                walletCode,
                corpCode,
                purchaseNumber
            )

        val seqNum = getBRImoPrefRepository().getSeqNumber()


        getCompositeDisposable()
            .add(
                getApiSource().getData(getUrlInquiry(), inquiryRequest, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(object : ApiObserver(getView(), seqNum) {

                    override fun onFailureHttp(errorMessage: String) {
                        getView().hideProgress()
                        getView().onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView().hideProgress()
                        val inquiryResponse =
                            response.getData(InquiryDompetDigitalResponse::class.java)
                        getView().onSuccessGetInquirySaved(
                            inquiryResponse,
                            getUrlConfirm(),
                            getUrlPayment(),
                            isFromFastMenu,
                            purchaseNumber
                        )
                    }

                    override fun onApiCallError(response: RestResponse) {
                        getView().hideProgress()
                        onApiError(response)
                    }
                })
            )
    }

    override fun getUrlInquiry(): String = if (this::mUrlInquiry.isInitialized) mUrlInquiry.ifEmpty { "" } else ""

    override fun getUrlConfirm(): String = if (this::mUrlConfirm.isInitialized) mUrlConfirm.ifEmpty { "" } else ""

    override fun getUrlPayment(): String = if (this::mUrlPayment.isInitialized) mUrlPayment.ifEmpty { "" } else ""

}
