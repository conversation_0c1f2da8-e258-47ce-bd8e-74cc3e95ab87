package id.co.bri.brimo.presenters.travel;

import android.util.Log;

import java.util.concurrent.TimeUnit;

import id.co.bri.brimo.contract.IPresenter.travel.IKonfirmasiTravelTrainPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.travel.IKonfirmasiTravelTrainView;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.api.observer.ApiObserverKonfirmasi;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.config.AppConfig;
import id.co.bri.brimo.domain.config.DbConfig;
import id.co.bri.brimo.domain.helpers.calendar.CalendarHelper;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.apimodel.request.CancelBookingRequest;
import id.co.bri.brimo.models.apimodel.request.cashback.CashbackRequest;
import id.co.bri.brimo.models.apimodel.request.PaymentRequest;
import id.co.bri.brimo.models.apimodel.response.GeneralConfirmationResponse;
import id.co.bri.brimo.models.apimodel.response.ListAllCashbackFilterResponse;
import id.co.bri.brimo.models.apimodel.response.ReceiptTravelTrainResponse;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.models.daomodel.Transaksi;
import id.co.bri.brimo.presenters.MvpPresenter;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.disposables.Disposable;
import io.reactivex.observers.DisposableSingleObserver;
import io.reactivex.schedulers.Schedulers;

public class KonfirmasiTravelTrainPresenter<V extends IMvpView & IKonfirmasiTravelTrainView> extends MvpPresenter<V> implements IKonfirmasiTravelTrainPresenter<V> {

    private static final String TAG = "GeneralConfirmationPres";

    protected String urlPayment, urlGetCashback;
    protected String urlCancel;
    protected Object paymentRequest;
    protected boolean isGeneral;
    protected boolean isCashback;

    public KonfirmasiTravelTrainPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void getDataPayment(String pin, String note, GeneralConfirmationResponse generalConfirmationResponse, boolean fromFast) {
        if (urlPayment == null) {
            Log.d(TAG, "getDataPayment: url payment null");
            return;
        }

        if (!isViewAttached() || onLoad) {
            Log.d(TAG, "getDataPayment: view null");
            return;
        }

        if (isViewAttached()) {
            //set flag Loading
            onLoad = true;

            getView().showProgress();
            String seqNum = getBRImoPrefRepository().getSeqNumber();
            paymentRequest = new PaymentRequest(generalConfirmationResponse.getReferenceNumber(), pin, generalConfirmationResponse.getPfmCategory().toString(), note);

            Disposable disposable = getApiSource().getData(urlPayment, paymentRequest, seqNum)//function(param)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(new ApiObserverKonfirmasi(getView(), seqNum) {

                        @Override
                        protected void onFailureHttp(String errorMessage) {
                            onLoad = false;
                            getView().onException(errorMessage);
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            onLoad = false;
                            getView().hideProgress();

                            ReceiptTravelTrainResponse receiptTravelResponse = response.getData(ReceiptTravelTrainResponse.class);
                            getView().onSuccessGetPayment(receiptTravelResponse);

                            if (receiptTravelResponse.getImmediatelyFlag())
                                onSaveTransaksiPfm(generateTransaksiModel(
                                        generalConfirmationResponse.getPfmCategory().intValue(),
                                        generalConfirmationResponse.getPayAmount(),
                                        generalConfirmationResponse.getReferenceNumber(),
                                        generalConfirmationResponse.getPfmDescription())
                                );

                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            onLoad = false;
                            getView().hideProgress();
                            if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                getView().onSessionEnd(restResponse.getDesc());
                            else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.getValue()))
                                getView().onException93(restResponse.getDesc());
                            else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_01.getValue()))
                                getView().onException01(restResponse.getDesc());
                            else if (restResponse.getCode().equals(RestResponse.ResponseCodeEnum.RC_99.getValue())) {
                                getView().onException99(restResponse.getDesc());
                            } else
                                getView().onException(restResponse.getDesc());
                        }
                    });

            getCompositeDisposable().add(disposable);
        }

    }

    @Override
    public void setUrlPayment(String urlPayment) {
        this.urlPayment = urlPayment;
    }

    @Override
    public void isGeneral(boolean general) {

    }

    @Override
    public void isCashback(boolean cashback) {
        isCashback = cashback;
    }

    @Override
    public void setUrlCancel(String url) {
        this.urlCancel = url;
    }

    @Override
    public void getCancelBooking(CancelBookingRequest cancelBookingRequest) {
        String seqNum = getBRImoPrefRepository().getSeqNumber();

        getView().showProgress();
        getCompositeDisposable()
                .add(getApiSource().getData(urlCancel, cancelBookingRequest, seqNum)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(Schedulers.io())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(new ApiObserver(getView(), seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().hideProgress();
                                getView().onException(errorMessage);
                                onLoad = false;
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                //TO-DO onSuccess
                                getView().hideProgress();
                                onLoad = false;
//                                InquiryBusResponse inquiryBusResponse = response.getData(InquiryBusResponse.class);
                                getView().onSucccessCancel();
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                onLoad = false;
                                getView().hideProgress();
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                    getView().onSessionEnd(restResponse.getDesc());
                                else
                                    getView().onException(restResponse.getDesc());

                            }
                        }));
    }

    @Override
    public Transaksi generateTransaksiModel(int kategoriId, long amount, String referenceNumber, String billingName) {
        Transaksi transaksi = null;
        try {
            transaksi = new Transaksi(
                    (long) kategoriId,
                    1,
                    billingName,
                    "",
                    DbConfig.TRX_OUT,
                    getBRImoPrefRepository().getUser(),
                    (long) amount,
                    CalendarHelper.getCurrentDate(),
                    CalendarHelper.getCurrentTime(),
                    Long.valueOf(referenceNumber),
                    0
            );
        } catch (Exception e) {
            /*
            if(BuildConfig.DEBUG)
            e.printStackTrace();
             */
        }

        return transaksi;
    }

    @Override
    public void onSaveTransaksiPfm(Transaksi transaksi) {
        if (transaksi != null) {
            getCompositeDisposable().add(getTransaksiPfmSource()
                    .saveTransaksiPfm(transaksi)
                    .subscribeOn(getSchedulerProvider().io())
                    .observeOn(getSchedulerProvider().mainThread())
                    .subscribeWith(new DisposableSingleObserver<Long>() {
                        @Override
                        public void onSuccess(Long aLong) {

                        }

                        @Override
                        public void onError(Throwable e) {
                            Log.d(TAG, "onError: " + e.toString());
                        }
                    })
            );
        }
    }

    @Override
    public void setUrlGetCashback(String url) {
        this.urlGetCashback = url;
    }

    @Override
    public void getCashbackAll(CashbackRequest cashbackRequest) {
        if (urlGetCashback == null || !isViewAttached()) {
            return;
        }
        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        getCompositeDisposable().add(
                getApiSource().getData(urlGetCashback, cashbackRequest, seqNum)
                        .subscribeOn(getSchedulerProvider().single())
                        .observeOn(getSchedulerProvider().mainThread())
                        .subscribeWith(new ApiObserver(getView(), seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                getView().hideProgress();
                                if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SUCCESS.getValue())) {
                                    ListAllCashbackFilterResponse responseData = response.getData(ListAllCashbackFilterResponse.class);
                                    getView().onSuccessGetCashback(responseData);
                                } else if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_01.getValue())) {
                                    getView().onCashbackBlank(response.getDesc());
                                }
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                    getView().onSessionEnd(restResponse.getDesc());
                                else
                                    getView().onException(restResponse.getDesc());
                            }
                        })
        );
    }

    @Override
    public void start() {
        super.start();
        setDisablePopup(true);
    }

    @Override
    public void stop() {
        super.stop();
    }
}