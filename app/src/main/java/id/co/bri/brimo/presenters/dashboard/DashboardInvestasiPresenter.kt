package id.co.bri.brimo.presenters.dashboard

import android.util.Log
import id.co.bri.brimo.R
import id.co.bri.brimo.contract.IPresenter.dashboard.IDashboardInvestasiPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.dashboard.IDashboardInvestasiView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimo.data.repository.category.CategoryPfmSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.config.AppConfig

import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.OnboardingRDNSBNRequest
import id.co.bri.brimo.models.apimodel.request.rdn.InquiryRdnRequest
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.TermConditionTabRes
import id.co.bri.brimo.models.apimodel.response.dashboardInvestasi.*
import id.co.bri.brimo.models.apimodel.response.dplk.DplkBoardingResponse
import id.co.bri.brimo.models.apimodel.response.rdn.*
import id.co.bri.brimo.models.apimodel.response.rdn.RdnAccountResponse.AccountRdn
import id.co.bri.brimo.models.apimodel.response.sbnrevamp.SbnBeliResponse
import id.co.bri.brimo.models.apimodel.response.sbnrevamp.SbnDashboardHeaderResponse
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.Schedulers
import java.util.concurrent.TimeUnit

class DashboardInvestasiPresenter<V>(
        schedulerProvider: SchedulerProvider,
        compositeDisposable: CompositeDisposable,
        mBRImoPrefRepository: BRImoPrefSource,
        apiSource: ApiSource,
        categoryPfmSource: CategoryPfmSource,
        transaksiPfmSource: TransaksiPfmSource,
        anggaranPfmSource: AnggaranPfmSource
) : MvpPresenter<V>(
    schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource
),
        IDashboardInvestasiPresenter<V> where V : IMvpView, V : IDashboardInvestasiView {

    lateinit var mUrl :String
    lateinit var mUrlFaq :String
    lateinit var mUrlPromo :String
    lateinit var mUrlContent :String
    lateinit var mUrlRecommendation :String
    lateinit var mUrlMenu :String
    lateinit var mUrlBukaRdn : String
    lateinit var mUrlRegisDeposito : String
    lateinit var mUrlRegisDplk : String
    lateinit var mUrlForced :String
    lateinit var mUrlAccountRdn :String
    lateinit var mUrlInquiryRdn : String
    var urlValidateUser = ""
    var mUrlKemenkeu = ""
    var urlHeader = ""
    var urlBeli = ""
    var mUrlInvesmentRecap = ""
    var listRdnAccount: MutableList<AccountRdn> = mutableListOf()

    override fun setUrlInvestasi(url: String) {
        mUrl = url
    }

    override fun setUrlInvestasiForced(url: String) {
        mUrlForced = url
    }

    override fun setUrlInvestasiFaq(url: String) {
        mUrlFaq = url
    }

    override fun setUrlInvestasiPromo(url: String) {
        mUrlPromo = url
    }

    override fun setUrlInvestasiContent(url: String) {
        mUrlContent = url
    }

    override fun setUrlInvestasiRecomendation(url: String) {
        mUrlRecommendation = url
    }

    override fun setUrlInvestasiMenu(url: String) {
        mUrlMenu = url
    }

    override fun setUrlBukaRdn(url: String) {
       mUrlBukaRdn = url
    }

    override fun setUrlDepositoRegis(url: String) {
        mUrlRegisDeposito = url
    }

    override fun seturlDplkRegis(url: String) {
        mUrlRegisDplk = url
    }

    override fun setUrlGetListAccountRdn(url: String) {
        mUrlAccountRdn = url
    }

    override fun setUrlInquiryRdn(url: String) {
        mUrlInquiryRdn = url
    }

    override fun setUrlValidateUsers(urlValidate: String) {
        urlValidateUser= urlValidate
    }

    override fun setUrlKemenkeu(urlKemenkeu: String) {
        mUrlKemenkeu = urlKemenkeu
    }

    override fun setUrlSbnDashboardHeader(urlDashboardHeader: String) {
        this.urlHeader = urlDashboardHeader
    }

    override fun setUrlBeliSbn(urlBeliSbn: String) {
        urlBeli = urlBeliSbn
    }

    override fun setUrlInvesmentRecap(urlInvesmentRecap: String) {
        mUrlInvesmentRecap = urlInvesmentRecap
    }

    override fun getDataInvestasiFaq() {
        if (mUrlFaq.isEmpty() && !isViewAttached) return

        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable
                .add(
                        apiSource.getDataTanpaRequest(mUrlFaq, seqNum)
                                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                                .subscribeOn(schedulerProvider.io())
                                .observeOn(schedulerProvider.mainThread())
                                .subscribeWith(object : ApiObserver(view, seqNum) {
                                    override fun onFailureHttp(errorMessage: String) {
                                        getView().onException(errorMessage)
                                    }

                                    override fun onApiCallSuccess(response: RestResponse) {
                                        //TO-DO onSuccess
                                        val mResponse = response.getData(
                                                InvenstasiFaqResponse::class.java
                                        )
                                        getView().onSuccessInvestasiFaq(mResponse)
                                    }

                                    override fun onApiCallError(restResponse: RestResponse) {
                                        if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value))
                                            getView().onSessionEnd(restResponse.desc)
                                        else if(restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_REFRESH.value)){
                                            getView().onException21Faq()
                                        }
                                        else if(restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_12.value)){
                                            getView().onException12(restResponse.desc)
                                        }
                                        else getView().onException(restResponse.desc)
                                    }
                                })
                )
    }

    override fun getDataInvestasiPromo() {
        if (mUrlPromo.isEmpty() && !isViewAttached) return

        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable
                .add(
                        apiSource.getDataTanpaRequest(mUrlPromo, seqNum)
                                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                                .subscribeOn(schedulerProvider.io())
                                .observeOn(schedulerProvider.mainThread())
                                .subscribeWith(object : ApiObserver(view, seqNum) {
                                    override fun onFailureHttp(errorMessage: String) {
                                        getView().onException(errorMessage)
                                    }

                                    override fun onApiCallSuccess(response: RestResponse) {
                                        //TO-DO onSuccess
                                        val mResponse = response.getData(
                                                BannerResponse::class.java
                                        )
                                        getView().onSuccessInvestasiPromo(mResponse)
                                    }

                                    override fun onApiCallError(restResponse: RestResponse) {
                                        if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value))
                                            getView().onSessionEnd(restResponse.desc)
                                        else if(restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_REFRESH.value)){
                                            getView().onException21Promo()
                                        }
                                        else if(restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_12.value)){
                                            getView().onException12(restResponse.desc)
                                        }
                                        else getView().onException(restResponse.desc)
                                    }
                                })
                )
    }

    override fun getDataInvestasiContent() {
        if (mUrlContent.isEmpty() && !isViewAttached) return

        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable
                .add(
                        apiSource.getDataTanpaRequest(mUrlContent, seqNum)
                                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                                .subscribeOn(schedulerProvider.io())
                                .observeOn(schedulerProvider.mainThread())
                                .subscribeWith(object : ApiObserver(view, seqNum) {
                                    override fun onFailureHttp(errorMessage: String) {
                                        getView().onException(errorMessage)
                                    }

                                    override fun onApiCallSuccess(response: RestResponse) {
                                        //TO-DO onSuccess
                                        val mResponse = response.getData(
                                                ContentResponse::class.java
                                        )
                                        getView().onSuccessInvestasiContent(mResponse)
                                    }

                                    override fun onApiCallError(restResponse: RestResponse) {
                                        if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value))
                                            getView().onSessionEnd(restResponse.desc)
                                        else if(restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_REFRESH.value)){
                                            getView().onException21Content()
                                        }
                                        else if(restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_12.value)){
                                            getView().onException12(restResponse.desc)
                                        }
                                        else getView().onException(restResponse.desc)
                                    }
                                })
                )
    }

    override fun getDataInvestasiRecommedantion() {
        if (mUrlRecommendation.isEmpty() && !isViewAttached) return

        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable
                .add(
                        apiSource.getDataTanpaRequest(mUrlRecommendation, seqNum)
                                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                                .subscribeOn(schedulerProvider.io())
                                .observeOn(schedulerProvider.mainThread())
                                .subscribeWith(object : ApiObserver(view, seqNum) {
                                    override fun onFailureHttp(errorMessage: String) {
                                        getView().onException(errorMessage)
                                    }

                                    override fun onApiCallSuccess(response: RestResponse) {
                                        //TO-DO onSuccess
                                        val mResponse = response.getData(
                                                RecomendationResponse::class.java
                                        )
                                        getView().onSuccessInvestasiRecommendation(mResponse)
                                    }

                                    override fun onApiCallError(restResponse: RestResponse) {
                                        if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value))
                                            getView().onSessionEnd(restResponse.desc)
                                        else if(restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_REFRESH.value)){
                                            getView().onException21Recommedetion()
                                        }
                                        else if(restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_12.value)){
                                            getView().onException12(restResponse.desc)
                                        }
                                        else getView().onException(restResponse.desc)
                                    }
                                })
                )
    }

    override fun getDataInvestasiMenu() {
        if (mUrlMenu.isEmpty() && !isViewAttached) return

        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable
                .add(
                        apiSource.getDataTanpaRequest(mUrlMenu, seqNum)
                                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                                .subscribeOn(schedulerProvider.io())
                                .observeOn(schedulerProvider.mainThread())
                                .subscribeWith(object : ApiObserver(view, seqNum) {
                                    override fun onFailureHttp(errorMessage: String) {
                                        getView().onException(errorMessage)
                                    }

                                    override fun onApiCallSuccess(response: RestResponse) {
                                        //TO-DO onSuccess
                                        val mResponse = response.getData(
                                                MenuInvestasiResponse::class.java
                                        )
                                        getView().onSuccessInvestasiMenu(mResponse)
                                    }

                                    override fun onApiCallError(restResponse: RestResponse) {
                                        if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value))
                                            getView().onSessionEnd(restResponse.desc)
                                        else getView().onException(restResponse.desc)
                                    }
                                })
                )
    }


    override fun getDataInvestasiAsset() {
        if (mUrl.isEmpty() && !isViewAttached) return

        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable
                .add(
                        apiSource.getDataTanpaRequest(mUrl, seqNum)
                                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                                .subscribeOn(schedulerProvider.io())
                                .observeOn(schedulerProvider.mainThread())
                                .subscribeWith(object : ApiObserver(view, seqNum) {
                                    override fun onFailureHttp(errorMessage: String) {
                                        getView().onException(errorMessage)
                                    }

                                    override fun onApiCallSuccess(response: RestResponse) {
                                        //TO-DO onSuccess
                                        val mResponse = response.getData(
                                                DashboardInvestasiResponse::class.java
                                        )
                                        getView().onSuccessInvestasi(mResponse)
                                    }

                                    override fun onApiCallError(restResponse: RestResponse) {
                                        if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value))
                                            getView().onSessionEnd(restResponse.desc)
                                        else if(restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_REFRESH.value)){
                                            getView().onException21Asset()
                                        }
                                        else if(restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_12.value)){
                                            getView().onException12(restResponse.desc)
                                        }
                                        else getView().onException(restResponse.desc)
                                    }
                                })
                )
    }

    override fun getDataInvestasiAssetForce() {
        if (mUrlForced.isEmpty() && !isViewAttached) return

        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable
                .add(
                        apiSource.getDataTanpaRequest(mUrlForced, seqNum)
                                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                                .subscribeOn(schedulerProvider.io())
                                .observeOn(schedulerProvider.mainThread())
                                .subscribeWith(object : ApiObserver(view, seqNum) {
                                    override fun onFailureHttp(errorMessage: String) {
                                        getView().onException(errorMessage)
                                    }

                                    override fun onApiCallSuccess(response: RestResponse) {
                                        //TO-DO onSuccess
                                        val mResponse = response.getData(
                                                DashboardInvestasiResponse::class.java
                                        )
                                        getView().onSuccessInvestasiForced(mResponse)
                                    }

                                    override fun onApiCallError(restResponse: RestResponse) {
                                        if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value))
                                            getView().onSessionEnd(restResponse.desc)
                                        else if(restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_REFRESH.value)){
                                            getView().onException21Asset()
                                        }
                                        else if(restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_12.value)){
                                            getView().onException12(restResponse.desc)
                                        }
                                        else getView().onException(restResponse.desc)
                                    }
                                })
                )
    }

    override fun onGetOnBoarding() {
        if (isViewAttached) {
            view.showProgress()
            val request = OnboardingRDNSBNRequest(brImoPrefRepository.firstRdn)
            val seqNum = brImoPrefRepository.seqNumber
            compositeDisposable.add(apiSource.getData(mUrlBukaRdn, request, seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView().hideProgress()
                            getView().onException(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            if (response.code.equals(RestResponse.ResponseCodeEnum.RC_SUCCESS.value, ignoreCase = true)) {
                                val rdnOnBoardingResponse = response.getData(RdnOnBoardingResponse::class.java)
                                getView().onSuccessOnBoarding(rdnOnBoardingResponse)
                                getView().hideProgress()
                            } else if (response.code.equals(RestResponse.ResponseCodeEnum.RC_01.value, ignoreCase = true)) {
                                val rdnOnBoardingResponse = response.getData(RdnOnCheckpointResponse::class.java)
                                getView().onSuccessCheckPoint(rdnOnBoardingResponse)
                                getView().hideProgress()
                            }
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView().hideProgress()
                            if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value, ignoreCase = true)) getView().onSessionEnd(restResponse.desc)
                            else getView().onException(restResponse.desc)
                        }
                    }))
        }
    }

    override fun getDepositoRegis() {
        if (isViewAttached) {
            val seqNum = brImoPrefRepository.seqNumber
            view.showProgress()
            compositeDisposable.add(
                    apiSource.getData(mUrlRegisDeposito, "", seqNum)
                            .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                            .subscribeOn(Schedulers.io())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribeWith(object : ApiObserver(view, seqNum) {
                                override fun onFailureHttp(errorMessage: String) {
                                    getView().hideProgress()
                                    getView().onException(errorMessage)
                                }

                                override fun onApiCallSuccess(response: RestResponse) {
                                    //TO-DO onSucces
                                    getView().hideProgress()
                                    if (response.code.equals(RestResponse.ResponseCodeEnum.RC_SUCCESS.value, ignoreCase = true)) {
                                        val termConditionTabRes = response.getData(TermConditionTabRes::class.java)
                                        getView().onGetDataTerm(termConditionTabRes)
                                    } else if (response.code.equals(RestResponse.ResponseCodeEnum.RC_01.value, ignoreCase = true)) {
                                        getView().onGetDataProses()
                                    }
                                }

                                override fun onApiCallError(restResponse: RestResponse) {
                                    getView().hideProgress()
                                    if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value, ignoreCase = true)) getView().onSessionEnd(restResponse.desc)
                                    else if(restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_12.value)){
                                        getView().onException12(restResponse.desc)
                                    }
                                    else getView().onException(restResponse.desc)
                                }
                            })
            )
        }
    }

    override fun getDplkRegis() {
        view.showProgress()
        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable.add(apiSource.getData(mUrlRegisDplk, "", seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView().onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView().hideProgress()
                        val response1 = response.getData(DplkBoardingResponse::class.java)
                        getView().onSuccessDplkRegis(response1)
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView().hideProgress()
                        if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value, ignoreCase = true)) {
                            getView().onSessionEnd(restResponse.desc)
                        }
                        else if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_12.value, ignoreCase = true)) {
                            getView().onException12(restResponse.desc)
                        }
                         else getView().onException(restResponse.desc)
                    }
                }))
    }

    override fun getListAccountRdn() {
        view.showProgress()
        if (isViewAttached) {
            val seqNum = brImoPrefRepository.seqNumber
            compositeDisposable.add(
                    apiSource.getData(mUrlAccountRdn, "", seqNum)
                            .subscribeOn(schedulerProvider.single())
                            .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                            .observeOn(schedulerProvider.mainThread())
                            .subscribeWith(object : ApiObserver(view, seqNum) {
                                override fun onFailureHttp(type: String) {
                                    getView().hideProgress()
                                    getView().onException(type)
                                }

                                override fun onApiCallSuccess(response: RestResponse) {
                                    getView().hideProgress()
                                    if (response.code.equals(RestResponse.ResponseCodeEnum.RC_SUCCESS.value, ignoreCase = true)) {
                                        val rdnAccountResponse = response.getData(RdnAccountResponse::class.java)
                                        if (listRdnAccount.isEmpty()) {
                                            listRdnAccount.addAll(rdnAccountResponse.accountRdn)
                                        } else {
                                            listRdnAccount.clear()
                                            listRdnAccount.addAll(rdnAccountResponse.accountRdn)
                                        }
                                        getView().onSuccessGetAccountRdn(rdnAccountResponse)
                                    } else if (response.code.equals(RestResponse.ResponseCodeEnum.RC_01.value, ignoreCase = true)) {
                                        val rdnDashboardFailureResponse = response.getData(RdnDashboardFailureResponse::class.java)
                                        getView().onException01Rdn(rdnDashboardFailureResponse.title, rdnDashboardFailureResponse.description)
                                    } else if (response.code.equals(RestResponse.ResponseCodeEnum.RC_02.value, ignoreCase = true)) {
                                        getView().onException02Rdn(GeneralHelper.getString(R.string.txt_halaman_gagal_memuat), response.desc)
                                    }
                                }

                                override fun onApiCallError(restResponse: RestResponse) {
//                                    getView().hideProgress()
                                    if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value, ignoreCase = true)) {
                                        getView().onSessionEnd(restResponse.desc)
                                    }
                                    else if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_99.value, ignoreCase = true)) {
                                        getView().onException02Rdn("", restResponse.desc)
                                    } else {
                                        getView().onException(restResponse.desc)
                                    }
                                }
                            })
            )
        }
    }


    override fun getSbnDashboardHeader() {
        if (isViewAttached) {
            view.showProgress()
            val seqNum = brImoPrefRepository.seqNumber
            val listConnectableObservable =
                    apiSource.getDataTanpaRequest(urlHeader, seqNum).subscribeOn(
                            schedulerProvider.io()
                    ).timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                            .observeOn(schedulerProvider.mainThread())
                            .replay()
            compositeDisposable.add(
                    listConnectableObservable
                            .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                            .subscribeOn(Schedulers.io())
                            .observeOn(schedulerProvider.mainThread())
                            .subscribeWith(object : ApiObserver(view, seqNum) {
                                override fun onFailureHttp(type: String) {
                                    getView().hideProgress()
                                    getView().onException(type)
                                }

                                override fun onApiCallSuccess(response: RestResponse) {
                                    getView().hideProgress()
                                    val data =  response.getData(SbnDashboardHeaderResponse::class.java)

                                    getView().onSuccessSbnDashboardHeader(data)

                                }

                                override fun onApiCallError(restResponse: RestResponse) {
                                    getView().hideProgress()
                                    if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value, ignoreCase = true)) {
                                        getView().onSessionEnd(restResponse.desc)
                                    } else if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_12.value, ignoreCase = true)) {
                                        getView().onException12(restResponse.desc)
                                    }else if(restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.value)){
                                        getView().onExceptionTrxExpired(restResponse.desc)
                                    }
                                    else {
                                        getView().onException(restResponse.desc)
                                    }
                                }
                            })
            )
            listConnectableObservable.connect()
        }
    }
    override fun getTopUpRdn(request : InquiryRdnRequest) {
        if (isViewAttached) {
            val seqNum = brImoPrefRepository.seqNumber
//            view.showProgress()
            compositeDisposable.add(
                    apiSource.getData(mUrlInquiryRdn, request, seqNum)
                            .subscribeOn(schedulerProvider.single())
                            .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                            .observeOn(schedulerProvider.mainThread())
                            .subscribeWith(object : ApiObserver(view, seqNum) {
                                override fun onFailureHttp(errorMessage: String) {
                                    getView().hideProgress()
                                    getView().onException(errorMessage)
                                }

                                override fun onApiCallSuccess(response: RestResponse) {
                                    getView().hideProgress()
                                    val responResend = response.getData(InquiryRdnResponse::class.java)
                                    getView().onSuccessInquiry(responResend)
                                }

                                override fun onApiCallError(restResponse: RestResponse) {
                                    getView().hideProgress()
                                    if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value, ignoreCase = true)) getView().onSessionEnd(restResponse.desc) else getView().onException(restResponse.desc)
                                }
                            })
            )
        }
    }

    override fun getSbnBeliSbn() {
        if (isViewAttached) {
            val seqNum = brImoPrefRepository.seqNumber
            val listConnectableObservable =
                apiSource.getDataTanpaRequest(urlBeli, seqNum).subscribeOn(
                    schedulerProvider.io()
                ).timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .observeOn(schedulerProvider.mainThread())
                    .replay()
            compositeDisposable.add(
                listConnectableObservable
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(Schedulers.io())
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(type: String) {
                            getView().hideProgress()
                            getView().onException(type)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            getView().hideProgress()
                            val data =  response.getData(SbnBeliResponse::class.java)

                            getView().onSuccessBeliSbn(data)

                        }

                                override fun onApiCallError(restResponse: RestResponse) {
                                    getView().hideProgress()
                                    if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value, ignoreCase = true)) {
                                        getView().onSessionEnd(restResponse.desc)
                                    } else if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_12.value, ignoreCase = true)) {
                                        getView().onException12(restResponse.desc)
                                    } else {
                                        getView().onException(restResponse.desc)
                                    }
                                }
                            })
            )
            listConnectableObservable.connect()
        }
    }

    override fun getInvesmentRecap() {
        if (isViewAttached) {
            val seqNum = brImoPrefRepository.seqNumber
            val listConnectableObservable =
                apiSource.getDataTanpaRequest(mUrlInvesmentRecap, seqNum).subscribeOn(
                    schedulerProvider.io()
                ).timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .observeOn(schedulerProvider.mainThread())
                    .replay()
            compositeDisposable.add(
                listConnectableObservable
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(Schedulers.io())
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(type: String) {
                            getView().hideProgress()
                            getView().onException(type)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            getView().hideProgress()
                            val data =  response.getData(InvestmentRecapResponse::class.java)

                            getView().onSuccessInvesmentRecap(data)

                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView().hideProgress()
                           if (restResponse.code.equals("12", ignoreCase = true)) {
                                getView().onException12(restResponse.desc)
                            } else {
                                getView().onExceptionInvesmentRecap(restResponse.desc)
                            }
                        }
                    })
            )
            listConnectableObservable.connect()
        }
    }
}