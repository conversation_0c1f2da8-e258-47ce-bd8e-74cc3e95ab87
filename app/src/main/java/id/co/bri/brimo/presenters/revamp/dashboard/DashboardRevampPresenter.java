package id.co.bri.brimo.presenters.revamp.dashboard;

import android.util.Log;

import androidx.annotation.NonNull;

import com.google.gson.Gson;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import id.co.bri.brimo.contract.IPresenter.revamp.dashboard.IDashboardRevampPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.revamp.dashboard.IDashboardRevampView;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.lifestyle.MenuLifestyleSource;
import id.co.bri.brimo.data.repository.menudashboard.MenuDashboardSource;
import id.co.bri.brimo.data.repository.menukategori.MenuKategoriSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.config.AppConfig;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.config.MenuConfig;
import id.co.bri.brimo.domain.converter.MapperHelper;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.domain.helpers.LifestyleHelper;
import id.co.bri.brimo.domain.helpers.calendar.CalendarHelper;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.PFMTotalAmountResponse;
import id.co.bri.brimo.models.apimodel.request.BaseSubscribeTopicRequest;
import id.co.bri.brimo.models.apimodel.request.DetailPromoRequest;
import id.co.bri.brimo.models.apimodel.request.nfcpayment.GeneratePayloadNfcRequest;
import id.co.bri.brimo.models.apimodel.request.IdRequest;
import id.co.bri.brimo.models.apimodel.request.LatestBlastNotifRequest;
import id.co.bri.brimo.models.apimodel.request.NotifReadFastMenuRequest;
import id.co.bri.brimo.models.apimodel.request.NotifUnreadsRequest;
import id.co.bri.brimo.models.apimodel.request.SafetyPusatBantuanRequest;
import id.co.bri.brimo.models.apimodel.request.bilingual.PrefrencesLanguageRequest;
import id.co.bri.brimo.models.apimodel.response.EmptyStateResponse;
import id.co.bri.brimo.models.apimodel.request.bilingual.PrefrencesLanguageRequest;
import id.co.bri.brimo.models.apimodel.response.EmptyStateResponse;
import id.co.bri.brimo.models.apimodel.request.splitbill.ListSplitBillRequest;
import id.co.bri.brimo.models.apimodel.response.NotifUnreadsResponse;
import id.co.bri.brimo.models.apimodel.response.NotifikasiBlastResponse;
import id.co.bri.brimo.models.apimodel.response.PromoResponse;
import id.co.bri.brimo.models.apimodel.response.QuestionResponse;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.models.apimodel.response.SafetyModeResponse;
import id.co.bri.brimo.models.apimodel.response.SaldoReponse;
import id.co.bri.brimo.models.apimodel.response.lifestyle.DashboardLifestyleMenuResponse;
import id.co.bri.brimo.models.apimodel.response.lifestyle.FeatureDataView;
import id.co.bri.brimo.models.apimodel.response.splitbill.BillListResponse;
import id.co.bri.brimo.models.apimodel.response.splitbill.SplitBillHistoryResponse;
import id.co.bri.brimo.models.apimodel.response.splitbill.viewentity.BillEntity;
import id.co.bri.brimo.models.apimodel.response.nfcpayment.NfcPayloadResponse;
import id.co.bri.brimo.models.daomodel.DashboardMenu.MenuDashboard;
import id.co.bri.brimo.models.daomodel.DashboardMenu.MenuKategori;
import id.co.bri.brimo.models.daomodel.lifestyle.MenuLifestyle;
import id.co.bri.brimo.presenters.MvpPresenter;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.disposables.Disposable;
import io.reactivex.observers.DisposableCompletableObserver;
import io.reactivex.observers.DisposableMaybeObserver;
import io.reactivex.schedulers.Schedulers;

public class DashboardRevampPresenter<V extends IMvpView & IDashboardRevampView> extends MvpPresenter<V> implements IDashboardRevampPresenter<V> {

    private static final String TAG = "DashboardRevampPresente";
    private String urlSaldoUtama;
    private String urlPfmSummary;
    private String urlSafetyMode;
    private String urlLatestBlastNotif;
    private String urlNotifUnreads;
    private String urlPusatBantuanSafety;
    private String urlPromoFeatured;
    private String urlDetailItemPromo;
    private String urlReadNotif;
    private String urlUpdateToken;
    private String urlInfoSaldoHold;
    private String urlSplitBillList;
    private String urlGetPayloadNfc;
    private String imagePopup;
    private String promoId;
    private int blastId;
    private String additionalData, blastType;
    public String urlDashboardLifestyle;
    private String mUrlPrefrences;

    private MenuKategoriSource menuKategoriSource;
    private MenuDashboardSource menuDashboardSource;

    private MenuLifestyleSource menuLifestyleSource;

    protected Object detailPromoRequest;

    private DashboardLifestyleMenuResponse dashboardLifestyleMenuResponse;

    private int menuPos;

    public DashboardRevampPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource, MenuKategoriSource menuKategoriSource, MenuDashboardSource menuDashboardSource, MenuLifestyleSource menuLifestyleSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
        this.menuKategoriSource = menuKategoriSource;
        this.menuDashboardSource = menuDashboardSource;
        this.menuLifestyleSource = menuLifestyleSource;
    }

    @Override
    public void setUrlSaldo(String urlSaldo) {
        urlSaldoUtama = urlSaldo;
    }

    @Override
    public void setUrlPFMSummary(String url) {
        urlPfmSummary = url;
    }

    @Override
    public void setUrlLatestBlastNotif(String latestBlastUrl) {
        urlLatestBlastNotif = latestBlastUrl;
    }

    @Override
    public void setUrlNotificationUnreads(String notifUnreadUrl) {
        urlNotifUnreads = notifUnreadUrl;
    }

    @Override
    public void setUrlReadNotif(String urlRead) {
        urlReadNotif = urlRead;
    }

    @Override
    public void setUrlPusatBantuanSafety(String urlPusatSafety) {
        urlPusatBantuanSafety = urlPusatSafety;
    }

    @Override
    public void setUrlSafetyMode(String urlSafety) {
        urlSafetyMode = urlSafety;
    }

    @Override
    public void start() {
        super.start();

        //pop up notif enable
        setDisablePopup(false);

        //set user type exist
        getBRImoPrefRepository().saveUserExist(true);

        //get menu kategori
        getMenuKategori();

    }

    @Override
    public void getMenuKategori() {
        if (isViewAttached()) {
            getCompositeDisposable().add(menuKategoriSource.getMenuKategori()
                    .subscribeOn(getSchedulerProvider().io())
                    .observeOn(getSchedulerProvider().mainThread())
                    .subscribeWith(new DisposableMaybeObserver<List<MenuKategori>>() {
                        @Override
                        public void onSuccess(List<MenuKategori> menuKategoriList) {
                            if (!menuKategoriList.isEmpty()) {
                                //set 4 kategori menu
                                List<MenuKategori> menuKategoriFour = getFourKategoriMenu(menuKategoriList, 0, 4);
                                getView().setFourMenuKategori(menuKategoriFour);
                                //set all menu
                                List<MenuKategori> menuKategoriAll = getFourKategoriMenu(menuKategoriList, 4, menuKategoriList.size());
                                if (menuKategoriAll.size() != 0) {
                                    getView().setAllMenuKategori(menuKategoriAll);
                                } else {
                                    getView().setAllMenuKategori(getFourKategoriMenu(MenuConfig.getDefaultMenuKategori(), 4, MenuConfig.getDefaultMenuKategori().size()));
                                }
                            }

                        }

                        @Override
                        public void onError(Throwable e) {

                        }

                        @Override
                        public void onComplete() {

                        }
                    }));
        }
    }

    @Override
    public void getSaldoUtama() {
        if (isViewAttached()) {
            //initiate param with getter from view
            String seqNum = getBRImoPrefRepository().getSeqNumber();

            //load loading dot
            getView().showProgressSaldo(true);

            Disposable disposable = getApiSource().getDataTanpaRequest(urlSaldoUtama, seqNum)//function(param)
                    .subscribeOn(getSchedulerProvider().io())
                    .observeOn(getSchedulerProvider().mainThread())
                    .subscribeWith(new ApiObserver(getView(), seqNum) {

                        @Override
                        protected void onFailureHttp(String errorMessage) {
                            getView().onExceptionIgnore(errorMessage);
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            SaldoReponse saldoReponse = response.getData(SaldoReponse.class);
                            if (saldoReponse != null) {
                                getBRImoPrefRepository().saveSaldoHold(saldoReponse.isOnHold());
                                getBRImoPrefRepository().saveSaldoRekeningUtama(String.valueOf(saldoReponse.getBalance()));
                                getBRImoPrefRepository().saveSaldoRekeningUtamaString(saldoReponse.getBalanceString());
                                getBRImoPrefRepository().saveCurrency(saldoReponse.getCurrency());
                                getBRImoPrefRepository().saveNameRekeningUtama(saldoReponse.getName());
                                getBRImoPrefRepository().saveAccountDefault(saldoReponse.getAccount());
                                getView().onSuccessSaldoUtama(saldoReponse);
                            }
                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {

                            if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_99.getValue()))
                                getView().onException99(restResponse.getDesc());
                            else {
                                getView().onExceptionIgnore(restResponse.getDesc());
                                getView().onSaldoNoResp();
                                getBRImoPrefRepository().saveSaldoRekeningUtama(String.valueOf(0));
                                getBRImoPrefRepository().saveSaldoRekeningUtamaString("-");
                            }
                        }
                    });

            getCompositeDisposable().add(disposable);
        }
    }


    @Override
    public void getPFMSummary() {
        if (getView() != null) {
            getView().showSkeletonPfm(true);
            String seqNum = getBRImoPrefRepository().getSeqNumber();
            getCompositeDisposable()
                    .add(getApiSource().getDataTanpaRequest(urlPfmSummary, seqNum)
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .subscribeOn(getSchedulerProvider().single())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribeWith(new ApiObserver(getView(), seqNum) {

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    getView().showSkeletonPfm(false);
                                    PFMTotalAmountResponse data = response.getData(PFMTotalAmountResponse.class);

                                    if (data.getPfmTotalAmount().getTotalAmountPemasukanString() != null) {
                                        getView().onSuccessGetIncome(data.getPfmTotalAmount().getTotalAmountPemasukanString());
                                    }

                                    if (data.getPfmTotalAmount().getTotalAmountPengeluaranString() != null) {
                                        getView().onSuccessGetOutcome(data.getPfmTotalAmount().getTotalAmountPengeluaranString());
                                    }

                                    if (data.getPfmTotalAmount().getDifferenceAmountString() != null) {
                                        char firstChar = data.getPfmTotalAmount().getDifferenceAmountString().charAt(0);

                                        if (Character.toString(firstChar).equalsIgnoreCase("-")) {
                                            getView().onSuccessGetPfmSummary(false, data.getPfmTotalAmount().getDifferenceAmountString());
                                        } else {
                                            getView().onSuccessGetPfmSummary(true, data.getPfmTotalAmount().getDifferenceAmountString());
                                        }
                                    }

                                    if (data.getPfmTotalAmount().getDateRange() != null) {
                                        getView().onSuccessGetDatePfm(data.getPfmTotalAmount().getDateRange());
                                    }
                                    //shoq hide berdasarkan cache
                                    getView().checkPfmFlag();
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().showSkeletonPfm(false);
                                    getView().onExceptionSummaryPfm();
                                }

                                @Override
                                protected void onFailureHttp(String type) {
                                    getView().showSkeletonPfm(false);
                                    getView().onExceptionSummaryPfm();
                                }
                            }));
        }
    }

    @Override
    public void getPusatBantuanSafety(String id) {
        if (urlPusatBantuanSafety == null || !isViewAttached()) {
            Log.d(TAG, "getDataInquiry: view atau inquiry urlInformasi null");
            return;
        }

        getView().showProgress();
        SafetyPusatBantuanRequest safetyPusatBantuanRequest = new SafetyPusatBantuanRequest(id);
        String seqNum = getBRImoPrefRepository().getSeqNumber();

        getCompositeDisposable()
                .add(getApiSource().getData(urlPusatBantuanSafety, safetyPusatBantuanRequest, seqNum)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(Schedulers.io())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(new ApiObserver(getView(), seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().hideProgress();
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                //TO-DO onSuccess
                                getView().hideProgress();
                                QuestionResponse topicQuestionResponse = response.getData(QuestionResponse.class);

                                if (urlPusatBantuanSafety != null)
                                    getView().onSuccessGetPusatBantuan(topicQuestionResponse);
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                    getView().onSessionEnd(restResponse.getDesc());
                                else
                                    getView().onException(restResponse.getDesc());

                            }
                        }));

    }

    @Override
    public void getTimerSafetyMode() {

        if (!isViewAttached() || urlSafetyMode.isEmpty())
            return;


        String seqNum = getBRImoPrefRepository().getSeqNumber();

        getCompositeDisposable().add(
                getApiSource().getDataTanpaRequest(urlSafetyMode, seqNum)
                        .subscribeOn(getSchedulerProvider().io())
                        .observeOn(getSchedulerProvider().mainThread())
                        .subscribeWith(new ApiObserver(getView(), seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SUCCESS.getValue())) {
                                    SafetyModeResponse safetyModeResponse = response.getData(SafetyModeResponse.class);
                                    getView().onSuccessGetTimerSafety(safetyModeResponse);
                                }
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue())) {
                                    getView().onSessionEnd(restResponse.getDesc());
                                } else {
                                    getView().onExceptionIgnore(restResponse.getDesc());
                                }
                            }
                        })
        );
    }


    @Override
    public void setUrlPromoFeatured(String url) {
        urlPromoFeatured = url;
    }

    @Override
    public void setDetailItemPromoUrl(String detailItemUrl) {
        urlDetailItemPromo = detailItemUrl;
    }

    @Override
    public void setUrlUpdateToken(String urlUpdateToken) {
        this.urlUpdateToken = urlUpdateToken;
    }

    @Override
    public void setUrlInfoSaldoHold(String url) {
        urlInfoSaldoHold = url;
    }

    @Override
    public void setUrlGetPayload(String url) {
        this.urlGetPayloadNfc = url;
    }

    @Override
    public void getInfoSaldoHold() {
        if (urlInfoSaldoHold.isEmpty() || !isViewAttached()) return;

        getView().showProgress();

        IdRequest idRequest = new IdRequest("105");
        String seqNum = getBRImoPrefRepository().getSeqNumber();

        getCompositeDisposable()
                .add(getApiSource().getData(urlInfoSaldoHold, idRequest, seqNum)
                        .subscribeOn(getSchedulerProvider().io())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(new ApiObserver(getView(), seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                //do nothing
                                getView().hideProgress();
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                //TO-DO onSuccess
                                getView().hideProgress();
                                QuestionResponse questionResponse = response.getData(QuestionResponse.class);
                                getView().onSuccessInfoSaldoHold(questionResponse);
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                    getView().onSessionEnd(restResponse.getDesc());
                                else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_99.getValue()))
                                    getView().onException99(restResponse.getDesc());
                                else
                                    getView().onException(restResponse.getDesc());
                            }
                        }));
    }

    @Override
    public void getBrimoPrefSaldoHold() {

    }

    @Override
    public void saveBrimoPrefSaldoHold() {

    }

    @Override
    public void getTotalAmount() {

    }

    @Override
    public void updateTokenFirebase() {
        if (getBRImoPrefRepository().getUpdateTokenFirebase()) {
            if (!GeneralHelper.isProd()) {
                Log.d(TAG, "updateTokenFirebase: done update");
            }
            return;
        }

        BaseSubscribeTopicRequest request = new BaseSubscribeTopicRequest(
                getBRImoPrefRepository().getUsername(),
                getBRImoPrefRepository().getTokenKey(),
                getBRImoPrefRepository().getFirebaseToken()
        );

        String seqNum = getBRImoPrefRepository().getSeqNumber();

        getCompositeDisposable().add(
                getApiSource().getData(urlUpdateToken, request, seqNum)
                        .subscribeOn(getSchedulerProvider().io())
                        .observeOn(getSchedulerProvider().mainThread())
                        .subscribeWith(new ApiObserver(getView(), seqNum) {

                            @Override
                            protected void onFailureHttp(String type) {
                                getBRImoPrefRepository().saveUpdateTokenFirebase(false);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                // do nothing
                                getBRImoPrefRepository().saveUpdateTokenFirebase(true);
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getBRImoPrefRepository().saveUpdateTokenFirebase(false);
                            }
                        })
        );
    }

    @Override
    public void saveSaldoHideFlag(boolean isSaldoHide) {
        if (getBRImoPrefRepository() != null)
            getBRImoPrefRepository().updateSaldoHide(isSaldoHide);
    }

    @Override
    public void savePfmHideFlag(boolean isPfmHide) {
        if (getBRImoPrefRepository() != null)
            getBRImoPrefRepository().updatePfmHide(isPfmHide);
    }

    @Override
    public void getListMenubyId(int kategoryId, String title) {
        if (isViewAttached()) {
            getCompositeDisposable().add(menuDashboardSource.getMenuByID(kategoryId)
                    .subscribeOn(getSchedulerProvider().io())
                    .observeOn(getSchedulerProvider().mainThread())
                    .subscribeWith(new DisposableMaybeObserver<List<MenuDashboard>>() {

                        @Override
                        public void onSuccess(List<MenuDashboard> menuDashboards) {
                            if (!menuDashboards.isEmpty()) {
                                getView().onSuccessGetMenubyId(menuDashboards, title, kategoryId);
                            }
                        }

                        @Override
                        public void onError(Throwable e) {

                        }

                        @Override
                        public void onComplete() {

                        }
                    }));
        }
    }

    @Override
    public void updateFlagNewKategori(int kategoryId) {
        getCompositeDisposable().add(menuKategoriSource.updateKategori(kategoryId, MenuConfig.NewStatus.OLD)
                .subscribeOn(getSchedulerProvider().io())
                .observeOn(getSchedulerProvider().mainThread())
                .subscribeWith(new DisposableCompletableObserver() {
                    @Override
                    public void onComplete() {
                        getMenuKategori();
                    }

                    @Override
                    public void onError(@NonNull Throwable e) {
                        // do nothing
                    }
                })
        );
    }

    @Override
    public void getPromoFeatured() {
        if (!isViewAttached() || urlPromoFeatured.isEmpty())
            return;
        String seqNum = getBRImoPrefRepository().getSeqNumber();

        getCompositeDisposable().add(getApiSource().getDataForm(urlPromoFeatured, seqNum)
                .subscribeOn(getSchedulerProvider().io())
                .observeOn(getSchedulerProvider().mainThread())
                .subscribeWith(new ApiObserver(getView(), seqNum) {
                    @Override
                    protected void onFailureHttp(String type) {
                        getView().showSkeletonPfm(true);
                    }

                    @Override
                    protected void onApiCallSuccess(RestResponse response) {
                        getView().showSkeletonPfm(false);
                        PromoResponse promoResponse = response.getData(PromoResponse.class);

                        getView().onSuccessGetPromoFeatured(promoResponse);
                    }

                    @Override
                    protected void onApiCallError(RestResponse restResponse) {
                        getView().showSkeletonPfm(true);
                    }
                })
        );
    }

    @Override
    public void getDetailPromoItem(String id) {
        if (urlDetailItemPromo == null || !isViewAttached())
            return;

        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();

        detailPromoRequest = new DetailPromoRequest(id);
        getCompositeDisposable().add(getApiSource().getData(urlDetailItemPromo, detailPromoRequest, seqNum)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(new ApiObserver(getView(), seqNum) {

                    @Override
                    protected void onFailureHttp(String errorMessage) {
                        getView().hideProgress();
                        getView().onException(errorMessage);
                    }

                    @Override
                    protected void onApiCallSuccess(RestResponse response) {
                        getView().hideProgress();
                        PromoResponse responsebriva = response.getData(PromoResponse.class);

                        getView().onSuccessGetDetailItem(responsebriva);
                    }

                    @Override
                    protected void onApiCallError(RestResponse restResponse) {
                        getView().hideProgress();
                        if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                            getView().onSessionEnd(restResponse.getDesc());
                        else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_99.getValue()))
                            getView().onException99(restResponse.getDesc());
                        else
                            getView().onException(restResponse.getDesc());
                    }
                })
        );
    }

    @Override
    public void getLatestBlastNotif() {
        if (getView() != null) {
            LatestBlastNotifRequest request = new LatestBlastNotifRequest(
                    getBRImoPrefRepository().getUsername(),
                    getBRImoPrefRepository().getFirebaseToken()
            );

            String seqNum = getBRImoPrefRepository().getSeqNumber();

            getCompositeDisposable().add(getApiSource().getData(urlLatestBlastNotif, request, seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                    .subscribeOn(getSchedulerProvider().io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(new ApiObserver(getView(), seqNum) {

                        @Override
                        protected void onFailureHttp(String type) {
                            getView().hideProgress();
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            getView().hideProgress();

                            if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SUCCESS.getValue())) {
                                NotifikasiBlastResponse notifikasiBlastResponse = response.getData(NotifikasiBlastResponse.class);

                                if (getBRImoPrefRepository().getBlastId() != notifikasiBlastResponse.getNotifikasiModel().getBlastId()) {

                                    imagePopup = notifikasiBlastResponse.getNotifikasiModel().getImagePopup();
                                    promoId = String.valueOf(notifikasiBlastResponse.getNotifikasiModel().getPromoId());
                                    blastId = notifikasiBlastResponse.getNotifikasiModel().getBlastId();
                                    additionalData = notifikasiBlastResponse.getNotifikasiModel().getAdditionalPayload();
                                    blastType = notifikasiBlastResponse.getNotifikasiModel().getType();

                                    //menyimpan Blast ID Terakhir
                                    getBRImoPrefRepository().saveBlastId(blastId);
                                    getView().successGetPromoNotification(notifikasiBlastResponse);
                                }
                            }
                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            getView().hideProgress();
                            if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                getView().onSessionEnd(restResponse.getDesc());
                            else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_99.getValue()))
                                getView().onException99(restResponse.getDesc());
                        }
                    })
            );
        }
    }

    @Override
    public void getReadNotifFastMenu(String blastId) {
        NotifReadFastMenuRequest notifRequest = new NotifReadFastMenuRequest("android",
                blastId,
                getBRImoPrefRepository().getTokenKey(),
                getBRImoPrefRepository().getUsername());

        String seqNum = getBRImoPrefRepository().getSeqNumber();

        getCompositeDisposable().add(getApiSource().getData(urlReadNotif, notifRequest, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                .subscribeOn(getSchedulerProvider().io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(new ApiObserver(getView(), seqNum) {

                    @Override
                    protected void onFailureHttp(String errorMessage) {
                        getView().onException(errorMessage);
                    }

                    @Override
                    protected void onApiCallSuccess(RestResponse response) {
                        // do nothing
                    }

                    @Override
                    protected void onApiCallError(RestResponse restResponse) {
                        if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                            getView().onSessionEnd(restResponse.getDesc());
                    }
                })
        );
    }

    @Override
    public void getTermCondition() {
        getView().successTermConditionNewOnboarding(getBRImoPrefRepository().getTermCondition());
    }

    @Override
    public void closeBubbleOnboarding() {
        if (getBRImoPrefRepository().getBubbleNewOnboarding()) {
            getView().showBubbleNewOnboarding();
            getBRImoPrefRepository().saveBubbleNewOnboarding(false);
        }
    }

    @Override
    public void setUrlDashboardLifestyleMenu(String urlDashboardLifestyleMenu) {
        this.urlDashboardLifestyle = urlDashboardLifestyleMenu;
    }

    @Override
    public void getDataPayloadNfc(String pin) {
        if (getView() != null) {
            getView().showProgress();
            String seqNum = getBRImoPrefRepository().getSeqNumber();

            Object request = new GeneratePayloadNfcRequest("", "", pin, "", "", "");

            getCompositeDisposable()
                    .add(getApiSource().getData(urlGetPayloadNfc, request, seqNum)
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .subscribeOn(getSchedulerProvider().io())
                            .observeOn(getSchedulerProvider().mainThread())
                            .subscribeWith(new ApiObserver(getView(), seqNum) {

                                @Override
                                protected void onFailureHttp(String errorMessage) {
                                    getView().hideProgress();
                                    getView().onException(errorMessage);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    getView().hideProgress();
                                    NfcPayloadResponse data = response.getData(NfcPayloadResponse.class);
                                    getView().onSuccessGetNfcPayload(data);
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().hideProgress();
                                    if (restResponse.getCode().equalsIgnoreCase(Constant.RE_FITUR_OFF)){
                                        EmptyStateResponse response = restResponse.getData(EmptyStateResponse.class);
                                        getView().onExceptionFO(response);
                                    } else {
                                        getView().onException(restResponse.getDesc());
                                    }
                                }
                            })
                    );
        }
    }

    @Override
    public void getNotificationUnreads() {
        if (getView() != null) {
            NotifUnreadsRequest request = new NotifUnreadsRequest(
                    "android"
            );

            String seqNum = getBRImoPrefRepository().getSeqNumber();

            getCompositeDisposable()
                    .add(getApiSource().getData(urlNotifUnreads, request, seqNum)
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .subscribeOn(getSchedulerProvider().io())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribeWith(new ApiObserver(getView(), seqNum) {

                                @Override
                                protected void onFailureHttp(String errorMessage) {
                                    getView().hideProgress();
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    getView().hideProgress();
                                    NotifUnreadsResponse notifUnreadsResponse = response.getData(NotifUnreadsResponse.class);
                                    getView().onSuccessNotifUnread(notifUnreadsResponse);
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().hideProgress();
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                        getView().onSessionEnd(restResponse.getDesc());
                                }
                            }));
        }
    }

    @Override
    public void getNickname() {
        String nickname = getBRImoPrefRepository().getNickname();
        getView().setNickname(nickname);
    }

    @Override
    public boolean getSaldoHideFlag() {
        boolean flag = false;
        if (getBRImoPrefRepository() != null)
            flag = getBRImoPrefRepository().isSaldoHide();
        return flag;
    }

    @Override
    public boolean getPfmHideFlag() {
        return getBRImoPrefRepository().isPfmHide();
    }

    private List<MenuKategori> getFourKategoriMenu(List<MenuKategori> list, int indexStart, int indexEnd) {
        List<MenuKategori> listMenu = new ArrayList<MenuKategori>();

        if (list.size() >= indexEnd) {
            for (int item = indexStart; item < indexEnd; item++) {
                if (list.get(item) != null)
                    listMenu.add(list.get(item));
            }
        }

        return listMenu;
    }

    @Override
    public String getBiometricType() {
        return getBRImoPrefRepository().getBiometricType();
    }

    @Override
    public void updateStatusUpdateBio(boolean statusUpdate) {
        getBRImoPrefRepository().saveStatusUpdateBio(statusUpdate);
    }

    @Override
    public boolean getNewBubbleOnboard() {
        return getBRImoPrefRepository().getBubbleNewOnboarding();
    }

    @Override
    public boolean getBottomBiometric() {
        return getBRImoPrefRepository().getBottomBiometric();
    }

    @Override
    public void updateBottomBiometric(boolean bottomBio) {
        getBRImoPrefRepository().saveBottomBiometric(bottomBio);
    }

    @Override
    public boolean getStatusUpdateBio() {
        return getBRImoPrefRepository().getStatusUpdateBio();
    }

    @Override
    public void getDataDashboardLifestyleMenu() {
        if (isViewAttached()) {

            getView().showProgress();
            String seqNum = getBRImoPrefRepository().getSeqNumber();

            Disposable disposable = getApiSource().getDataTanpaRequest(urlDashboardLifestyle, seqNum)//function(param)
                    .subscribeOn(getSchedulerProvider().single())
                    .observeOn(getSchedulerProvider().mainThread())
                    .subscribeWith(new ApiObserver(getView(), seqNum) {

                        @Override
                        protected void onFailureHttp(String errorMessage) {
                            getView().hideProgress();
                            getView().onException(errorMessage);
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            getView().hideProgress();
                            dashboardLifestyleMenuResponse =
                                    response.getData(DashboardLifestyleMenuResponse.class);

                            String parentfeatureCode = "";
                            String subfeatureCode = "";
                            String updateParentFeature = "";
                            String updateSubFeature = "";
                            boolean parentIsNew = false;
                            boolean subIsNew = false;

                            List<FeatureDataView> featureDataLocals = new ArrayList<>();
                            for (int menuData = 0; menuData < dashboardLifestyleMenuResponse.getMenuDataView().size();
                                 menuData++) {

                                featureDataLocals.addAll(
                                        dashboardLifestyleMenuResponse.getMenuDataView().get(menuData).getFeature());

                                for (int parentMenu = 0; parentMenu < dashboardLifestyleMenuResponse.getMenuDataView()
                                        .get(menuData).getFeature().size(); parentMenu++) {

                                    parentfeatureCode = dashboardLifestyleMenuResponse.getMenuDataView()
                                            .get(menuData).getFeature().get(parentMenu).getFeatureCode();
                                    parentIsNew = dashboardLifestyleMenuResponse.getMenuDataView()
                                            .get(menuData).getFeature().get(parentMenu).isNew();
                                    updateParentFeature = dashboardLifestyleMenuResponse.getMenuDataView()
                                            .get(menuData).getFeature().get(parentMenu).getUpdatedDate();

                                    //to check if parent menu not have submenu and isNew true then set isNew to false
                                    checkMenuLifestyle(parentfeatureCode);

                                    if (dashboardLifestyleMenuResponse.getMenuDataView().get(menuData)
                                            .getFeature().get(parentMenu).getSubFeature() != null &&
                                            !dashboardLifestyleMenuResponse.getMenuDataView()
                                            .get(menuData).getFeature().get(parentMenu).getSubFeature().isEmpty()
                                    ) {

                                        for (int subMenu = 0; subMenu < dashboardLifestyleMenuResponse.getMenuDataView()
                                                .get(menuData).getFeature().get(parentMenu).getSubFeature().size(); subMenu++) {

                                            if (subMenu != 0) {
                                                featureDataLocals.addAll(
                                                        dashboardLifestyleMenuResponse.getMenuDataView()
                                                                .get(menuData).getFeature().get(parentMenu).getSubFeature()
                                                );
                                            }

                                            updateSubFeature = dashboardLifestyleMenuResponse.getMenuDataView()
                                                    .get(menuData).getFeature().get(parentMenu)
                                                    .getSubFeature().get(subMenu).getUpdatedDate();

                                            subfeatureCode = dashboardLifestyleMenuResponse.getMenuDataView()
                                                    .get(menuData).getFeature().get(parentMenu)
                                                    .getSubFeature().get(subMenu).getFeatureCode();

                                            subIsNew = dashboardLifestyleMenuResponse.getMenuDataView()
                                                    .get(menuData).getFeature().get(parentMenu)
                                                    .getSubFeature().get(subMenu).isNew();
                                        }
                                    }

                                }
                            }

                            if (response.getCode().equals(RestResponse.ResponseCodeEnum.RC_SUCCESS.getValue())) {
                                if (Objects.equals(getBRImoPrefRepository().getDateMenuUpdate(), "")) {
                                    saveMenuLifestyle(featureDataLocals);
                                } else if (Boolean.TRUE == CalendarHelper.compareDate(
                                        getBRImoPrefRepository().getDateMenuUpdate(),
                                        CalendarHelper.stringDateTimeToddMMyyyy(
                                                dashboardLifestyleMenuResponse.getUpdatedDate()))) {
                                    saveMenuLifestyle(featureDataLocals);
                                } else if (Boolean.TRUE == CalendarHelper.compareDate(
                                        getBRImoPrefRepository().getDateMenuUpdate(),
                                        CalendarHelper.stringDateTimeToddMMyyyy(updateParentFeature))) {
                                    updateFlagNewLifestyle(parentfeatureCode, parentIsNew ? 1 : 0);
                                }
                                else if (updateSubFeature != null &&
                                        !updateSubFeature.isEmpty() &&
                                        Boolean.TRUE == CalendarHelper.compareDate(
                                        getBRImoPrefRepository().getDateMenuUpdate(),
                                        CalendarHelper.stringDateTimeToddMMyyyy(updateSubFeature))) {
                                    updateFlagNewLifestyle(subfeatureCode, subIsNew ? 1 : 0);
                                } else {
                                    getView().onSuccessDashboardLifestyleMenu(dashboardLifestyleMenuResponse);
                                }
                            }
                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            getView().hideProgress();
                            if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                getView().onSessionEnd(restResponse.getDesc());
                            else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_99.getValue()))
                                getView().onException99(restResponse.getDesc());
                            else
                                getView().onException(restResponse.getDesc());
                        }
                    });

            getCompositeDisposable().add(disposable);
        }
    }

    @Override
    public void saveAlertMaintenanceTokenId(String id) {
        getBRImoPrefRepository().saveAlertMaintenanceTokenId(id);
    }

    @Override
    public String getAlertMaintenanceTokenId() {
        return getBRImoPrefRepository().getAlertMaintenanceTokenId();
    }

    private void saveMenuLifestyle(List<FeatureDataView> featureDataViews) {
        getCompositeDisposable().add(menuLifestyleSource
                .insertMenuLifestyle(MapperHelper.dashboardLifestyleModelConverter(featureDataViews))
                .subscribeOn(getSchedulerProvider().io())
                .observeOn(getSchedulerProvider().mainThread())
                .subscribeWith(new DisposableCompletableObserver() {
                    @Override
                    public void onComplete() {
                        getBRImoPrefRepository().saveDateMenuUpdate(
                                CalendarHelper.stringDateTimeToddMMyyyy(
                                        dashboardLifestyleMenuResponse.getUpdatedDate()
                                )
                        );
                        getView().onSuccessDashboardLifestyleMenu(dashboardLifestyleMenuResponse);
                    }

                    @Override
                    public void onError(@NonNull Throwable e) {
                        // do nothing
                    }
                }));
    }

    private void updateFlagNewLifestyle(String featureCode, int isNew) {
        getCompositeDisposable().add(menuLifestyleSource.
                updateMenuLifestyle(featureCode, isNew)
                .subscribeOn(getSchedulerProvider().io())
                .observeOn(getSchedulerProvider().mainThread())
                .subscribeWith(new DisposableCompletableObserver() {
                    @Override
                    public void onComplete() {
                        getBRImoPrefRepository().saveDateMenuUpdate(
                                CalendarHelper.stringDateTimeToddMMyyyy(
                                        dashboardLifestyleMenuResponse.getUpdatedDate()
                                )
                        );
                        getView().onSuccessDashboardLifestyleMenu(dashboardLifestyleMenuResponse);
                    }

                    @Override
                    public void onError(@NonNull Throwable e) {
                        // do nothing
                    }
                })
        );
    }

    public void checkMenuLifestyle(String parentfeatureCode) {
        if (isViewAttached()) {
            getCompositeDisposable().add(menuLifestyleSource.getNewMenubyParentCode(parentfeatureCode)
                    .subscribeOn(getSchedulerProvider().io())
                    .observeOn(getSchedulerProvider().mainThread())
                    .subscribeWith(new DisposableMaybeObserver<List<MenuLifestyle>>() {
                        @Override
                        public void onSuccess(List<MenuLifestyle> menuLifestyles) {
                            if (menuLifestyles.size() == 1) {
                                updateFlagNewLifestyle(parentfeatureCode, 0);
                            }
                        }

                        @Override
                        public void onError(Throwable e) {

                        }

                        @Override
                        public void onComplete() {

                        }
                    }));
        }



    }

    @Override
    public void setUrlPrefrences(String urlPrefrences) {
        this.mUrlPrefrences = urlPrefrences;
    }

    //need to be review
    @Override
    public void updateFirstTimePrefrencesLanguage() {
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        PrefrencesLanguageRequest request = new PrefrencesLanguageRequest(getFastMenuRequest(), getBRImoPrefRepository().getLanguange())  ;
        getCompositeDisposable().add(
                getApiSource().getData(mUrlPrefrences, request, seqNum)
                        .subscribeOn(getSchedulerProvider().io())
                        .observeOn(getSchedulerProvider().mainThread())
                        .subscribeWith(new ApiObserver(getView(), seqNum) {
                            @Override
                            protected void onFailureHttp(String type) {
                                //do nothing

                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                // do nothing
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                //do nothing
                            }
                        })
        );

    }

    @Override
    public void setUrlSplitBillList(String url) {
        urlSplitBillList = url;
    }

    @Override
    public void getSplitBillList() {
        if (isViewAttached()) {

            getView().showProgress();
            String seqNum = getBRImoPrefRepository().getSeqNumber();

            Disposable disposable = getApiSource().getData(urlSplitBillList,
                            new ListSplitBillRequest(),
                            seqNum
                    )
                    .subscribeOn(getSchedulerProvider().io())
                    .observeOn(getSchedulerProvider().mainThread())
                    .subscribeWith(new ApiObserver(getView(), seqNum) {

                        @Override
                        protected void onFailureHttp(String errorMessage) {
                            getView().hideProgress();
                            getView().onException(errorMessage);
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            getView().hideProgress();

                            SplitBillHistoryResponse billListResponse =
                                    response.getData(SplitBillHistoryResponse.class);

                            getView().onSuccessGetSplitBillList(
                                    LifestyleHelper.Companion.mapToBillEntity(billListResponse)
                            );
                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            getView().hideProgress();
                            if (restResponse.getCode().equalsIgnoreCase("99"))
                                getView().onException99(restResponse.getDesc());
                            else
                                getView().onException(restResponse.getDesc());
                        }
                    });

            getCompositeDisposable().add(disposable);
        }
    }

    @Override
    public boolean isBillCreated() {
        return getBRImoPrefRepository().isBillCreated();
    }

}