package id.co.bri.brimo.presenters.listrikrevamp.reskin

import id.co.bri.brimo.R
import id.co.bri.brimo.contract.IPresenter.base.InquiryConfirmation
import id.co.bri.brimo.contract.IPresenter.listrikrevamp.reskin.IFormListrikReskinPresenter
import id.co.bri.brimo.contract.IPresenter.listrikrevamp.reskin.SavedListNs
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.base.IBaseTransactionView
import id.co.bri.brimo.contract.IView.listrikrevamp.reskin.ICetakTokenReskinView
import id.co.bri.brimo.contract.IView.listrikrevamp.reskin.IFormListrikReskinView
import id.co.bri.brimo.contract.IView.listrikrevamp.reskin.ISavedListrikReskinView
import id.co.bri.brimo.contract.IView.listrikrevamp.reskin.ListrikErrorCode
import id.co.bri.brimo.contract.IView.listrikrevamp.reskin.ListrikException
import id.co.bri.brimo.contract.IView.listrikrevamp.reskin.ListrikResult
import id.co.bri.brimo.contract.IView.pulsarevamp.reskin.AccountModelNs
import id.co.bri.brimo.contract.IView.pulsarevamp.reskin.IPulsaDataReskinView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.api.observer.ApiObserverKonfirmasi
import id.co.bri.brimo.data.api.observer.ApiReskinObserver
import id.co.bri.brimo.data.api.observer.ResExceptionErr
import id.co.bri.brimo.data.api.observer.mapping
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimo.data.repository.category.CategoryPfmSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.config.AppConfig
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.GeneralHelperNewSkin
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.CetakTokenRequest
import id.co.bri.brimo.models.apimodel.request.ConfirmationRequest
import id.co.bri.brimo.models.apimodel.request.FastConfirmationRequest
import id.co.bri.brimo.models.apimodel.request.InquiryPlnRequest
import id.co.bri.brimo.models.apimodel.request.StatusRequest
import id.co.bri.brimo.models.apimodel.request.revampbriva.PayBrivaRevampRequest
import id.co.bri.brimo.models.apimodel.response.DataPlnResponse
import id.co.bri.brimo.models.apimodel.response.EmptyStateResponse
import id.co.bri.brimo.models.apimodel.response.ExceptionResponse
import id.co.bri.brimo.models.apimodel.response.GeneralConfirmationResponse
import id.co.bri.brimo.models.apimodel.response.GeneralResponse
import id.co.bri.brimo.models.apimodel.response.InboxResponse
import id.co.bri.brimo.models.apimodel.response.InquiryBrivaRevampResponse
import id.co.bri.brimo.models.apimodel.response.ReceiptRevampInboxResponse
import id.co.bri.brimo.models.apimodel.response.ReceiptRevampResponse
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.SaldoReponse
import id.co.bri.brimo.models.apimodel.response.pulsarevamp.FormPulsaDataResponse
import id.co.bri.brimo.models.apimodel.response.transferrevamp.SavedDataResponse
import id.co.bri.brimo.presenters.MvpPresenter
import id.co.bri.brimo.presenters.base.BaseTransactionPresenter
import id.co.bri.brimo.presenters.base.SaldoRequestNS
import id.co.bri.brimo.util.DecodeResult
import id.co.bri.brimo.util.ResponseDecoder
import id.co.bri.brimo.util.subscribeWithObserver
import io.reactivex.Observable
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.Schedulers
import java.util.concurrent.TimeUnit
import javax.inject.Inject

open class FormListrikReskinPresenter<V> @Inject constructor(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBRImoPrefRepository: BRImoPrefSource,
    apiSource: ApiSource,
    categoryPfmSource: CategoryPfmSource,
    transaksiPfmSource: TransaksiPfmSource,
    anggaranPfmSource: AnggaranPfmSource
) : BaseTransactionPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    mBRImoPrefRepository,
    apiSource,
    transaksiPfmSource
), IFormListrikReskinPresenter<V> where V : IMvpView, V: IFormListrikReskinView {
    override fun <Req, Res> executeRequest(
        url: String,
        requestParam: Req?,
        responseType: Class<Res>,
        isSaved: FavoriteType?
    ) {
        val seqNum = brImoPrefRepository.seqNumber
        val sObserve = if(requestParam!=null)
            apiSource.getData(url, requestParam, seqNum)
        else apiSource.getDataForm(url, seqNum)

        if (isViewAttached) {
            sObserve.subscribeWithObserver(
                compositeDisposable = compositeDisposable,
                schedulerProvider = schedulerProvider,
                createObserver = {
                    object: ApiReskinObserver(view, seqNum) {
                        override fun onApiCallSuccess(response: RestResponse) {
                            if(RestResponse::class.java.isAssignableFrom(responseType)) {
                                if(isSaved!=null) {
                                    val result = mapDataToListrikResult(isSaved)
                                    (getView() as IFormListrikReskinView).onSuccess(result)
                                } else {
                                    val result = mapDataToListrikResult(response)
                                    (getView() as IFormListrikReskinView).onSuccess(result)
                                }
                            } else {
                                val resData = response.getData(responseType)

                                val result = mapDataToListrikResult(resData)
                                (getView() as IFormListrikReskinView).onSuccess(result)
                            }
                        }

                        override fun onApiCallError(errRes: ResExceptionErr) {
                            println("onApiCallError: ${errRes.code}")
                            val exception = mapErrorToPulsaDataException(errRes)
                            (getView() as IFormListrikReskinView).onExceptionReskin(exception)
                        }
                    }
                }
            )
        }
    }

    private fun <Res> mapDataToListrikResult(data: Res): ListrikResult {
        return when (data) {
            is DataPlnResponse -> ListrikResult.Form(data)
            is InquiryBrivaRevampResponse -> ListrikResult.Inquiry(data)
            is InboxResponse -> ListrikResult.CetakToken(data)
            is ReceiptRevampInboxResponse -> ListrikResult.ActivityDetail(data)
            is SavedDataResponse -> ListrikResult.InquiryFav(data)
            is ReceiptRevampResponse -> ListrikResult.Receipt(data)
            is GeneralConfirmationResponse -> ListrikResult.Confirmation(data)
            is FavoriteType -> ListrikResult.Favorite(data)
            else -> ListrikResult.Other(data as Any)
        }
    }

    private fun mapErrorToPulsaDataException(errRes: ResExceptionErr): ListrikException {
        val code = errRes.code

        return when (code) {
            ListrikErrorCode.EXCEPTION_93.code ->
                ListrikException.KnownError(code, errRes)
            ListrikErrorCode.EXCEPTION_12.code ->
                ListrikException.KnownError(code, errRes)
            ListrikErrorCode.EXCEPTION_01.code ->
                ListrikException.KnownError(code, errRes)
            ListrikErrorCode.EXCEPTION_61.code ->
                ListrikException.KnownError(code, errRes)
            else ->
                ListrikException.UnknownError(code, errRes)
        }
    }

//    override fun cetakToken() {
//        getView().showProgress()
//        val mUrl = GeneralHelper.getString(R.string.cetak_token_pln)
//
//        if (isViewAttached && mUrl != null) {
//            val seqNum = brImoPrefRepository.seqNumber
//            apiSource.getData(mUrl, CetakTokenRequest("0"), seqNum).subscribeWithObserver(
//                compositeDisposable = compositeDisposable,
//                schedulerProvider = schedulerProvider,
//                createObserver = {
//                    object: ApiObserverKonfirmasi(view, seqNum) {
//                        override fun onFailureHttp(errorMessage: String?) {
//                            getView().hideProgress()
//                            getView().onException(errorMessage)
//                        }
//
//                        override fun onApiCallSuccess(response: RestResponse) {
//                            getView().hideProgress()
//                            val receiptRevampResponse =
//                                response.getData(InboxResponse::class.java)
//
//                            (getView() as ICetakTokenReskinView).onSuccessGetCetakToken(receiptRevampResponse)
//                        }
//
//                        override fun onApiCallError(restResponse: RestResponse) {
//                            getView().hideProgress()
//                            getView().onException(restResponse.desc)
//                        }
//                    }
//                }
//            )
//        }
//    }
//    override fun updateSavedList(param: SavedListNs) {
//        getView().showProgress()
//        val mUrl = GeneralHelper.getString(R.string.url_update_pln_v3)
//
//        if (isViewAttached && mUrl != null) {
//            val seqNum = brImoPrefRepository.seqNumber
//            apiSource.getData(mUrl, param, seqNum).subscribeWithObserver(
//                compositeDisposable = compositeDisposable,
//                schedulerProvider = schedulerProvider,
//                createObserver = {
//                    object: ApiObserver(view, seqNum) {
//                        override fun onFailureHttp(errorMessage: String?) {
//                            getView().hideProgress()
//                            getView().onException(errorMessage)
//                        }
//
//                        override fun onApiCallSuccess(response: RestResponse) {
//                            getView().hideProgress()
//                            (getView() as ISavedListrikReskinView).onSuccess(response)
//                        }
//
//                        override fun onApiCallError(restResponse: RestResponse) {
//                            getView().hideProgress()
//                            getView().onException(restResponse.desc)
//                        }
//                    }
//                }
//            )
//        }
//    }
//    override fun favoriteSavedList(param: SavedListNs) {
//        getView().showProgress()
//        val mUrl = GeneralHelper.getString(R.string.url_favorit_pln_v3)
//
//        inquiryFavorite(param) {
//            if (isViewAttached && mUrl != null) {
//                val seqNum = brImoPrefRepository.seqNumber
//                apiSource.getData(mUrl, param, seqNum).subscribeWithObserver(
//                    compositeDisposable = compositeDisposable,
//                    schedulerProvider = schedulerProvider,
//                    createObserver = {
//                        object: ApiObserver(view, seqNum) {
//                            override fun onFailureHttp(errorMessage: String?) {
//                                getView().hideProgress()
//                                getView().onException(errorMessage)
//                            }
//
//                            override fun onApiCallSuccess(response: RestResponse) {
//                                getView().hideProgress()
//                                (getView() as IFormListrikReskinView).onSuccess(response, FavoriteType.favorite)
//                            }
//
//                            override fun onApiCallError(restResponse: RestResponse) {
//                                getView().hideProgress()
//                                getView().onException(restResponse.desc)
//                            }
//                        }
//                    }
//                )
//            }
//        }
//    }
//
//    override fun unfavoriteSavedList(param: SavedListNs) {
//        getView().showProgress()
//        val mUrl = GeneralHelper.getString(R.string.url_unfavorit_pln_v3)
//
//        if (isViewAttached && mUrl != null) {
//            val seqNum = brImoPrefRepository.seqNumber
//            apiSource.getData(mUrl, param, seqNum).subscribeWithObserver(
//                compositeDisposable = compositeDisposable,
//                schedulerProvider = schedulerProvider,
//                createObserver = {
//                    object: ApiObserver(view, seqNum) {
//                        override fun onFailureHttp(errorMessage: String?) {
//                            getView().hideProgress()
//                            getView().onException(errorMessage)
//                        }
//
//                        override fun onApiCallSuccess(response: RestResponse) {
//                            getView().hideProgress()
//                            (getView() as IFormListrikReskinView).onSuccess(response, FavoriteType.unfavorite)
//                        }
//
//                        override fun onApiCallError(restResponse: RestResponse) {
//                            getView().hideProgress()
//                            getView().onException(restResponse.desc)
//                        }
//                    }
//                }
//            )
//        }
//    }
}

enum class FavoriteType {
    favorite, unfavorite, removeFavorite, editFavorite
}