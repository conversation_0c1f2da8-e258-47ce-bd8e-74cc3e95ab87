package id.co.bri.brimo.presenters.brizzi;

import id.co.bri.brimo.contract.IPresenter.brizzi.IFormBrizziPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.base.IBaseFormView;
import id.co.bri.brimo.contract.IView.brizzi.IFormBrizziView;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.apimodel.request.FastInquiryBrizziRequest;

import id.co.bri.brimo.models.apimodel.request.InquiryDepositBrizziRequest;
import id.co.bri.brimo.models.apimodel.response.BrizziResponse;
import id.co.bri.brimo.models.apimodel.response.GeneralInquiryResponse;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.presenters.base.BaseFormPresenter;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.schedulers.Schedulers;

public class FormBrizziPresenter<V extends IMvpView & IBaseFormView & IFormBrizziView> extends BaseFormPresenter<V> implements IFormBrizziPresenter<V> {

    private static final String TAG = "FormBrizziPresenter";
    private String inquiryUrlTopUp;
    private String konfirmasiUrlTopUp;


    public FormBrizziPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, categoryPfmSource, transaksiPfmSource, anggaranPfmSource);
    }


    /*
     * method untuk memproses restResponse jika berhasil mendapatkan balikan dari getDataForm()
     */
    @Override
    public void onApiSuccess(RestResponse response) {
        if (!isViewAttached() )
            return;

        // if(response != null)
        //setGeneralFormResponse(response.getData(GeneralFormResponse.class));

        BrizziResponse brizziResponse = response.getData(BrizziResponse.class);

        getView().isHideSkeleton(true);
        getView().onSuccessGetHistoryForm(brizziResponse.getHistory());
        getView().onSuccessGetRestResponse(response);
    }


    /**
     * @param card
     * @param amount
     * @param isFormFast
     */
    @Override
    public void getDataInquiry(String card, String amount, boolean isFormFast) {
        if (isViewAttached()) {
            getView().showProgress();

            if (isFormFast)
                inquiryRequest = new FastInquiryBrizziRequest(getFastMenuRequest(), card, amount);
            else
                inquiryRequest = new InquiryDepositBrizziRequest(card, amount);

            String seqNum = getBRImoPrefRepository().getSeqNumber();

            getCompositeDisposable().add(getApiSource().getData(inquiryUrl, inquiryRequest, seqNum)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(new ApiObserver(getView(),seqNum) {
                        @Override
                        protected void onFailureHttp(String errorMessage) {
                            getView().onException(errorMessage);
                            //flag on Load
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            getView().hideProgress();
                            GeneralInquiryResponse responsebrizzi = response.getData(GeneralInquiryResponse.class);
                            getView().onSuccessGetInquiryDeposito(responsebrizzi, konfirmasiUrl, paymentUrl);
                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            getView().hideProgress();
                            if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                getView().onSessionEnd(restResponse.getDesc());
                            else
                                getView().onException(restResponse.getDesc());
                        }
                    }));
        }
    }

//    @Override
//    public void getDataInquiryTopUp(String card, String amount, boolean isFromFast) {
//        if (isViewAttached() && !onLoad) {
//            getView().showProgress();
//
//            //flag on Load
//            onLoad = true;
//
//            if (isFromFast)
//                inquiryRequest = new FastInquiryBrizziRequest(getFastMenuRequest(), card, amount);
//            else
//                inquiryRequest = new InquiryBrizziRequest(card, amount);
//
//            getCompositeDisposable().add(getApiSource().getData(inquiryUrlTopUp, inquiryRequest)
//                    .subscribeOn(Schedulers.io())
//                    .observeOn(AndroidSchedulers.mainThread())
//                    .subscribeWith(new ApiObserver() {
//                        @Override
//                        protected void onFailureHttp(String errorMessage) {
//                            getView().onException(errorMessage);
//                            //flag on Load
//                            onLoad = false;
//                        }
//
//                        @Override
//                        protected void onApiCallSuccess(RestResponse response) {
//                            getView().hideProgress();
//                            onLoad = false;
//
//                            GeneralInquiryResponse responsebrizzi = response.getData(GeneralInquiryResponse.class);
//                            getView().onSuccessInquiryTopUp(responsebrizzi, konfirmasiUrlTopUp, paymentUrl);
//                        }
//
//                        @Override
//                        protected void onApiCallError(RestResponse restResponse) {
//                            onLoad = false;
//                            getView().hideProgress();
//                            if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
//                                getView().onSessionEnd(restResponse.getDesc());
//                            else
//                                getView().onException(restResponse.getDesc());
//                        }
//                    }));
//        }
//    }

//    @Override
//    public void setInquiryUrlTopUp(String urlInformasi) {
//        inquiryUrlTopUp = urlInformasi;
//    }
//
//    @Override
//    public void setKonfirmasiUrlTopUp(String urlInformasi) {
//        konfirmasiUrlTopUp = urlInformasi;
//    }


}