package id.co.bri.brimo.presenters.transaksisaya.belumdibayar

import id.co.bri.brimo.contract.IPresenter.transaksisaya.belumdibayar.IPendingTransactionPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.transaksisaya.belumdibayar.IPendingTransactionView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.config.AppConfig
import id.co.bri.brimo.domain.config.Constant

import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.InquiryBusObjectRequest
import id.co.bri.brimo.models.apimodel.request.InquiryKaiObjectRequest
import id.co.bri.brimo.models.apimodel.request.lifestyle.LifestylePatternRequest
import id.co.bri.brimo.models.apimodel.request.lifestyle.RequestMoliga
import id.co.bri.brimo.models.apimodel.request.lifestyle.shopping.ConfirmationMobelanjaRequest
import id.co.bri.brimo.models.apimodel.request.transaksisaya.belumdibayar.SearchBelumDibayarRequest
import id.co.bri.brimo.models.apimodel.request.travel.TravelFlightRequest
import id.co.bri.brimo.models.apimodel.request.travel.TravelKcicRequest
import id.co.bri.brimo.models.apimodel.response.*
import id.co.bri.brimo.models.apimodel.response.lifestyle.ConfirmationLifestyleResponse
import id.co.bri.brimo.models.apimodel.response.lifestyle.ekspedisi.KonfirmasiWebviewEkspedisiResponse
import id.co.bri.brimo.models.apimodel.response.lifestyle.ekspedisi.NotifikasiMokirimRequest
import id.co.bri.brimo.models.apimodel.response.lifestyle.transaction.belumdibayar.PendingTransactionResponseData
import id.co.bri.brimo.models.apimodel.response.lifestyle.transaction.emptyresult.LifestyleTransactionEmptyResultResponseData
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.disposables.CompositeDisposable
import java.util.concurrent.TimeUnit

class PendingTransactionPresenter<V>(
    schedulerProvider: SchedulerProvider?,
    compositeDisposable: CompositeDisposable?,
    mBRImoPrefRepository: BRImoPrefSource?,
    apiSource: ApiSource?,
    transaksiPfmSource: TransaksiPfmSource?,
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    mBRImoPrefRepository,
    apiSource,
    transaksiPfmSource
), IPendingTransactionPresenter<V> where V : IMvpView?, V : IPendingTransactionView? {

    var mUrlUndoneTransaction = ""
    var mUrlUndoneTransactionSearch = ""
    var mUrlInquiryTravel = ""
    var mUrlConfirmationKcic = ""
    var mUrlPaymentKcic = ""
    var mUrlInquiryBusShuttle = ""
    private lateinit var mUrlConfirmMobelanja: String
    private lateinit var mUrlPaymentMobelanja: String
    var mUrlConfirmationFlight = ""
    var mUrlPaymentFlight = ""
    private var isSearch = false
    private lateinit var inquiryRequestKai: InquiryKaiObjectRequest
    private lateinit var confirmationRequestKcic: TravelKcicRequest
    private lateinit var inquiryRequestBus: InquiryBusObjectRequest
    private lateinit var confirmMobelanjaRequest: Any
    private lateinit var confirmationRequestFlight: Any
    private var mUrlConfirmationMoliga = ""
    private var mUrlPaymentMoliga = ""
    private var mUrlConfirmMokirim = ""
    private var mUrlPaymentMokirim = ""
    private var mUrlConfirmPattern = ""

    override fun setUrlPendingTransaction(url: String) {
        this.mUrlUndoneTransaction = url
    }

    override fun setUrlPendingTransactionSearch(url: String) {
        this.mUrlUndoneTransactionSearch = url
    }

    override fun setUrlInquiryTravel(url: String) {
        this.mUrlInquiryTravel = url
    }

    override fun setUrlConfirmationKcic(url: String) {
        this.mUrlConfirmationKcic = url
    }

    override fun setUrlPaymentKcic(url: String) {
        this.mUrlPaymentKcic = url
    }

    override fun setUrlInquiryBusShuttle(url: String) {
        this.mUrlInquiryBusShuttle = url
    }

    override fun setUrlConfirmationFlight(url: String) {
        this.mUrlConfirmationFlight = url
    }

    override fun setUrlPaymentFlight(url: String) {
        this.mUrlPaymentFlight = url
    }

    override fun setUrlConfirmationMobelanja(url: String) {
        this.mUrlConfirmMobelanja = url
    }

    override fun setUrlPaymentMobelanja(url: String) {
        this.mUrlPaymentMobelanja = url
    }

    override fun setUrlConfirmationMoliga(url: String) {
        this.mUrlConfirmationMoliga = url
    }

    override fun setUrlPaymentMoliga(url: String) {
        this.mUrlPaymentMoliga = url
    }

    override fun setUrlConfirmationMokirim(urlMokirim: String) {
        this.mUrlConfirmMokirim = urlMokirim
    }

    override fun setUrlPaymentMokirim(urlPayment: String) {
        this.mUrlPaymentMokirim = urlPayment
    }

    override fun setUrlConfirmationPattern(url: String) {
        this.mUrlConfirmPattern = url
    }

    override fun getPendingTransactionResponse(search: Boolean) {
        if (mUrlUndoneTransaction.isEmpty() || !isViewAttached) return

        val seqNum = brImoPrefRepository.seqNumber
        val disposable = apiSource.getDataTanpaRequest(
            mUrlUndoneTransaction,
            seqNum
        ).subscribeOn(schedulerProvider.io())
            .observeOn(schedulerProvider.mainThread())
            .subscribeWith(object : ApiObserver(view, seqNum) {
                override fun onFailureHttp(message: String) {
                    getView()?.onException(message)
                }

                override fun onApiCallSuccess(response: RestResponse?) {
                    when (response?.code) {
                        RestResponse.ResponseCodeEnum.RC_01.value -> {
                            val responseData = response?.getData(
                                LifestyleTransactionEmptyResultResponseData::class.java
                            )
                            responseData?.let { getView()?.onExceptionWithDialog(it) }
                        }
                        else -> {
                            getView()?.apply {
                                val responseData = response?.getData(
                                    PendingTransactionResponseData::class.java
                                )
                                responseData?.let { getView()?.onSuccessPendingTransaction(it, search) }
                            }
                        }
                    }
                }

                override fun onApiCallError(restResponse: RestResponse?) {
                    getView()?.hideProgress()
                    if (restResponse?.code?.equals(RestResponse.ResponseCodeEnum.RC_12.value, ignoreCase = true) == true) {
                        getView()?.onException12(restResponse.desc)
                    } else {
                        getView()?.onException(restResponse?.desc)
                    }
                }
            })
        compositeDisposable.add(disposable)
    }

    override fun getPendingTransactionSearch(
        searchKey: SearchBelumDibayarRequest,
        search: Boolean
    ): Boolean {
        isSearch = false
        if (mUrlUndoneTransactionSearch.isEmpty() || !isViewAttached) return false
        val seqNum = brImoPrefRepository.seqNumber
        val disposable = apiSource.getData(
            mUrlUndoneTransactionSearch,
            searchKey,
            seqNum
        ).subscribeOn(schedulerProvider.io())
            .observeOn(schedulerProvider.mainThread())
            .subscribeWith(object : ApiObserver(view, seqNum) {
                override fun onFailureHttp(message: String) {
                    getView()?.onException(message)
                }

                override fun onApiCallSuccess(response: RestResponse?) {
                    when (response?.code) {
                        RestResponse.ResponseCodeEnum.RC_01.value -> {
                            val responseData = response?.getData(
                                LifestyleTransactionEmptyResultResponseData::class.java
                            )
                            responseData?.let { getView()?.onExceptionWithDialog(it) }
                        }
                        else -> {
                            getView()?.apply {
                                val responseData = response?.getData(
                                    PendingTransactionResponseData::class.java
                                )
                                responseData?.let {
                                    getView()?.onSuccessPendingTransaction(it, search)
                                }
                            }
                        }
                    }
                }

                override fun onApiCallError(restResponse: RestResponse?) {
                    restResponse?.let { response ->
                        getView()?.apply {
                            when (response.code) {
                                RestResponse.ResponseCodeEnum.RC_SESSION_END.value -> this.onException(response.desc.orEmpty())
                                else -> this.onException(restResponse.desc.orEmpty())
                            }
                        }
                    }
                }
            })
        isSearch = true
        compositeDisposable.add(disposable)
        return isSearch
    }

    override fun getInquiryTravelKai(bookingId: String, bookingRefnum: String) {
        if (mUrlInquiryTravel.isEmpty() || !isViewAttached) {
            return
        }

        inquiryRequestKai = InquiryKaiObjectRequest(bookingId, bookingRefnum)
        val seqNum = brImoPrefRepository.seqNumber

        view?.showProgress()
        compositeDisposable
            .add(
                apiSource.getData(mUrlInquiryTravel, inquiryRequestKai, seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(schedulerProvider.io())
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView()?.hideProgress()
                            getView()?.onException(errorMessage)
                            onLoad = false
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            getView()?.hideProgress()
                            val inquiryTrainResponse = response.getData(
                                InquiryTrainResponse::class.java
                            )
                            getView()?.onSuccessInquiryKai(inquiryTrainResponse)
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            onLoad = false
                            getView()?.apply {
                                hideProgress()
                                when (restResponse.code) {
                                    RestResponse.ResponseCodeEnum.RC_12.value -> this.onException12(restResponse.desc)
                                    else -> this.onException(restResponse.desc.orEmpty())
                                }
                            }
                        }
                    })
            )
    }

    override fun getConfirmationKcic(
        brivaNumber: String?,
        corpCode: String?,
        quantityTicket: String?,
        expirateDate: String?,
        arrivalDate: String?,
        invoiceNumber: String?
    ) {
        view?.showProgress()
        if (mUrlConfirmationKcic.isEmpty() || !isViewAttached) {
            return
        }
        val seqNum = brImoPrefRepository.seqNumber
        confirmationRequestKcic = TravelKcicRequest(
            brivaNumber.orEmpty(),
            corpCode.orEmpty(),
            quantityTicket.orEmpty(),
            expirateDate.orEmpty(),
            arrivalDate.orEmpty(),
            invoiceNumber.orEmpty()
        )

        compositeDisposable.add(
            apiSource.getData(mUrlConfirmationKcic, confirmationRequestKcic, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView()?.hideProgress()
                        getView()?.onException(errorMessage)
                        onLoad = false
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView()?.hideProgress()
                        val inquiryBrivaRevampResponse = response.getData(
                            InquiryBrivaRevampResponse::class.java
                        )
                        getView()?.onSuccessConfirmationkcic(
                            inquiryBrivaRevampResponse,
                            mUrlPaymentKcic
                        )
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        onLoad = false
                        getView()?.apply {
                            hideProgress()
                            when (restResponse.code) {
                                RestResponse.ResponseCodeEnum.RC_12.value -> this.onException12(restResponse.desc)
                                RestResponse.ResponseCodeEnum.RC_94.value -> this.onException94(restResponse.desc)
                                else -> this.onException(restResponse.desc.orEmpty())
                            }
                        }
                    }
                })
        )
    }

    override fun getInquiryTravelBusShuttle(bookingRefnum: String) {
        if (mUrlInquiryBusShuttle.isEmpty() || !isViewAttached) {
            return
        }

        val inquiryBusRequest = InquiryBusObjectRequest(bookingRefnum, null)
        inquiryRequestBus = inquiryBusRequest

        val seqNum = brImoPrefRepository.seqNumber

        view?.showProgress()
        compositeDisposable
            .add(
                apiSource.getData(mUrlInquiryBusShuttle, inquiryRequestBus, seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(schedulerProvider.io())
                    .observeOn(schedulerProvider.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView()?.hideProgress()
                            getView()?.onException(errorMessage)
                            onLoad = false
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            getView()?.hideProgress()
                            val inquiryBusResponse = response.getData(
                                InquiryBusResponse::class.java
                            )
                            getView()?.onSuccessInquiry(inquiryBusResponse)
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            onLoad = false
                            getView()?.apply {
                                hideProgress()
                                when (restResponse.code) {
                                    RestResponse.ResponseCodeEnum.RC_12.value -> this.onException12(restResponse.desc)
                                    else -> this.onException(restResponse.desc.orEmpty())
                                }
                            }
                        }
                    })
            )
    }

    override fun getConfirmationTravelFlight(bookingId: String) {
        view!!.showProgress()

        if (mUrlConfirmationFlight.isEmpty() || !isViewAttached) {
            return
        }

        val seqNum = brImoPrefRepository.seqNumber

        confirmationRequestFlight = TravelFlightRequest(bookingId)

        compositeDisposable.add(
            apiSource.getData(mUrlConfirmationFlight, confirmationRequestFlight, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(schedulerProvider.single())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView()?.hideProgress()
                        getView()?.onException(errorMessage)
                        onLoad = false
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView()?.hideProgress()
                        val inquiryBrivaRevampResponse = response.getData(
                            InquiryBrivaRevampResponse::class.java
                        )
                        getView()?.onSuccessConfirmationFlight(
                            inquiryBrivaRevampResponse,
                            mUrlPaymentFlight
                        )
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        onLoad = false
                        getView()?.hideProgress()
                        if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_12.value, ignoreCase = true)) {
                            getView()?.onException12(restResponse.desc)
                        } else {
                            getView()?.onException(restResponse.desc)
                        }
                    }
                })
        )
    }

    override fun getConfirmationMobelanja(orderNumber: String) {
        view?.showProgress()

        if (mUrlConfirmMobelanja.isEmpty() || !isViewAttached) return

        val seqNum = brImoPrefRepository.seqNumber

        confirmMobelanjaRequest = ConfirmationMobelanjaRequest(orderNumber)

        compositeDisposable.add(
            apiSource.getData(mUrlConfirmMobelanja, confirmMobelanjaRequest, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView()?.hideProgress()
                        getView()?.onException(errorMessage)
                        onLoad = false
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView()?.hideProgress()
                        val inquiryBrivaRevampResponse = response.getData(
                            InquiryBrivaRevampResponse::class.java
                        )
                        getView()?.onSuccessGetConfirmationMobelanja(
                            inquiryBrivaRevampResponse,
                            mUrlPaymentMobelanja
                        )
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        onLoad = false
                        getView()?.hideProgress()
                        if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_12.value, ignoreCase = true)) {
                            getView()?.onException12(restResponse.desc)
                        } else {
                            getView()?.onException(restResponse.desc)
                        }
                    }
                })
        )
    }

    override fun getConfirmationMoliga(requestMoliga: RequestMoliga) {
        view?.showProgress()

        if (mUrlConfirmationMoliga.isEmpty() || !isViewAttached) return

        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable.add(
            apiSource.getData(mUrlConfirmationMoliga, requestMoliga, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView()?.hideProgress()
                        getView()?.onException(errorMessage)
                        onLoad = false
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView()?.hideProgress()
                        val inquiryBrivaRevampResponse = response.getData(
                            InquiryBrivaRevampResponse::class.java
                        )
                        getView()?.onSuccessGetConfirmationMoliga(
                            inquiryBrivaRevampResponse,
                            mUrlPaymentMoliga
                        )
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        onLoad = false
                        getView()?.hideProgress()
                        if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_12.value, ignoreCase = true)) {
                            getView()?.onException12(restResponse.desc)
                        } else {
                            getView()?.onException(restResponse.desc)
                        }
                    }
                })
        )
    }


    override fun getConfirmationMokirim(notifikasiMokirimRequest: NotifikasiMokirimRequest) {

        view?.showProgress()

        if (mUrlConfirmMokirim.isEmpty() || !isViewAttached) return

        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable.add(
            apiSource.getData(mUrlConfirmMokirim, notifikasiMokirimRequest, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView()?.hideProgress()
                        getView()?.onException(errorMessage)
                        onLoad = false
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView()?.hideProgress()
                        val konfirmasiWebviewEkspedisiResponse = response.getData(
                            KonfirmasiWebviewEkspedisiResponse::class.java
                        )
                        getView()?.onSuccessGetConfirmationMokirim(
                            konfirmasiWebviewEkspedisiResponse,
                            mUrlPaymentMokirim
                        )
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        onLoad = false
                        getView()?.hideProgress()
                        if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_12.value, ignoreCase = true)) {
                            getView()?.onException12(restResponse.desc)
                        } else {
                            getView()?.onException(restResponse.desc)
                        }
                    }
                })
        )
    }

    override fun getConfirmationPattern(confirmationLifestyleRequest: LifestylePatternRequest) {
        view?.showProgress()

        if (mUrlConfirmPattern.isEmpty() || !isViewAttached) return

        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable.add(
            apiSource.getData(mUrlConfirmPattern, confirmationLifestyleRequest, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView()?.hideProgress()
                        getView()?.onException(errorMessage)
                        onLoad = false
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView()?.hideProgress()
                        val confirmationLifestyleResponse = response.getData(
                            ConfirmationLifestyleResponse::class.java
                        )
                        getView()?.onSuccessGetConfirmationPattern(
                            confirmationLifestyleResponse
                        )
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        onLoad = false
                        getView()?.hideProgress()
                        when(restResponse.code) {
                            Constant.RE06 -> view.onException06(restResponse.getData(
                                ExceptionResponse::class.java))
                            Constant.RE99 -> getView()?.onException99(restResponse.desc)
                            else -> getView()?.onException(restResponse.desc)
                        }
                    }
                })
        )
    }

}