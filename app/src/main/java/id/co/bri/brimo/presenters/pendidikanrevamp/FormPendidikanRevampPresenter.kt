package id.co.bri.brimo.presenters.pendidikanrevamp

import id.co.bri.brimo.contract.IPresenter.pendidikanrevamp.IFormPendidikanRevampPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.base.IBaseFormRevampView
import id.co.bri.brimo.contract.IView.pendidikanrevamp.IFormPendidikanRevampView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.config.AppConfig
import id.co.bri.brimo.domain.config.enumconfig.JourneyType
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.InquiryPendidikanRequest
import id.co.bri.brimo.models.apimodel.request.UpdateSavedRequest
import id.co.bri.brimo.models.apimodel.response.InquiryBrivaRevampResponse
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.SavedResponse
import id.co.bri.brimo.presenters.base.BaseFormRevampPresenter
import io.reactivex.disposables.CompositeDisposable
import java.util.concurrent.TimeUnit

class FormPendidikanRevampPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBRImoPrefRepository: BRImoPrefSource,
    apiSource: ApiSource,
    transaksiPfmSource: TransaksiPfmSource
) : BaseFormRevampPresenter<V>(
    schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource
),
    IFormPendidikanRevampPresenter<V> where V : IMvpView, V : IBaseFormRevampView, V : IFormPendidikanRevampView {


    override fun setUpdateItem(url: String, savedResponse: SavedResponse, position: Int, type: Int, journeyType: JourneyType?) {
        if (url.isEmpty() || !isViewAttached || onLoad) return

        onLoad = true

        val str1 = savedResponse.value.split("\\|".toRegex()).dropLastWhile { it.isEmpty() }.toTypedArray()
        val saveId = str1[0]

        val updateSavedRequest = UpdateSavedRequest(saveId, "")
        val seqNum = brImoPrefRepository.seqNumber
        view.showProgress()

        compositeDisposable.add(apiSource.getData(url, updateSavedRequest, seqNum)
            .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
            .subscribeOn(schedulerProvider.io())
            .observeOn(schedulerProvider.mainThread())
            .subscribeWith(object : ApiObserver(view, seqNum) {
                override fun onFailureHttp(errorMessage: String) {
                    onLoad = false
                    getView().hideProgress()
                    getView().onException(errorMessage)
                }

                override fun onApiCallSuccess(response: RestResponse) {
                    onLoad = false
                    getView().hideProgress()
                    getView().onSuccessUpdate(savedResponse, position, type)
                }

                override fun onApiCallError(restResponse: RestResponse) {
                    onApiError(restResponse)
                }
            })
        )
    }

    override fun getDataInquiry(inquiryPendidikanRequest: InquiryPendidikanRequest) {
        if (mUrlInquiry.isEmpty() && !isViewAttached) return

        view.showProgress()

        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable.add(apiSource.getData(mUrlInquiry, inquiryPendidikanRequest, seqNum)
            .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
            .subscribeOn(schedulerProvider.io())
            .observeOn(schedulerProvider.mainThread())
            .subscribeWith(object : ApiObserver(view, seqNum) {
                override fun onFailureHttp(errorMessage: String) {
                    getView().hideProgress()
                    getView().onException(errorMessage)
                }

                override fun onApiCallSuccess(response: RestResponse) {
                    //TO-DO onSuccess
                    getView().hideProgress()
                    val responsebriva = response.getData(
                        InquiryBrivaRevampResponse::class.java
                    )
                    getView().onSuccessGetInquiry(
                        responsebriva,
                        mUrlPayment
                    )
                }

                override fun onApiCallError(restResponse: RestResponse) {
                    getView().hideProgress()
                    if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value))
                        getView().onSessionEnd(restResponse.desc)
                    else if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_58.value))
                        getView().onException58(restResponse.desc)
                    else if(restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_59.value))
                        getView().onException59(restResponse.desc)
                    else if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_88_BILL_ALREADY_PAID.value))
                        getView().onException88(restResponse.desc)
                    else getView().onException(restResponse.desc)
                }
            })
        )
    }


}