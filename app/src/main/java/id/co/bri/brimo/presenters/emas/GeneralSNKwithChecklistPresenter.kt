package id.co.bri.brimo.presenters.emas

import id.co.bri.brimo.contract.IPresenter.emas.IGeneralSNKwithChecklistPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.emas.IGeneralSNKwithChecklistView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserverKonfirmasi
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimo.data.repository.category.CategoryPfmSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.emas.PaymentOnboardEmasRequest
import id.co.bri.brimo.models.apimodel.response.ReceiptRevampResponse
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.emas.ReceiptGagalEmasResponse
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.disposables.Disposable

class GeneralSNKwithChecklistPresenter <V>(schedulerProvider: SchedulerProvider?,
                                           compositeDisposable: CompositeDisposable?,
                                           mBRImoPrefRepository: BRImoPrefSource?, apiSource: ApiSource?,
                                           categoryPfmSource: CategoryPfmSource?,
                                           transaksiPfmSource: TransaksiPfmSource?,
                                           anggaranPfmSource: AnggaranPfmSource?) : MvpPresenter<V>(
    schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource
),
        IGeneralSNKwithChecklistPresenter<V> where V : IMvpView?, V : IGeneralSNKwithChecklistView {

    lateinit var mUrlPayment : String
    private var responseEmas= ReceiptRevampResponse()
    private var responseEmasFailed= ReceiptGagalEmasResponse()

    override fun getDataPaymentEmas(request: PaymentOnboardEmasRequest) {
        if (isViewAttached) {
            //set flag Loading
            view.showProgress()
            val seqNum = brImoPrefRepository.seqNumber
            val disposable: Disposable =
                    apiSource.getData(mUrlPayment, request, seqNum) //function(param)
                            .subscribeOn(schedulerProvider.io())
                            .observeOn(schedulerProvider.mainThread())
                            .subscribeWith(object : ApiObserverKonfirmasi(view, seqNum) {
                                override fun onFailureHttp(errorMessage: String) {
                                    getView().onException(errorMessage)
                                }

                                override fun onApiCallSuccess(response: RestResponse) {
                                    getView().hideProgress()

                                    if (response.code.equals(RestResponse.ResponseCodeEnum.RC_SUCCESS.value, ignoreCase = true)){
                                        responseEmas = response.getData(ReceiptRevampResponse::class.java)
                                        getView().onSuccessGetPaymentEmas(responseEmas)
                                    }

                                }

                                override fun onApiCallError(restResponse: RestResponse) {
                                    getView().hideProgress()
                                    if (restResponse.code.equals(
                                                    RestResponse.ResponseCodeEnum.RC_SESSION_END.value,
                                                    ignoreCase = true)
                                    ) getView().onSessionEnd(restResponse.desc)
                                    else if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.value, ignoreCase = true))
                                        getView().onException93(restResponse.desc)
                                    else if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_01.value, ignoreCase = true))
                                        getView().onException01(restResponse.desc)
                                    else if (restResponse.code == RestResponse.ResponseCodeEnum.RC_99.value)
                                        getView().onException99(restResponse.desc)
                                    else if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_FL.value, ignoreCase = true)){
                                        responseEmasFailed = restResponse.getData(ReceiptGagalEmasResponse::class.java)
                                        getView().onSuccessGetPaymentEmasFailed(responseEmasFailed)
                                    }
                                    else getView().onException(restResponse.desc)
                                }
                            })
            compositeDisposable.add(disposable)
        }
    }

    override fun setUrlPayment(urlPayment: String) {
        this.mUrlPayment = urlPayment
    }
}