package id.co.bri.brimo.presenters.esbn;

import java.util.concurrent.TimeUnit;

import id.co.bri.brimo.contract.IPresenter.esbn.IDashboardESBNPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.esbn.IDashboardESBNView;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.config.AppConfig;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.apimodel.request.esbn.GetRedeemRequest;
import id.co.bri.brimo.models.apimodel.request.esbn.RegisKemenkeuRequest;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.models.apimodel.response.esbn.DashboardDataSbnResponse;
import id.co.bri.brimo.models.apimodel.response.esbn.DataPortofolioSbnResponse;
import id.co.bri.brimo.models.apimodel.response.esbn.EsbnExceptionResponse;
import id.co.bri.brimo.models.apimodel.response.esbn.earlyredeem.DetailSbnPencairanResponse;
import id.co.bri.brimo.presenters.MvpPresenter;
import io.reactivex.disposables.CompositeDisposable;

public class DashboardESBNPresenter<V extends IMvpView & IDashboardESBNView> extends MvpPresenter<V> implements IDashboardESBNPresenter<V> {
    private String url;
    private String url_porto;
    private String url_kemenkeu;
    private String url_getredeem;
    private String url_r5;

    public DashboardESBNPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void setUrl(String url) {
        this.url = url;
    }

    @Override
    public void setUrlGetPortofolio(String url) {
        this.url_porto = url;
    }

    @Override
    public void setUrlKemenkeu(String url) {
        this.url_kemenkeu = url;
    }

    @Override
    public void setUrlGetDetail(String url) {
        this.url_getredeem = url;
    }

    @Override
    public void setUrlR5(String url) {
        this.url_r5 = url;
    }


    /**
     * RC 00 -> Normal case
     * RC R -> Abnormal Case
     */
    @Override
    public void getData() {
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        getCompositeDisposable().add(
                getApiSource().getData(url, "", seqNum)
                        .subscribeOn(getSchedulerProvider().single())
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .observeOn(getSchedulerProvider().mainThread())
                        .subscribeWith(new ApiObserver(getView(),seqNum) {
                            @Override
                            protected void onFailureHttp(String type) {
                                getView().onException(type);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SUCCESS.getValue())) {
                                    DashboardDataSbnResponse dataResponse = response.getData(DashboardDataSbnResponse.class);
                                    getView().onSuccesGedtDataDashboardSbn(dataResponse);
                                    if (dataResponse.getIsPending() == 0){
                                        getView().hideBadges(dataResponse.getIsPending());
                                    }
                                    if (dataResponse.getStatusPembelian() != 1) {
                                        getView().closeSkeleton();
                                    }
                                }else if (response.getCode().contains("R1")) {
                                    DashboardDataSbnResponse dataResponse = response.getData(DashboardDataSbnResponse.class);
                                    getView().onSuccessR1(dataResponse);
                                    if (dataResponse.getStatusPembelian() != 1) {
                                        getView().closeSkeleton();
                                    }
                                    if (dataResponse.getIsPending() == 0){
                                        getView().hideBadges(dataResponse.getIsPending());
                                    }
                                }
                                else if (response.getCode().contains("R2")) {
                                    DashboardDataSbnResponse dataResponse = response.getData(DashboardDataSbnResponse.class);
                                    getView().showDialog(dataResponse, response.getCode());
                                    if (dataResponse.getStatusPembelian() != 1) {
                                        getView().closeSkeleton();
                                    }
                                    if (dataResponse.getIsPending() == 0){
                                        getView().hideBadges(dataResponse.getIsPending());
                                    }
                                }
                                else if (response.getCode().contains("R3")) {
                                    DashboardDataSbnResponse dataResponse = response.getData(DashboardDataSbnResponse.class);
                                    getView().showDialog(dataResponse, response.getCode());
                                    if (dataResponse.getStatusPembelian() != 1) {
                                        getView().closeSkeleton();
                                    }
                                    if (dataResponse.getIsPending() == 0){
                                        getView().hideBadges(dataResponse.getIsPending());
                                    }
                                }
                                else if (response.getCode().contains("R4")) {
                                    DashboardDataSbnResponse dataResponse = response.getData(DashboardDataSbnResponse.class);
                                    getView().onSuccessR4(dataResponse);
                                    if (dataResponse.getStatusPembelian() != 1) {
                                        getView().closeSkeleton();
                                    }
                                    if (dataResponse.getIsPending() == 0){
                                        getView().hideBadges(dataResponse.getIsPending());
                                    }
                                }
                                else if (response.getCode().contains("R5")) {
                                    DashboardDataSbnResponse dataResponse = response.getData(DashboardDataSbnResponse.class);
                                    getView().onSuccessR5(dataResponse);
                                    if (dataResponse.getStatusPembelian() != 1) {
                                        getView().closeSkeleton();
                                    }
                                    if (dataResponse.getIsPending() == 0){
                                        getView().hideBadges(dataResponse.getIsPending());
                                    }
                                }
                                else if (response.getCode().contains("R6")) {
                                    DashboardDataSbnResponse dataResponse = response.getData(DashboardDataSbnResponse.class);
                                    getView().onSuccessR6(dataResponse);
                                    if (dataResponse.getStatusPembelian() != 1) {
                                        getView().closeSkeleton();
                                    }
                                    if (dataResponse.getIsPending() == 0){
                                        getView().hideBadges(dataResponse.getIsPending());
                                    }
                                }
                                else if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_02.getValue())) {
                                    EsbnExceptionResponse esbnExceptionResponse = response.getData(EsbnExceptionResponse.class);
                                    getView().onException02(esbnExceptionResponse);
                                    getView().hideProgress();
                                }
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue())){
                                    getView().onSessionEnd(restResponse.getDesc());
                                }else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_12.getValue())){
                                    getView().onException12(restResponse.getDesc());
                                }
                                else {
                                    getView().onException(restResponse.getDesc());
                                }
                            }

                            @Override
                            public void onComplete() {

                            }
                        })
        );
    }

    @Override
    public void getDataPortofolio() {
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        getCompositeDisposable().add(
                getApiSource().getData(url_porto, "", seqNum)
                        .subscribeOn(getSchedulerProvider().single())
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .observeOn(getSchedulerProvider().mainThread())
                        .subscribeWith(new ApiObserver(getView(),seqNum) {
                            @Override
                            protected void onFailureHttp(String type) {
                                getView().onException(type);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {

                                if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SUCCESS.getValue())) {
                                    DataPortofolioSbnResponse dataResponse = response.getData(DataPortofolioSbnResponse.class);
                                    getView().onSuccesGetPorto(dataResponse);
                                }else if (response.getCode().contains(RestResponse.ResponseCodeEnum.RC_01.getValue())) {
                                    EsbnExceptionResponse esbnExceptionResponse = response.getData(EsbnExceptionResponse.class);
                                    getView().onException01(esbnExceptionResponse);
                                    getView().hideProgress();
                                }
                                else if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_02.getValue())) {
                                    EsbnExceptionResponse esbnExceptionResponse = response.getData(EsbnExceptionResponse.class);
                                    getView().onException02(esbnExceptionResponse);
                                    getView().hideProgress();
                                }

                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                    getView().onSessionEnd(restResponse.getDesc());
                                else
                                    getView().onException(restResponse.getDesc());
                            }
                        })
        );
    }

    @Override
    public void getDataDetail(GetRedeemRequest redeemRequest) {
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        getView().showProgress();
        getCompositeDisposable().add(
                getApiSource().getData(url_getredeem, redeemRequest, seqNum)
                        .subscribeOn(getSchedulerProvider().single())
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .observeOn(getSchedulerProvider().mainThread())
                        .subscribeWith(new ApiObserver(getView(),seqNum) {
                            @Override
                            protected void onFailureHttp(String type) {
                                getView().hideProgress();
                                getView().onException(type);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                getView().hideProgress();
                                DetailSbnPencairanResponse dialogData = response.getData(DetailSbnPencairanResponse.class);
                                getView().onSuccessGetRedeeem(dialogData);

                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                    getView().onSessionEnd(restResponse.getDesc());
                                else
                                    getView().onException(restResponse.getDesc());
                            }
                        })
        );
    }

    @Override
    public void getKemenkeuData(RegisKemenkeuRequest regisKemenkeuRequest) {
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        getView().showProgress();
        getCompositeDisposable().add(
                getApiSource().getData(url_kemenkeu, regisKemenkeuRequest, seqNum)
                        .subscribeOn(getSchedulerProvider().single())
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .observeOn(getSchedulerProvider().mainThread())
                        .subscribeWith(new ApiObserver(getView(),seqNum) {
                            @Override
                            protected void onFailureHttp(String type) {
                                getView().hideProgress();
                                getView().onException(type);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                getView().hideProgress();
                                DashboardDataSbnResponse dialogData = response.getData(DashboardDataSbnResponse.class);
                                getView().onSuccesKemekeu(dialogData);

                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                    getView().onSessionEnd(restResponse.getDesc());
                                else
                                    getView().onException(restResponse.getDesc());
                            }
                        })
        );
    }

    @Override
    public void saveBackGeneral(int status) {
        getBRImoPrefRepository().saveBackGeneral(status);
    }

    @Override
    public void start() {
        super.start();
        getData();
    }

    @Override
    public void stop() {
        super.stop();
    }
}