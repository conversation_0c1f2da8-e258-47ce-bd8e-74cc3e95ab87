package id.co.bri.brimo.presenters.dashboard;

import android.util.Log;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import androidx.annotation.NonNull;
import id.co.bri.brimo.contract.IPresenter.dashboard.IDashboardActivityPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.dashboard.IDashboardActivityView;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.lifestyle.MenuLifestyleSource;
import id.co.bri.brimo.data.repository.rate.RateSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.config.AppConfig;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.converter.MapperHelper;
import id.co.bri.brimo.domain.converter.RoomDateConverter;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.domain.helpers.calendar.CalendarHelper;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.apimodel.request.voiceassistant.AktivasiVoiceAssistantRequest;
import id.co.bri.brimo.models.apimodel.request.splitbill.NotifikasiSplitBillRequest;
import id.co.bri.brimo.models.apimodel.response.RateUsResponse;
import id.co.bri.brimo.models.NotifikasiModel;
import id.co.bri.brimo.models.apimodel.request.DetailPromoRequest;
import id.co.bri.brimo.models.apimodel.response.GeneralInquiryResponse;
import id.co.bri.brimo.models.apimodel.response.PromoResponse;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.models.apimodel.response.lifestyle.ConfirmationLifestyleResponse;
import id.co.bri.brimo.models.apimodel.response.lifestyle.DashboardLifestyleMenuResponse;
import id.co.bri.brimo.models.apimodel.response.lifestyle.FeatureDataView;
import id.co.bri.brimo.models.apimodel.response.lifestyle.ekspedisi.KonfirmasiWebviewEkspedisiResponse;
import id.co.bri.brimo.models.apimodel.response.lifestyle.ekspedisi.NotifikasiMokirimRequest;
import id.co.bri.brimo.models.apimodel.response.voiceassistant.GetAktivasiVoiceAssistantResponse;
import id.co.bri.brimo.models.daomodel.RateUs;
import id.co.bri.brimo.presenters.MvpPresenter;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.disposables.Disposable;
import io.reactivex.observers.DisposableCompletableObserver;
import io.reactivex.schedulers.Schedulers;
import io.reactivex.observers.DisposableSingleObserver;

public class DashboardActivityPresenter<V extends IMvpView & IDashboardActivityView> extends MvpPresenter<V> implements IDashboardActivityPresenter<V> {

    private static final String TAG = "DashboardActivityPresen";

    private String urlLogout = "";
    protected String inquiryUrl;
    protected String urlPromo = "";

    protected boolean isLoadingItem = false;
    protected DetailPromoRequest detailPromoRequest;
    private String urlRateData = "";
    private String urlGetAktivasiVoiceAssistant;

    private RateSource rateSource;

    // Rating
    private String date;
    private long rateStat = 0;

    //Lifestyle
    private MenuLifestyleSource menuLifestyleSource;
    private DashboardLifestyleMenuResponse lifestyleMenuResponse;

    public DashboardActivityPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable,
                                      BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource,
                                      CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource,
                                      AnggaranPfmSource anggaranPfmSource, RateSource rateSource,
                                      MenuLifestyleSource menuLifestyleSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
        this.rateSource = rateSource;
        this.menuLifestyleSource = menuLifestyleSource;
    }


    @Override
    public void logOut() {
        if (urlLogout == null || !isViewAttached()) {
            if (!GeneralHelper.isProd()) {
                Log.d(TAG, "logOut: urlInformasi empty");
            }
            return;
        }
        //initiate param with getter from view

        getView().showProgress();

        String seqNum = getBRImoPrefRepository().getSeqNumber();

        Disposable disposable = getApiSource().onLogout(urlLogout, seqNum)//function(param)
                .subscribeOn(getSchedulerProvider().io())
                .observeOn(getSchedulerProvider().mainThread())
                .subscribeWith(new ApiObserver(getView(), seqNum) {

                    @Override
                    protected void onFailureHttp(String errorMessage) {

                        getView().hideProgress();
                        getView().onException(errorMessage);
                    }

                    @Override
                    protected void onApiCallSuccess(RestResponse response) {

                        updateLoginFlag(false);

                        getView().hideProgress();
                        getView().onLogout(response.getDesc());

                    }

                    @Override
                    protected void onApiCallError(RestResponse restResponse) {
                        updateLoginFlag(false);

                        getView().hideProgress();
                        getView().onLogout("");
                    }
                });

        getCompositeDisposable().add(disposable);

    }


    @Override
    public void setUrlLogout(String urlLogout) {
        this.urlLogout = urlLogout;
    }

    @Override
    public void getDataInquiry(String reqSeq, String typeInquiry, String urlKonfirmasi, String urlPayment, String title, String typeInquiryRevamp) {
        if (inquiryUrl == null || !isViewAttached()) {
            return;
        }

        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        getCompositeDisposable()
                .add(getApiSource().getDataNotif(inquiryUrl, reqSeq, seqNum)
                        .subscribeOn(getSchedulerProvider().io())
                        .observeOn(getSchedulerProvider().mainThread())
                        .subscribeWith(new ApiObserver(getView(), seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                getView().hideProgress();
                                if (typeInquiryRevamp != null && !typeInquiryRevamp.isEmpty()) {
                                    getView().onSuccessGetInquiryRevamp(typeInquiry, response, urlKonfirmasi, urlPayment, title, typeInquiryRevamp, reqSeq);
                                } else if (typeInquiry.equalsIgnoreCase(Constant.PaymentReminderTypeInquiry.listrikPrepaid)) {
                                    getView().onSuccessGetInquiryRevamp(typeInquiry, response, urlKonfirmasi, urlPayment, title, typeInquiryRevamp, reqSeq);
                                } else {
                                    GeneralInquiryResponse generalInquiryResponse = response.getData(GeneralInquiryResponse.class);
                                    getView().onSuccessGetInquiry(typeInquiry, generalInquiryResponse, urlKonfirmasi, urlPayment, title);
                                }
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                onApiError(restResponse);
                            }
                        }));
    }

    /**
     * Method yang digunakan untuk set Url yang akan digunakan di Halaman FORM
     *
     * @param url
     */
    @Override
    public void setInquiryUrl(String url) {
        inquiryUrl = url;
    }

    @Override
    public void getDataProduct(String promoId, String blastId, String titleButton, NotifikasiModel notifikasiModel) {
        if (isLoadingItem) {
            return;
        } else {
            if (urlPromo != null || isViewAttached()) {
                getView().showProgress();
                isLoadingItem = true;
                detailPromoRequest = new DetailPromoRequest(promoId);
                String seqNum = getBRImoPrefRepository().getSeqNumber();
                getCompositeDisposable()
                        .add(getApiSource().getData(urlPromo, detailPromoRequest, seqNum)
                                .subscribeOn(Schedulers.io())
                                .observeOn(AndroidSchedulers.mainThread())
                                .subscribeWith(new ApiObserver(getView(), seqNum) {


                                    @Override
                                    protected void onFailureHttp(String errorMessage) {
                                        getView().hideProgress();
                                        getView().onException(errorMessage);
                                        isLoadingItem = false;
                                    }

                                    @Override
                                    protected void onApiCallSuccess(RestResponse response) {
                                        //TO-DO onSuccess
                                        getView().hideProgress();
                                        PromoResponse promoResponse = response.getData(PromoResponse.class);
                                        getView().onSuccessGetDetailPromoProduct(promoResponse, promoId, blastId, titleButton, notifikasiModel);
                                        isLoadingItem = false;
                                        Log.d("Chuck", restResponse.getDesc());
                                    }

                                    @Override
                                    protected void onApiCallError(RestResponse restResponse) {
                                        getView().hideProgress();
                                        isLoadingItem = false;
                                        if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                            getView().onSessionEnd(restResponse.getDesc());
                                        else
                                            getView().onException(restResponse.getDesc());

                                        Log.d("Chuck", restResponse.getDesc());

                                    }
                                }));

            }
        }
    }

    @Override
    public void setPromoUrl(String url) {
        this.urlPromo = url;
    }

    @Override
    public void setUrlRateData(String urlRateData) {
        this.urlRateData = urlRateData;
    }

    /**
     * Metod untuk pengecekan di service db
     */
    @Override
    public void onRateData() {
        if (isViewAttached()) {
            String seqNum = getBRImoPrefRepository().getSeqNumber();
            String username = getBRImoPrefRepository().getUsername();

            getCompositeDisposable().add(getApiSource().getData(urlRateData, username, seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(new ApiObserver(getView(), seqNum) {
                        @Override
                        protected void onFailureHttp(String errorMessage) {
                            getView().onException(errorMessage);
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            RateUsResponse rateRes = response.getData(RateUsResponse.class);

                            if (Boolean.TRUE.equals(rateRes.getStatus())) {
                                rateStat = Long.parseLong(rateRes.getData().getRating());
                                if (rateRes.getData().getModifiedDate() != null)
                                    date = rateRes.getData().getModifiedDate().substring(0, 10);
                                else
                                    date = rateRes.getData().getCreatedDate().substring(0, 10);

                                RateUs rateUs = new RateUs(1, rateStat, RoomDateConverter.stringToDate(date), 0);
                                onSaveRateDevice(rateUs);
                            } else {
                                getView().showNewRateFragment(rateStat);
                            }
                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                getView().onSessionEnd(restResponse.getDesc());
                        }
                    }));
        }
    }

    @Override
    public void onSaveRateDevice(RateUs rateUs) {
        if (isViewAttached()) {
            getCompositeDisposable().add(rateSource.saveRateUs(rateUs)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(new DisposableSingleObserver<Long>() {
                        @Override
                        public void onSuccess(@NonNull Long aLong) {
                            if (!date.equalsIgnoreCase(CalendarHelper.getDateNow()))
                                getView().showNewRateFragment(rateStat);
                        }

                        @Override
                        public void onError(@NonNull Throwable e) {
                            // Do nothing
                        }
                    }));
        }
    }

    /**
     * Method untuk pengecekan ke db local
     */
    @Override
    public void CheckRate() {
        if (isViewAttached()) {
            getCompositeDisposable().add(rateSource.getRateAll()
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(new DisposableSingleObserver<RateUs>() {
                        @Override
                        public void onSuccess(@NonNull RateUs rateUs) {
                            onCheckDataLocal(rateUs);
                        }

                        @Override
                        public void onError(@NonNull Throwable e) {
                            onRateData();
                        }
                    }));
        }
    }

    protected void onCheckDataLocal(RateUs rateUs) {
        if (RoomDateConverter.dateToString(rateUs.getRateDate()).equalsIgnoreCase(CalendarHelper.getDateNow())) {
            int count = (int) rateUs.getRateCounter() + 1;
            if (count <= AppConfig.RATE_COUNT_TRX) {
                onUpdateRateCounter(count, rateUs.getId());
            } else {
                if (rateUs.getRateStat() < AppConfig.RATE_THHOLD) {
                    getView().showRateFragment(rateUs);
                }
            }
        } else {
            if (rateUs.getRateStat() < AppConfig.RATE_THHOLD) {
                getView().showRateFragment(rateUs);
            }
        }
    }

    @Override
    public void onUpdateRateCounter(long rateCounter, long id) {
        if (isViewAttached() && id != 0) {
            getCompositeDisposable().add(rateSource.updateRateCounter(rateCounter, id)
                    .subscribeOn(getSchedulerProvider().io())
                    .observeOn(getSchedulerProvider().mainThread())
                    .subscribe(integer -> {
                    }));
        }
    }

    @Override
    public void getTartunNds() {

    }

    @Override
    public void getDataNotif(String reqContent, String urlData, String typeNotif) {
        if (urlData == null || !isViewAttached()) {
            return;
        }

        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        getCompositeDisposable().add(getApiSource().getDataNotif(urlData, reqContent, seqNum).subscribeOn(getSchedulerProvider().io()).observeOn(getSchedulerProvider().mainThread()).subscribeWith(new ApiObserver(getView(), seqNum) {

            @Override
            protected void onFailureHttp(String errorMessage) {
                getView().onException(errorMessage);
            }

            @Override
            protected void onApiCallSuccess(RestResponse response) {
                getView().hideProgress();
                getView().onSuccessGetDataNotif(response, typeNotif);
            }

            @Override
            protected void onApiCallError(RestResponse restResponse) {
                onApiError(restResponse);
            }
        }));
    }

    @Override
    public void getDataDashboardLifestyleMenu(@NonNull String url) {
        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();

        getCompositeDisposable().add(getApiSource().getDataTanpaRequest(url, seqNum)
                .subscribeOn(getSchedulerProvider().io())
                .observeOn(getSchedulerProvider().mainThread())
                .subscribeWith(new ApiObserver(getView(), seqNum) {

                    @Override
                    protected void onFailureHttp(String errorMessage) {
                        getView().hideProgress();
                        getView().onException(errorMessage);
                    }

                    @Override
                    protected void onApiCallSuccess(RestResponse response) {
                        getView().hideProgress();
                        lifestyleMenuResponse =
                                response.getData(DashboardLifestyleMenuResponse.class);

                        List<FeatureDataView> featureDataLocals = new ArrayList<>();
                        for (int menuData = 0; menuData < lifestyleMenuResponse.getMenuDataView().size(); menuData++) {
                            featureDataLocals.addAll(lifestyleMenuResponse.getMenuDataView().get(menuData).getFeature());
                        }

                        if (response.getCode().equals(RestResponse.ResponseCodeEnum.RC_SUCCESS.getValue())) {
                            if (Objects.equals(getBRImoPrefRepository().getDateMenuUpdate(), "")) {
                                saveMenuLifestyle(featureDataLocals);
                            } else if (Boolean.TRUE == CalendarHelper.compareDate(
                                    getBRImoPrefRepository().getDateMenuUpdate(),
                                    CalendarHelper.stringDateTimeToddMMyyyy(lifestyleMenuResponse.getUpdatedDate()))
                            ) {
                                saveMenuLifestyle(featureDataLocals);
                            } else {
                                getView().onSuccessDashboardLifestyleMenu(lifestyleMenuResponse);
                            }
                        }
                    }

                    @Override
                    protected void onApiCallError(RestResponse restResponse) {
                        getView().hideProgress();
                    }
                }));
    }


    private void saveMenuLifestyle(List<FeatureDataView> featureDataViews) {
        getCompositeDisposable().add(menuLifestyleSource
                .insertMenuLifestyle(MapperHelper.dashboardLifestyleModelConverter(featureDataViews))
                .subscribeOn(getSchedulerProvider().io())
                .observeOn(getSchedulerProvider().mainThread())
                .subscribeWith(new DisposableCompletableObserver() {
                    @Override
                    public void onComplete() {
                        getBRImoPrefRepository().saveDateMenuUpdate(
                                CalendarHelper.stringDateTimeToddMMyyyy(
                                        lifestyleMenuResponse.getUpdatedDate()
                                )
                        );
                        getView().onSuccessDashboardLifestyleMenu(lifestyleMenuResponse);
                    }

                    @Override
                    public void onError(@NonNull Throwable e) {/*do nothing*/}
                }));
    }


    @Override
    public void getConfirmationMokirim(NotifikasiMokirimRequest notifikasiMokirimRequest, String urlPaymentMokirim) {
        if (inquiryUrl == null || !isViewAttached()) {
            return;
        }

        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        getCompositeDisposable()
                .add(getApiSource().getData(inquiryUrl, notifikasiMokirimRequest, seqNum)
                        .subscribeOn(getSchedulerProvider().io())
                        .observeOn(getSchedulerProvider().mainThread())
                        .subscribeWith(new ApiObserver(getView(),seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                getView().hideProgress();

                                KonfirmasiWebviewEkspedisiResponse konfirmasiWebviewEkspedisiResponse =
                                        response.getData(KonfirmasiWebviewEkspedisiResponse.class);

                                getView().onSuccessGetConfirmationMokirim(
                                        konfirmasiWebviewEkspedisiResponse,
                                        urlPaymentMokirim
                                );
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                onApiError(restResponse);
                            }
                        }));
    }

    @Override
    public void getConfirmationSplitBill(NotifikasiSplitBillRequest notifikasiSplitBillRequest, String urlPayment) {
        if (inquiryUrl == null || !isViewAttached()) {
            return;
        }

        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        getCompositeDisposable()
                .add(getApiSource().getData(inquiryUrl, notifikasiSplitBillRequest, seqNum)
                        .subscribeOn(getSchedulerProvider().io())
                        .observeOn(getSchedulerProvider().mainThread())
                        .subscribeWith(new ApiObserver(getView(),seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                getView().hideProgress();

                                ConfirmationLifestyleResponse confirmationLifestyleResponse =
                                        response.getData(ConfirmationLifestyleResponse.class);

                                getView().onSuccessGetConfirmationSplitBill(
                                        confirmationLifestyleResponse,
                                        urlPayment
                                );
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                if (restResponse.getCode().equalsIgnoreCase(Constant.RE12)) {
                                    getView().onException(restResponse.getDesc());
                                } else {
                                    onApiError(restResponse);
                                }
                            }
                        }));
    }

    // untuk sementara di komen menunggu konfirmasi fitur ini release kapan
//    @Override
//    public void setUrlGetAktivasiVoiceAssistant(String url) {
//        this.urlGetAktivasiVoiceAssistant = url;
//    }

//    @Override
//    public void getAktivasiVoiceAssistant() {
//        if (isViewAttached()) {
//            String seqNum = getBRImoPrefRepository().getSeqNumber();
//
//            AktivasiVoiceAssistantRequest aktivasiVoiceAssistantRequest = new AktivasiVoiceAssistantRequest(getBRImoPrefRepository().getUsername(), null, null);
//
//            getView().showProgress();
//            getCompositeDisposable().add(
//                    getApiSource().getData(urlGetAktivasiVoiceAssistant, aktivasiVoiceAssistantRequest, seqNum)
//                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
//                            .subscribeOn(Schedulers.io())
//                            .observeOn(AndroidSchedulers.mainThread())
//                            .subscribeWith(new ApiObserver(getView(), seqNum) {
//                                @Override
//                                protected void onFailureHttp(String type) {
//                                    getView().hideProgress();
//                                    getView().onException(type);
//                                }
//
//                                @Override
//                                protected void onApiCallSuccess(RestResponse response) {
//                                    getView().hideProgress();
//                                    GetAktivasiVoiceAssistantResponse getAktivasiVoiceAssistantResponse = response.getData(GetAktivasiVoiceAssistantResponse.class);
//                                    getBRImoPrefRepository().saveAktivasiVoiceAssistant(getAktivasiVoiceAssistantResponse.getStatus());
//                                }
//
//                                @Override
//                                protected void onApiCallError(RestResponse restResponse) {
//                                    getView().hideProgress();
//                                    getView().onException(restResponse.getDesc());
//                                }
//                            })
//            );
//        }
//    }
}