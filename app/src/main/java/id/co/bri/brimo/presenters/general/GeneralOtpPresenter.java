package id.co.bri.brimo.presenters.general;

import android.util.Log;

import com.google.gson.Gson;

import java.util.concurrent.TimeUnit;

import javax.inject.Inject;

import id.co.bri.brimo.contract.IPresenter.general.IGeneralOtpPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.general.IGeneralOtpView;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.config.AppConfig;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.apimodel.request.CodeRequest;
import id.co.bri.brimo.models.apimodel.request.GeneralResendOtpRequest;
import id.co.bri.brimo.models.apimodel.request.GeneralValidateOtpRequest;
import id.co.bri.brimo.models.apimodel.response.GeneralOtpResponse;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.presenters.MvpPresenter;
import io.reactivex.disposables.CompositeDisposable;

public class GeneralOtpPresenter<V extends IMvpView & IGeneralOtpView> extends MvpPresenter<V> implements IGeneralOtpPresenter<V> {

    private String urlValidationOtp = "";
    private String urlResendOtp = "";

    private boolean isKeyReq = false;

    @Inject
    public GeneralOtpPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }


    @Override
    public void submitOtp(String otp, String refNumber) {
        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        Object generalValidateOtpRequest;
        if (isKeyReq)
            generalValidateOtpRequest = new CodeRequest(otp, refNumber);
        else
            generalValidateOtpRequest = new GeneralValidateOtpRequest(otp, refNumber);

        if (isViewAttached()) {
            getCompositeDisposable().add(
                    getApiSource().getData(urlValidationOtp, generalValidateOtpRequest, seqNum)
                            .subscribeOn(getSchedulerProvider().single())
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .observeOn(getSchedulerProvider().mainThread())
                            .subscribeWith(new ApiObserver(getView(), seqNum) {
                                @Override
                                protected void onFailureHttp(String type) {
                                    getView().hideProgress();
                                    getView().onException(type);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    getView().hideProgress();
                                    getView().deleteInputOtp();
                                    GeneralOtpResponse generalOtpResponse = response.getData(GeneralOtpResponse.class);
                                    getView().onSuccess(generalOtpResponse);
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().hideProgress();
                                    getView().deleteInputOtp();
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue())) {
                                        getView().onSessionEnd(restResponse.getDesc());
                                    } else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.getValue())) {
                                        getView().onException93(restResponse.getDesc());
                                    } else
                                        getView().onException(restResponse.getDesc());
                                }
                            })
            );
        }

    }

    @Override
    public void setUrlValidationOtp(String url) {
        this.urlValidationOtp = url;
    }

    @Override
    public void setUrlResendOtp(String urlResendOtp) {
        this.urlResendOtp = urlResendOtp;
    }

    @Override
    public void resendOtp(String refNumber) {
        if (getView() == null) {
            return;
        }
        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        GeneralResendOtpRequest generalResendOtpRequest = new GeneralResendOtpRequest(refNumber);

        if (isViewAttached()) {
            getCompositeDisposable().add(
                    getApiSource().getData(urlResendOtp, generalResendOtpRequest, seqNum)
                            .subscribeOn(getSchedulerProvider().single())
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .observeOn(getSchedulerProvider().mainThread())
                            .subscribeWith(new ApiObserver(getView(), seqNum) {
                                @Override
                                protected void onFailureHttp(String type) {
                                    getView().hideProgress();
                                    getView().onException(type);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    getView().hideProgress();
                                    GeneralOtpResponse generalOtpResponse = response.getData(GeneralOtpResponse.class);
                                    getView().onResendSuccess(generalOtpResponse);
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().hideProgress();
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue())) {
                                        getView().onSessionEnd(restResponse.getDesc());
                                    } else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.getValue())) {
                                        getView().onException93(restResponse.getDesc());
                                    } else
                                        getView().onException(restResponse.getDesc());
                                }
                            })
            );
        }
    }

    @Override
    public void isKeyRequest(boolean isKeyReq) {
        this.isKeyReq = isKeyReq;
    }

}