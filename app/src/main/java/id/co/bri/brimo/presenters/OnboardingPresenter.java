package id.co.bri.brimo.presenters;

import java.util.ArrayList;
import java.util.List;

import id.co.bri.brimo.contract.IPresenter.dashboard.IDasboardIBPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.dashboard.IDashboardIBView;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.fastmenu.FastMenuSource;
import id.co.bri.brimo.data.repository.fastmenudefault.FastMenuDefaultSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.OnboardingItemModel;

import id.co.bri.brimo.models.apimodel.request.dompetdigitalrevamp.InquiryDompetDigitalRequest;
import id.co.bri.brimo.models.apimodel.response.EwalletProductListResponse;
import id.co.bri.brimo.models.apimodel.request.ResendSmsRequest;
import io.reactivex.disposables.CompositeDisposable;

public class OnboardingPresenter<V extends IMvpView & IDashboardIBView> extends MvpPresenter<V> implements IDasboardIBPresenter<V> {


    public OnboardingPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource, FastMenuSource fastMenuSource, FastMenuDefaultSource fastMenuDefaultSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void getOnboarding() {
        List<OnboardingItemModel> onboardingItemModelList = new ArrayList<>();
        onboardingItemModelList.add(new OnboardingItemModel("Halo, Agung", "Bulan Juni telah berlalu, sekarang sudah bulan Juli"));
        onboardingItemModelList.add(new OnboardingItemModel("Halo, Budi", "Bulan Juli telah berlalu, sekarang sudah bulan Agustus"));
        onboardingItemModelList.add(new OnboardingItemModel("Halo, Cinta", "Bulan Agustus telah berlalu, sekarang sudah bulan September"));
        onboardingItemModelList.add(new OnboardingItemModel("Halo, Desi", "Bulan September telah berlalu, sekarang sudah bulan Oktober"));
        getView().onSuccessGetOnboarding(onboardingItemModelList);
    }

    @Override
    public void getSaldo() {

    }

    @Override
    public void updateTokenFirebase() {

    }

    public void getMenuDefault() {

    }

    @Override
    public void getPromoFeatured() {

    }

    @Override
    public void setFormUrl(String url) {

    }

    @Override
    public void setSubscribeUrl(String subscribeUrl) {

    }

    @Override
    public void setKategoriItemUrl(String kategoriItemUrl) {

    }

    @Override
    public void setDetailItemUrl(String detailItemUrl) {

    }

    @Override
    public void setLatestBlastNotifUrl(String latestBlastUrl) {

    }

    @Override
    public void setNotificationUnreads(String notifUnreadUrl) {

    }

    @Override
    public void setUrlReadNotif(String urlRead) {

    }

    @Override
    public void setUrlIbbiz(String urlIbbiz) {

    }

    @Override
    public void setUrlInfoSaldoHold(String urlInfoSaldoHold) {

    }

    @Override
    public void setSafetyMode(String urlSafety) {

    }

    @Override
    public void setUrlPFMSummary(String url) {

    }

    @Override
    public void setUrlPusatBantuanSafety(String urlPusatBantuanSafety) {

    }

    @Override
    public void getEwalletBindingList() {

    }

    @Override
    public void getEwalletBalance(EwalletProductListResponse ewalletProductListResponse) {

    }

    @Override
    public void getTimerSafetyMode() {

    }

    @Override
    public void setUnbindingEwallet(String type) {

    }

    @Override
    public void getDataInquiryDompetDigital(InquiryDompetDigitalRequest request) {

    }

    @Override
    public void setInquiryUrlDompetDigital(String url) {

    }

    @Override
    public void setUrlEwalletBindingList(String url) {

    }

    @Override
    public void setUrlEwalletBalance(String url) {

    }

    @Override
    public void setUrlEwalletUnBinding(String url) {

    }

    @Override
    public void setUrlMagicLinkRegis(String urlMagicLinkRegis) {

    }

    @Override
    public void setUrlOtpEmailRegis(String urlOtpEmailRegis) { }

    public void getTotalAmount() {

    }

    @Override
    public void getTermCondition() {

    }

    @Override
    public void getBubbleShowNewOnboarding() {

    }

    @Override
    public void updateIsNewFalse(int menuId, int isNewFlag) {

    }

    @Override
    public void updateKategori(int kategoriId) {

    }

    @Override
    public void getInfoSaldoHold() {

    }

    @Override
    public void getBrimoPrefSaldoHold() {

    }

    @Override
    public void saveBrimoPrefSaldoHold() {

    }

    @Override
    public void getKategoriItem(String namaPromo) {

    }

    @Override
    public void getDetailItem(String id) {

    }

    @Override
    public void getLatestBlastNotif() {

    }

    @Override
    public void getNotificationUnreads() {

    }

    @Override
    public void getReadNotifFastMenu(String blastId) {

    }

    @Override
    public void getDataIbbiz() {

    }

    @Override
    public void getDataMagicLinkRegis() {

    }

    @Override
    public void getDataOtpEmailRegis() {

    }

    @Override
    public void getPusatBantuanSafety(String id) {

    }

    @Override
    public ResendSmsRequest sendRequestCheckPoinRegis() {
        return null;
    }

    @Override
    public void setUrlSaldo(String urlSaldo) {

    }


    @Override
    public void onCloseButtonClicked() {
        getView().onCloseOnboarding();
    }
}