package id.co.bri.brimo.presenters.tariktunai;

import android.util.Log;

import id.co.bri.brimo.contract.IPresenter.tariktunai.IFormTarikPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.tariktunai.IFormTarikView;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.apimodel.request.CofirmationTarikRequest;
import id.co.bri.brimo.models.apimodel.response.KonfirmasiTarikTunaiResponse;
import id.co.bri.brimo.models.apimodel.response.PaymentTarikResponse;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.models.apimodel.response.TarikTunaiResponse;
import id.co.bri.brimo.presenters.MvpPresenter;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.disposables.Disposable;
import io.reactivex.schedulers.Schedulers;

public class FormTarikPresenter<V extends IMvpView & IFormTarikView> extends MvpPresenter<V> implements IFormTarikPresenter<V> {

    private static final String TAG = "FormTarikPresenter";
    protected String urlConfirmation;
    protected String inquiryUrl;
    protected String formUrl;

    public FormTarikPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void onClickSubmit() {
        if (getView() != null) {
            if (validateInput()) {


            }
        }

    }

    @Override
    public void getFormTarik() {
        if (formUrl == null || !isViewAttached()) {
            return;
        }

        if (onLoad)
            return;

        onLoad = true;
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        getCompositeDisposable()
                .add(getApiSource().getDataForm(formUrl, seqNum)
                        .subscribeOn(getSchedulerProvider().io())
                        .observeOn(getSchedulerProvider().mainThread())
                        .subscribeWith(new ApiObserver(getView(), seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                onLoad = false;
                                getView().hideProgress();
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                //TO-DO onSuccess
                                onLoad = false;
                                getView().hideProgress();
                                if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SUCCESS.getValue())) {
                                    TarikTunaiResponse tarikTunaiResponse = response.getData(TarikTunaiResponse.class);
                                    getView().onSuccessGetTarikTunai(tarikTunaiResponse);
                                } else if (response.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_01.getValue())) {
                                    PaymentTarikResponse paymentTarikResponse = response.getData(PaymentTarikResponse.class);
                                    getView().onTokenActive(paymentTarikResponse);
                                }
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                onLoad = false;
                                getView().hideProgress();

                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_12.getValue()))
                                    getView().onException12(restResponse.getDesc());
                                else
                                    getView().onException(restResponse.getDesc());
                            }
                        }));

    }

    @Override
    public void getDataConfirmation(String accountNum, String merchant, String amount, String merchantCode) {

        if (urlConfirmation == null || !isViewAttached()) {
            Log.d(TAG, "getData: form null");
            return;
        }

        if (onLoad)
            return;

        //show loading
        getView().showProgress();
        onLoad = true;

        CofirmationTarikRequest confirmationRequest = new CofirmationTarikRequest(accountNum, merchant, amount, merchantCode);
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        Disposable disposable = getApiSource().getData(urlConfirmation, confirmationRequest, seqNum)//function(param)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(new ApiObserver(getView(), seqNum) {

                    @Override
                    protected void onFailureHttp(String errorMessage) {
                        onLoad = false;
                        getView().hideProgress();
                        getView().onException(errorMessage);
                    }

                    @Override
                    protected void onApiCallSuccess(RestResponse response) {
                        onLoad = false;
                        getView().hideProgress();

                        KonfirmasiTarikTunaiResponse brivaResponse = response.getData(KonfirmasiTarikTunaiResponse.class);
                        getView().onSuccessGetConfirmation(brivaResponse);

                    }

                    @Override
                    protected void onApiCallError(RestResponse restResponse) {
                        onLoad = false;
                        getView().hideProgress();

                        if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_12.getValue()))
                            getView().onException12(restResponse.getDesc());
                        else
                            getView().onException(restResponse.getDesc());

                    }
                });

        getCompositeDisposable().add(disposable);

    }


    @Override
    public void setUrlConfirmation(String urlConfirmation) {
        this.urlConfirmation = urlConfirmation;
    }

    protected boolean validateInput() {
        boolean valid = true;

        try {

            if (getView().getAmount() < 1) {
                getView().showInputError("Harga tidak valid");
                getView().hideProgress();
                return false;
            }
        } catch (Exception e) {
            getView().showInputError(e.getMessage());
            return false;
        }

        return valid;
    }


    @Override
    public void start() {
        super.start();
        this.getDefaultSaldo();
    }


    protected void getDefaultSaldo() {
        double saldo = 0.0;
        String saldoText = getBRImoPrefRepository().getSaldoRekeningUtama();
        if (!saldoText.equals("")) {
            saldo = Double.valueOf(saldoText);
        }

        String akunDefault = getBRImoPrefRepository().getAccountDefault();
        String saldoString = getBRImoPrefRepository().getSaldoRekeningUtamaString();
        boolean saldoHold = getBRImoPrefRepository().getSaldoHold();

        getView().setDefaultSaldo(saldo, saldoString, akunDefault, saldoHold);
    }

    @Override
    public void setInquiryUrl(String url) {
        inquiryUrl = url;
    }


    @Override
    public void setFormUrl(String formUrl) {
        this.formUrl = formUrl;
    }
}