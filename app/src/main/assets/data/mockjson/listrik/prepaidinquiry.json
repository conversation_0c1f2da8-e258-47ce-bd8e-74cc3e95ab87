{"code": "00", "description": "<PERSON><PERSON><PERSON>", "data": {"account_list": [{"account": "***************", "account_string": "0230 0100 1674 308", "name": "Infinite", "currency": "Rp", "card_number": "5221XXXXXXXX7777", "card_number_string": "5221 XXXX XXXX 7777", "product_type": "Simpedes", "account_type": "SA", "sc_code": "SU", "default": 0, "alias": "", "minimum_balance": 25000, "limit": -1, "limit_string": "", "image_name": "Simpedes-Umum.png", "image_path": "http://*************:4010/brimo-asset/account_logo/Simpedes-Umum.png"}], "billing_detail": [{"name": "NOMOR METER", "value": "***********", "style": ""}, {"name": "NAMA", "value": "TMPXXXXXXXXXXXXXXXXXXXXXX", "style": ""}, {"name": "TARIF/DAYA", "value": "B2 / 7700 VA", "style": ""}], "billing_detail_open": [{"list_type": "image", "icon_name": "pln_token_listrik", "icon_path": "http://*************:4010/brimo-asset/pln/pln_token_listrik.png", "title": "TMPXXXXXXXXXXXXXXXXXXXXXX", "subtitle": "<PERSON><PERSON>", "description": "************ - B2 / 7700 VA"}], "billing_amount": [{"name": "TOTAL BAYAR", "value": "Rp3.000", "style": ""}], "billing_amount_detail": [{"name": "NOMINAL", "value": "Rp0", "style": ""}, {"name": "ADMIN BANK", "value": "Rp3.000", "style": ""}], "open_payment": false, "is_billing": true, "minimum_payment": false, "row_data_show": 3, "amount_option": [{"text": "10.000", "value": "10000"}, {"text": "20.000", "value": "20000"}, {"text": "50.000", "value": "50000"}, {"text": "100.000", "value": "100000"}, {"text": "200.000", "value": "200000"}, {"text": "500.000", "value": "500000"}, {"text": "1.000.000", "value": "1000000"}, {"text": "5.000.000", "value": "5000000"}, {"text": "10.000.000", "value": "********"}, {"text": "50.000.000", "value": "********"}], "saved": "", "amount": 0, "amount_string": "Rp0", "minimum_amount": 0, "minimum_amount_string": "Rp0", "admin_fee": 3000, "admin_fee_string": "Rp3.000", "pay_amount": 3000, "pay_amount_string": "Rp3.000", "minimum_transaction": 1, "minimum_transaction_string": "Rp1", "reference_number": "************", "cashback": false}}