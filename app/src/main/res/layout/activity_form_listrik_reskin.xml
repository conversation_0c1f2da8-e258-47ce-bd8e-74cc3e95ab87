<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <id.co.bri.brimo.ui.widget.BaseScreenLayout
            android:id="@+id/bsl_content"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toTopOf="@+id/ll_footer"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/bg_input_black_100_brimo_ns"
                android:minHeight="64dp"
                android:orientation="horizontal"
                android:paddingHorizontal="21dp">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:gravity="center_vertical|left"
                    android:orientation="vertical"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toLeftOf="@id/btn_token_check"
                    app:layout_constraintTop_toTopOf="parent">

                    <TextView
                        style="@style/BodySmallText.Medium.Grey"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Token Listrik"
                        android:textColor="@color/text_black_default_ns"
                        android:textFontWeight="600"
                        android:textSize="@dimen/size_text_14sp" />

                    <TextView
                        style="@style/BodySmallText.Medium.Grey"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Cetak ulang token listrik disini"
                        android:textColor="@color/text_disabled_default_ns"
                        android:textFontWeight="600"
                        android:textSize="@dimen/size_text_12sp" />
                </LinearLayout>

                <Button
                    android:id="@+id/btn_token_check"
                    style="@style/CustomButtonStyle"
                    android:layout_width="103dp"
                    android:layout_height="32dp"
                    android:background="@drawable/rounded_button_ns"
                    android:enabled="true"
                    android:text="Cetak Token"
                    android:textAllCaps="false"
                    android:textColor="@color/selector_text_color_button_primary_ns"
                    android:textSize="12sp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <LinearLayout
                android:id="@+id/ll_listrik_type"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="21dp"
                android:background="@drawable/bg_input_black_100_brimo_ns"
                android:gravity="center_vertical|center_horizontal"
                android:minHeight="64dp"
                android:orientation="horizontal"
                android:paddingHorizontal="21dp">

                <RelativeLayout
                    android:id="@+id/icon_container"
                    android:layout_width="@dimen/size_32dp"
                    android:layout_height="@dimen/size_32dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="0dp"
                    android:layout_marginEnd="16dp"
                    android:background="@drawable/round_icon_ns"
                    android:visibility="gone">

                    <ImageView
                        android:id="@+id/iv_area"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_alignParentTop="true"
                        android:src="@drawable/ikon_wilayah" />
                </RelativeLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical"
                        tools:ignore="RtlSymmetry">

                        <TextView
                            android:id="@+id/region_textview"
                            style="@style/BodySmallText.Medium.Grey"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="4dp"
                            android:text="Jenis Listrik"
                            android:textColor="@color/text_black_default_ns"
                            android:textSize="@dimen/size_12sp"
                            android:visibility="gone" />


                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center_vertical"
                            app:boxStrokeWidth="0dp"
                            app:hintEnabled="false">

                            <EditText
                                android:id="@+id/et_jenis_listrik"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center_vertical"
                                android:background="@android:color/transparent"
                                android:focusable="false"
                                android:hint="Jenis Listrik"
                                android:inputType="none|textNoSuggestions"
                                android:paddingVertical="0dp"
                                android:paddingStart="0dp"
                                android:paddingEnd="0dp"
                                android:textColorHint="@color/text_black_default_ns" />
                        </com.google.android.material.textfield.TextInputLayout>

                    </LinearLayout>

                    <ImageView
                        android:layout_width="@dimen/size_16dp"
                        android:layout_height="@dimen/size_16dp"
                        android:layout_gravity="center_vertical"
                        android:src="@drawable/ic_arrow_down_black" />

                </LinearLayout>
            </LinearLayout>

            <id.co.bri.brimo.ui.widget.input_til.BaseInputView
                android:id="@+id/biv_no_pelanggan"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="21dp"
                app:hintText="Nomor Meter / ID Pelanggan" />

            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/size_1dp"
                android:layout_marginVertical="@dimen/size_21dp"
                android:background="#E9EEF6" />

            <!-- Tab Section for Favorit and Riwayat -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <!-- Search bar -->
                <LinearLayout
                    android:id="@+id/searchview_briva"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="24dp"
                    android:background="@drawable/bg_input_black_100_brimo_ns_rounded"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingHorizontal="16dp"
                    android:paddingVertical="12dp"
                    android:theme="@style/AppSearchViewSmall">

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_marginEnd="8dp"
                        android:src="@drawable/ic_search_new_skin_20" />


                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:text="@string/cari_pelanggan_atau_layanan"
                        android:textSize="16dp" />
                </LinearLayout>

                <!-- Tab Layout -->
                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="24dp"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/tab_favorit"
                        android:layout_width="77dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="8dp"
                        android:layout_weight="1"
                        android:background="@drawable/rounded_button_soft_ns"
                        android:clickable="true"
                        android:focusable="true"
                        android:paddingVertical="12dp"
                        android:text="Favorit"
                        android:textAlignment="center"
                        android:textColor="@color/text_brand_primary_ns" />

                    <TextView
                        android:id="@+id/tab_riwayat"
                        android:layout_width="77dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="8dp"
                        android:layout_weight="1"
                        android:background="@drawable/bg_input_black_100_brimo_ns_rounded"
                        android:clickable="true"
                        android:focusable="true"
                        android:paddingVertical="12dp"
                        android:text="Riwayat"
                        android:textAlignment="center"
                        android:textColor="@color/black" />


                </LinearLayout>

                <!-- Favorit Content (Saved List) -->
                <LinearLayout
                    android:id="@+id/content_favorit"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:visibility="visible">

                    <LinearLayout
                        android:id="@+id/ll_add_to_fav_list"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="@dimen/_24sdp"
                        android:background="@drawable/rounded_dashed_border"
                        android:gravity="center_vertical"
                        android:padding="@dimen/_16sdp">

                        <ImageView
                            android:layout_width="@dimen/_36sdp"
                            android:layout_height="@dimen/_36sdp"
                            android:layout_marginEnd="@dimen/_12sdp"
                            android:background="@drawable/shadow_rounded"
                            android:contentDescription="@null"
                            android:elevation="8dp"
                            android:src="@drawable/ic_mystar"
                            android:translationZ="8dp" />

                        <TextView
                            style="@style/HeadingSemibold"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/add_to_favorites_list" />

                        <ImageView
                            android:layout_width="@dimen/_24sdp"
                            android:layout_height="@dimen/_24sdp"
                            android:contentDescription="@null"
                            android:src="@drawable/icon_add_circle" />

                    </LinearLayout>

                    <!-- Saved List RecyclerView -->
                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rv_daftar_favorit"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layoutAnimation="@anim/layout_animation_fade_in"
                        android:visibility="gone" />

                    <!-- No Saved Data Message -->
                    <LinearLayout
                        android:id="@+id/ll_no_data_saved"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:padding="32dp"
                        android:visibility="visible">

                        <ImageView
                            android:layout_width="120dp"
                            android:layout_height="120dp"
                            android:layout_marginBottom="16dp"
                            android:src="@drawable/empty_box_3d" />

                        <TextView
                            android:id="@+id/no_data_saved"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="8dp"
                            android:text="Belum Ada Daftar Favorit"
                            android:textColor="@color/black"
                            android:textStyle="bold" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:gravity="center"
                            android:text="Yuk, tambah favorit biar transaksi berikutnya lebih cepat."
                            android:textSize="12sp" />

                        <LinearLayout
                            android:id="@+id/ll_add_saved_list"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="23dp"
                            android:background="@drawable/rounded_button_border_blue_ns"
                            android:clickable="true"
                            android:focusable="true"
                            android:gravity="center"
                            android:orientation="horizontal"
                            android:paddingHorizontal="16dp"
                            android:paddingVertical="10dp"
                            android:visibility="visible">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:gravity="center"
                                android:text="Tambah Favorit"
                                android:textColor="@color/primary_ns_main"
                                android:textSize="12sp" />
                        </LinearLayout>
                    </LinearLayout>
                </LinearLayout>

                <!-- Riwayat Content (History) -->
                <LinearLayout
                    android:id="@+id/content_riwayat"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:visibility="gone">

                    <!-- History RecyclerView -->
                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rv_riwayat"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layoutAnimation="@anim/layout_animation_fade_in"
                        android:visibility="visible" />

                    <!-- No History Message -->
                    <LinearLayout
                        android:id="@+id/ll_no_history"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:padding="32dp"
                        android:visibility="gone">

                        <ImageView
                            android:layout_width="120dp"
                            android:layout_height="120dp"
                            android:layout_marginBottom="16dp"
                            android:src="@drawable/empty_box_3d" />

                        <TextView
                            android:id="@+id/tv_no_history"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:text="Belum ada riwayat transaksi"
                            android:textColor="@color/neutral_light60"
                            android:textSize="14sp" />
                    </LinearLayout>
                </LinearLayout>
            </LinearLayout>

        </id.co.bri.brimo.ui.widget.BaseScreenLayout>

        <!-- Bottom Button -->
        <LinearLayout
            android:id="@+id/ll_footer"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:background="@android:color/white"
            android:orientation="vertical"
            android:padding="16dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent">

            <View
                android:id="@+id/bottom_border"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/size_1dp"
                android:background="@color/border_gray_soft_ns" />

            <Button
                android:id="@+id/btnSubmit"
                style="@style/CustomButtonStyle"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:background="@drawable/rounded_button_ns"
                android:enabled="false"
                android:text="Cek Tagihan"
                android:textAllCaps="false"
                android:textColor="@color/selector_text_color_button_primary_ns"
                android:textSize="16sp" />
        </LinearLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>